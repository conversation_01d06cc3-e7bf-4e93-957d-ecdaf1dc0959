import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/models/parse_result.dart';
import 'package:dreamflow/models/transaction_model.dart';

void main() {
  group('AmbiguityType Validation Tests', () {
    group('AmbiguityType.isValid', () {
      test('should return true for null ambiguity type', () {
        expect(AmbiguityType.isValid(null), isTrue);
      });

      test('should return true for valid ambiguity types', () {
        expect(AmbiguityType.isValid(AmbiguityType.missingAmount), isTrue);
        expect(AmbiguityType.isValid(AmbiguityType.ambiguousAmount), isTrue);
        expect(AmbiguityType.isValid(AmbiguityType.ambiguousType), isTrue);
        expect(AmbiguityType.isValid(AmbiguityType.ambiguousCategory), isTrue);
      });

      test('should return false for invalid ambiguity types', () {
        expect(AmbiguityType.isValid('invalid_type'), isFalse);
        expect(AmbiguityType.isValid('random_string'), isFalse);
        expect(AmbiguityType.isValid(''), isFalse);
      });
    });

    group('ParseResult with ambiguityType validation', () {
      late Transaction testTransaction;

      setUp(() {
        testTransaction = Transaction(
          id: 'test-id',
          amount: 100.0,
          type: TransactionType.expense,
          categoryId: 'test-category',
          date: DateTime.now(),
          description: 'Test transaction',
          tags: [],
          currencyCode: 'USD',
        );
      });

      test('should successfully create ParseResult.success with valid ambiguityType', () {
        expect(
          () => ParseResult.success(
            testTransaction,
            ambiguityType: AmbiguityType.missingAmount,
          ),
          returnsNormally,
        );

        final result = ParseResult.success(
          testTransaction,
          ambiguityType: AmbiguityType.ambiguousAmount,
        );
        expect(result.ambiguityType, equals(AmbiguityType.ambiguousAmount));
      });

      test('should successfully create ParseResult.success with null ambiguityType', () {
        expect(
          () => ParseResult.success(testTransaction),
          returnsNormally,
        );

        final result = ParseResult.success(testTransaction);
        expect(result.ambiguityType, isNull);
      });

      test('should throw error for ParseResult.success with invalid ambiguityType', () {
        expect(
          () => ParseResult.success(
            testTransaction,
            ambiguityType: 'invalid_ambiguity_type',
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('should validate ambiguityType for all ParseResult factory methods', () {
        // Test missingAmount
        expect(
          () => ParseResult.missingAmount(
            testTransaction,
            ambiguityType: AmbiguityType.missingAmount,
          ),
          returnsNormally,
        );

        // Test failed
        expect(
          () => ParseResult.failed(
            testTransaction,
            'Test error',
            ambiguityType: AmbiguityType.ambiguousType,
          ),
          returnsNormally,
        );

        // Test needsCategory
        expect(
          () => ParseResult.needsCategory(
            testTransaction,
            ambiguityType: AmbiguityType.ambiguousCategory,
          ),
          returnsNormally,
        );

        // Test needsType
        expect(
          () => ParseResult.needsType(
            testTransaction,
            ambiguityType: AmbiguityType.ambiguousType,
          ),
          returnsNormally,
        );
      });

      test('should throw error for invalid ambiguityType in any ParseResult method', () {
        expect(
          () => ParseResult.missingAmount(
            testTransaction,
            ambiguityType: 'invalid_type',
          ),
          throwsA(isA<ArgumentError>()),
        );

        expect(
          () => ParseResult.failed(
            testTransaction,
            'Test error',
            ambiguityType: 'invalid_type',
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('AmbiguityType Constants', () {
      test('should have correct constant values', () {
        expect(AmbiguityType.missingAmount, equals('missing_amount'));
        expect(AmbiguityType.ambiguousAmount, equals('ambiguous_amount'));
        expect(AmbiguityType.ambiguousType, equals('ambiguous_type'));
        expect(AmbiguityType.ambiguousCategory, equals('ambiguous_category'));
      });

      test('should have all valid types in validTypes list', () {
        final validTypes = AmbiguityType.validTypes;
        expect(validTypes, contains(AmbiguityType.missingAmount));
        expect(validTypes, contains(AmbiguityType.ambiguousAmount));
        expect(validTypes, contains(AmbiguityType.ambiguousType));
        expect(validTypes, contains(AmbiguityType.ambiguousCategory));
        expect(validTypes.length, equals(4));
      });
    });
  });
}
