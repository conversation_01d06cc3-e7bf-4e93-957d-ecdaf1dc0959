import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/widgets/quick_reply_widget.dart';
import 'package:dreamflow/models/parse_result.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('Amount Confirmation UI Tests', () {
    Widget createTestWidget({required Widget child}) {
      return MaterialApp(
        home: Scaffold(
          body: child,
        ),
      );
    }

    testWidgets('should display amount confirmation message correctly', (WidgetTester tester) async {
      // Create a ParseResult with amount confirmation needed
      final candidateAmounts = [69.0, 100.0];
      final candidateTexts = ['69', '100'];
      final transaction = TestHelpers.createTestTransaction(
        amount: 69.0, // Default to first candidate
        description: 'an com tai lux69 100',
      );
      
      final parseResult = ParseResult.needsAmountConfirmation(
        transaction,
        candidateAmounts,
        candidateTexts,
      );
      
      // Verify the parse result is correct
      expect(parseResult.status, equals(ParseStatus.needsAmountConfirmation));
      expect(parseResult.candidateAmounts, equals(candidateAmounts));
      expect(parseResult.candidateTexts, equals(candidateTexts));
      
      // Create QuickReplyWidget with amount confirmation options
      final widget = QuickReplyWidget(
        replyOptions: [...candidateTexts, 'Cancel'],
        onReplySelected: (selectedOption) {
          // Mock callback for testing
        },
      );
      
      await tester.pumpWidget(createTestWidget(child: widget));
      
      // Verify quick reply buttons are displayed with correct formatting
      expect(find.text('69'), findsOneWidget);
      expect(find.text('100'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      
      // Verify QuickReplyWidget is present
      expect(find.byType(QuickReplyWidget), findsOneWidget);
    });

    testWidgets('should show candidate amounts in correct order', (WidgetTester tester) async {
      // Test with multiple amounts in specific order
      final candidateTexts = ['45', '200', '1.5k'];
      final widget = QuickReplyWidget(
        replyOptions: [...candidateTexts, 'Cancel'],
        onReplySelected: (selectedOption) {
          // Mock callback for testing
        },
      );
      
      await tester.pumpWidget(createTestWidget(child: widget));
      
      // Find all quick reply buttons
      final quickReplyWidget = find.byType(QuickReplyWidget);
      expect(quickReplyWidget, findsOneWidget);
      
      // Verify all candidate amounts are present
      for (final amount in candidateTexts) {
        expect(find.text(amount), findsOneWidget);
      }
      
      // Verify Cancel button is present
      expect(find.text('Cancel'), findsOneWidget);
    });

    testWidgets('should handle user selecting amount from candidates', (WidgetTester tester) async {
      String? selectedOption;
      
      final widget = QuickReplyWidget(
        replyOptions: ['69', '100', 'Cancel'],
        onReplySelected: (option) {
          selectedOption = option;
        },
      );
      
      await tester.pumpWidget(createTestWidget(child: widget));
      
      // Tap on the second amount option
      await tester.tap(find.text('100'));
      await tester.pump();
      
      // Verify the selection was processed
      expect(selectedOption, equals('100'));
      
      // Verify no exceptions occurred
      expect(tester.takeException(), isNull);
    });

    testWidgets('should handle user cancelling amount selection', (WidgetTester tester) async {
      String? selectedOption;
      
      final widget = QuickReplyWidget(
        replyOptions: ['50', '200', 'Cancel'],
        onReplySelected: (option) {
          selectedOption = option;
        },
      );
      
      await tester.pumpWidget(createTestWidget(child: widget));
      
      // Tap on Cancel
      await tester.tap(find.text('Cancel'));
      await tester.pump();
      
      // Verify the cancel was processed
      expect(selectedOption, equals('Cancel'));
      
      // Verify no exceptions occurred
      expect(tester.takeException(), isNull);
    });

    testWidgets('should handle empty options gracefully', (WidgetTester tester) async {
      final widget = QuickReplyWidget(
        replyOptions: [],
        onReplySelected: (option) {
          // Mock callback for testing
        },
      );
      
      await tester.pumpWidget(createTestWidget(child: widget));
      
      // Should render without errors but show no buttons
      expect(find.byType(QuickReplyWidget), findsOneWidget);
      expect(find.byType(InkWell), findsNothing);
    });

    testWidgets('should handle disabled state correctly', (WidgetTester tester) async {
      String? selectedOption;
      
      final widget = QuickReplyWidget(
        replyOptions: ['69', '100', 'Cancel'],
        enabled: false,
        onReplySelected: (option) {
          selectedOption = option;
        },
      );
      
      await tester.pumpWidget(createTestWidget(child: widget));
      
      // Try to tap on an option when disabled
      await tester.tap(find.text('100'));
      await tester.pump();
      
      // Verify the selection was NOT processed
      expect(selectedOption, isNull);
      
      // Verify buttons are still visible but disabled
      expect(find.text('100'), findsOneWidget);
    });

    testWidgets('should display proper styling for amount confirmation', (WidgetTester tester) async {
      final widget = QuickReplyWidget(
        replyOptions: ['69', '100', 'Cancel'],
        onReplySelected: (option) {
          // Mock callback for testing
        },
      );
      
      await tester.pumpWidget(createTestWidget(child: widget));
      
      // Verify all buttons are rendered with proper styling
      final containers = find.byType(Container);
      expect(containers, findsWidgets);
      
      // Verify text widgets are present
      expect(find.text('69'), findsOneWidget);
      expect(find.text('100'), findsOneWidget);
      expect(find.text('Cancel'), findsOneWidget);
      
      // Verify InkWell widgets for interaction
      expect(find.byType(InkWell), findsNWidgets(3));
    });

    testWidgets('should handle large numbers of candidates', (WidgetTester tester) async {
      final largeCandidateList = List.generate(10, (index) => '${(index + 1) * 10}');
      largeCandidateList.add('Cancel');
      
      final widget = QuickReplyWidget(
        replyOptions: largeCandidateList,
        onReplySelected: (option) {
          // Mock callback for testing
        },
      );
      
      await tester.pumpWidget(createTestWidget(child: widget));
      
      // Verify all candidates are displayed
      for (final candidate in largeCandidateList) {
        expect(find.text(candidate), findsOneWidget);
      }
      
      // Verify QuickReplyWidget handles the large list
      expect(find.byType(QuickReplyWidget), findsOneWidget);
    });

    testWidgets('should handle special amount formats', (WidgetTester tester) async {
      final specialFormats = ['1.5k', '2.3m', '0.99', '1,000', 'Cancel'];
      
      final widget = QuickReplyWidget(
        replyOptions: specialFormats,
        onReplySelected: (option) {
          // Mock callback for testing
        },
      );
      
      await tester.pumpWidget(createTestWidget(child: widget));
      
      // Verify all special formats are displayed correctly
      for (final format in specialFormats) {
        expect(find.text(format), findsOneWidget);
      }
    });

    testWidgets('should maintain accessibility features', (WidgetTester tester) async {
      final widget = QuickReplyWidget(
        replyOptions: ['69', '100', 'Cancel'],
        onReplySelected: (option) {
          // Mock callback for testing
        },
      );
      
      await tester.pumpWidget(createTestWidget(child: widget));
      
      // Verify semantic labels are present
      final semanticsNodes = tester.getSemantics(find.byType(QuickReplyWidget));
      expect(semanticsNodes, isNotNull);
      
      // Verify buttons are accessible
      expect(find.byType(InkWell), findsNWidgets(3));
    });
  });
}
