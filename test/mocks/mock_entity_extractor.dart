import 'package:dreamflow/services/parser/entity_extractor_base.dart';

/// Mock implementation of EntityAnnotationBase for testing
class MockEntityAnnotation implements EntityAnnotationBase {
  @override
  final String text;
  
  @override
  final int start;
  
  @override
  final int end;
  
  @override
  final EntityType entityType;

  MockEntityAnnotation({
    required this.text,
    required this.start,
    required this.end,
    required this.entityType,
  });
}

/// Mock implementation of EntityExtractorBase for testing
/// This allows configuring return values and simulating various scenarios
class MockEntityExtractor implements EntityExtractorBase {
  List<EntityAnnotationBase> _mockResults = [];
  bool _shouldThrowError = false;
  bool _isInitialized = true;
  String? _errorMessage;

  /// Set the entities that this mock should return when annotateText is called
  void setMockResults(List<EntityAnnotationBase> results) {
    _mockResults = results;
  }

  /// Configure the mock to throw an error when annotateText is called
  void setShouldThrowError(bool shouldThrow, [String? errorMessage]) {
    _shouldThrowError = shouldThrow;
    _errorMessage = errorMessage;
  }

  /// Configure the initialization state
  void setInitialized(bool initialized) {
    _isInitialized = initialized;
  }

  /// Reset the mock to default state
  void reset() {
    _mockResults = [];
    _shouldThrowError = false;
    _isInitialized = true;
    _errorMessage = null;
  }

  @override
  Future<List<EntityAnnotationBase>> annotateText(String text) async {
    if (_shouldThrowError) {
      throw Exception(_errorMessage ?? 'Mock entity extraction error');
    }
    return _mockResults;
  }

  @override
  Future<void> close() async {
    _isInitialized = false;
  }

  @override
  bool get isInitialized => _isInitialized;
}

/// Factory class for creating common mock scenarios
class MockEntityExtractorFactory {
  
  /// Create a mock extractor that returns empty results (no entities found)
  static MockEntityExtractor createEmpty() {
    final mock = MockEntityExtractor();
    mock.setMockResults([]);
    return mock;
  }

  /// Create a mock extractor that throws an error
  static MockEntityExtractor createWithError([String? errorMessage]) {
    final mock = MockEntityExtractor();
    mock.setShouldThrowError(true, errorMessage);
    return mock;
  }

  /// Create a mock extractor with a simple money entity
  static MockEntityExtractor createWithMoneyEntity({
    required String text,
    required String entityText,
    required int start,
    required int end,
  }) {
    final mock = MockEntityExtractor();
    mock.setMockResults([
      MockEntityAnnotation(
        text: entityText,
        start: start,
        end: end,
        entityType: EntityType.money,
      ),
    ]);
    return mock;
  }

  /// Create a mock extractor with a datetime entity
  static MockEntityExtractor createWithDateTimeEntity({
    required String text,
    required String entityText,
    required int start,
    required int end,
  }) {
    final mock = MockEntityExtractor();
    mock.setMockResults([
      MockEntityAnnotation(
        text: entityText,
        start: start,
        end: end,
        entityType: EntityType.dateTime,
      ),
    ]);
    return mock;
  }

  /// Create a mock extractor with both money and datetime entities
  static MockEntityExtractor createWithMultipleEntities({
    required String moneyText,
    required int moneyStart,
    required int moneyEnd,
    required String dateTimeText,
    required int dateTimeStart,
    required int dateTimeEnd,
  }) {
    final mock = MockEntityExtractor();
    mock.setMockResults([
      MockEntityAnnotation(
        text: moneyText,
        start: moneyStart,
        end: moneyEnd,
        entityType: EntityType.money,
      ),
      MockEntityAnnotation(
        text: dateTimeText,
        start: dateTimeStart,
        end: dateTimeEnd,
        entityType: EntityType.dateTime,
      ),
    ]);
    return mock;
  }

  /// Create a mock extractor for testing USD currency scenarios
  static MockEntityExtractor createUSDScenario(String amountText, int start, int end) {
    return createWithMoneyEntity(
      text: 'Spent $amountText on lunch',
      entityText: amountText,
      start: start,
      end: end,
    );
  }

  /// Create a mock extractor for testing EUR currency scenarios
  static MockEntityExtractor createEURScenario(String amountText, int start, int end) {
    return createWithMoneyEntity(
      text: 'Paid $amountText for dinner',
      entityText: amountText,
      start: start,
      end: end,
    );
  }

  /// Create a mock extractor for testing edge cases (very large amounts, decimals, etc.)
  static MockEntityExtractor createEdgeCaseScenario({
    required String entityText,
    required int start,
    required int end,
  }) {
    return createWithMoneyEntity(
      text: 'Transaction with $entityText',
      entityText: entityText,
      start: start,
      end: end,
    );
  }

  /// Create a mock extractor that simulates ML Kit finding entities but no money entity
  static MockEntityExtractor createWithNonMoneyEntities() {
    final mock = MockEntityExtractor();
    mock.setMockResults([
      MockEntityAnnotation(
        text: '<EMAIL>',
        start: 10,
        end: 25,
        entityType: EntityType.email,
      ),
      MockEntityAnnotation(
        text: '123 Main St',
        start: 30,
        end: 41,
        entityType: EntityType.address,
      ),
    ]);
    return mock;
  }

  /// Create a mock extractor for amount confirmation scenarios
  /// This simulates cases where ML Kit finds multiple money entities that could be ambiguous
  static MockEntityExtractor createAmountConfirmationScenario({
    required String fullText,
    required List<Map<String, dynamic>> moneyEntities,
  }) {
    final mock = MockEntityExtractor();
    final annotations = moneyEntities.map((entity) => MockEntityAnnotation(
      text: entity['text'] as String,
      start: entity['start'] as int,
      end: entity['end'] as int,
      entityType: EntityType.money,
    )).toList();

    mock.setMockResults(annotations);
    return mock;
  }

  /// Create a mock extractor for the classic "Lux68 vs 2m" ambiguity scenario
  static MockEntityExtractor createLux68AmountConfirmationScenario() {
    return createAmountConfirmationScenario(
      fullText: 'com trua tai Lux68 2m',
      moneyEntities: [
        {'text': '68', 'start': 15, 'end': 17},  // Embedded in "Lux68"
        {'text': '2m', 'start': 18, 'end': 20}, // Actual amount
      ],
    );
  }

  /// Add support for creating specific ML Kit entity scenarios
  static void addMoneyEntityToMock(MockEntityExtractor mock, String text, int start, int end, String entityText) {
    final currentResults = List<EntityAnnotationBase>.from(mock._mockResults);
    currentResults.add(MockEntityAnnotation(
      text: entityText,
      start: start,
      end: end,
      entityType: EntityType.money,
    ));
    mock.setMockResults(currentResults);
  }

  /// Helper that sets up the exact bug scenario: returns "68" as a money entity from position of "Lux68"
  static MockEntityExtractor simulateLux68Scenario() {
    final mock = MockEntityExtractor();
    // In "com trua tai Lux68 2m", "Lux68" starts at position 12 and "68" is at positions 15-17
    mock.setMockResults([
      MockEntityAnnotation(
        text: '68',
        start: 15,
        end: 17,
        entityType: EntityType.money,
      ),
    ]);
    return mock;
  }

  /// Helper to test selection logic between multiple candidates
  static MockEntityExtractor simulateMultipleMoneyEntities({
    required List<Map<String, dynamic>> entities,
  }) {
    final mock = MockEntityExtractor();
    final annotations = entities.map((entity) => MockEntityAnnotation(
      text: entity['text'] as String,
      start: entity['start'] as int,
      end: entity['end'] as int,
      entityType: EntityType.money,
    )).toList();
    mock.setMockResults(annotations);
    return mock;
  }

  /// Helper to calculate start/end positions for entity text within full input
  static Map<String, int> calculateEntityPosition(String fullText, String entityText) {
    final index = fullText.indexOf(entityText);
    if (index == -1) {
      throw ArgumentError('Entity text "$entityText" not found in full text "$fullText"');
    }
    return {
      'start': index,
      'end': index + entityText.length,
    };
  }

  /// Create mock for vendor name embedded number scenarios
  static MockEntityExtractor createVendorNameScenario({
    required String fullText,
    required String embeddedNumber,
  }) {
    final position = calculateEntityPosition(fullText, embeddedNumber);
    return createWithMoneyEntity(
      text: fullText,
      entityText: embeddedNumber,
      start: position['start']!,
      end: position['end']!,
    );
  }

  /// Create mock with exact position specification for more precise testing
  /// This allows tests to simulate exact ML Kit output without relying on string searching
  static MockEntityExtractor createExactPositionScenario({
    required String entityText,
    required int start,
    required int end,
    EntityType entityType = EntityType.money,
  }) {
    final mock = MockEntityExtractor();
    mock.setMockResults([
      MockEntityAnnotation(
        text: entityText,
        start: start,
        end: end,
        entityType: entityType,
      ),
    ]);
    return mock;
  }

  /// Create mock that simulates ML Kit finding "70" at the exact position in "com trua tai lux70 100k"
  static MockEntityExtractor createLux70Scenario() {
    final mock = MockEntityExtractor();
    // In "com trua tai lux70 100k", "70" is at positions 15-17
    mock.setMockResults([
      MockEntityAnnotation(
        text: '70',
        start: 15,
        end: 17,
        entityType: EntityType.money,
      ),
    ]);
    return mock;
  }

  /// Create mock for testing multiple vendor names with embedded numbers
  static MockEntityExtractor createMultipleEmbeddedScenario() {
    final mock = MockEntityExtractor();
    // Simulate "Shop123 Mall456 100k" with embedded numbers at positions
    // "Shop123 Mall456 100k"
    //  0123456789012345678901
    // "123" is at positions 4-7 (exclusive end)
    // "456" is at positions 12-15 (exclusive end)
    mock.setMockResults([
      MockEntityAnnotation(
        text: '123',
        start: 4,
        end: 7,
        entityType: EntityType.money,
      ),
      MockEntityAnnotation(
        text: '456',
        start: 12,
        end: 15,
        entityType: EntityType.money,
      ),
    ]);
    return mock;
  }

  /// Create mock for cases where ML Kit misses abbreviations but finds embedded numbers
  static MockEntityExtractor createAbbreviationMissedScenario({
    required String fullText,
    required String embeddedNumber,
    required int embeddedStart,
    required int embeddedEnd,
  }) {
    final mock = MockEntityExtractor();
    // Only return the embedded number, not the abbreviation (simulating ML Kit missing it)
    mock.setMockResults([
      MockEntityAnnotation(
        text: embeddedNumber,
        start: embeddedStart,
        end: embeddedEnd,
        entityType: EntityType.money,
      ),
    ]);
    return mock;
  }

  /// Enhanced position calculation with better error handling for edge cases
  static Map<String, int> calculateEntityPositionSafe(String fullText, String entityText) {
    // Handle multiple occurrences by finding the first one
    final index = fullText.indexOf(entityText);
    if (index == -1) {
      // If exact match not found, try case-insensitive search
      final lowerFullText = fullText.toLowerCase();
      final lowerEntityText = entityText.toLowerCase();
      final caseInsensitiveIndex = lowerFullText.indexOf(lowerEntityText);

      if (caseInsensitiveIndex == -1) {
        throw ArgumentError('Entity text "$entityText" not found in full text "$fullText"');
      }

      return {
        'start': caseInsensitiveIndex,
        'end': caseInsensitiveIndex + entityText.length,
      };
    }

    return {
      'start': index,
      'end': index + entityText.length,
    };
  }

  /// Create mock with enhanced position accuracy for Unicode and special characters
  static MockEntityExtractor createUnicodeAwareScenario({
    required String fullText,
    required String entityText,
  }) {
    final position = calculateEntityPositionSafe(fullText, entityText);
    return createWithMoneyEntity(
      text: fullText,
      entityText: entityText,
      start: position['start']!,
      end: position['end']!,
    );
  }
}
