import '../../lib/models/transaction_model.dart';

/// Spanish sample transaction texts with expected parsing results
/// Used for testing Spanish localization in the parsing pipeline
class SpanishSampleTransactions {
  
  /// Simple expense transactions in Spanish with clear amount and currency
  static const Map<String, Map<String, dynamic>> simpleExpenses = {
    'cafe_eur': {
      'text': 'Gasté €5,50 en café',
      'expected_amount': 5.50,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'café',
      'expected_category_keywords': ['café', 'comida', 'bebida'],
    },
    'almuerzo_eur': {
      'text': 'Pagado €12,75 para almuerzo',
      'expected_amount': 12.75,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'almuerzo',
      'expected_category_keywords': ['almuerzo', 'comida', 'restaurante'],
    },
    'gasolina_eur': {
      'text': 'Gasolina €45,20',
      'expected_amount': 45.20,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'gasolina',
      'expected_category_keywords': ['gasolina', 'combustible', 'transporte'],
    },
    'cena_eur': {
      'text': 'Cena €25,00',
      'expected_amount': 25.00,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'cena',
      'expected_category_keywords': ['cena', 'comida', 'restaurante'],
    },
    'compras_eur': {
      'text': 'Comprado €150,75 en compras',
      'expected_amount': 150.75,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'compras',
      'expected_category_keywords': ['compras', 'shopping'],
    },
  };

  /// Income transactions in Spanish
  static const Map<String, Map<String, dynamic>> incomeTransactions = {
    'salario_eur': {
      'text': 'Recibido €3.500 salario',
      'expected_amount': 3500.0,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.income,
      'expected_description': 'salario',
      'expected_category_keywords': ['salario', 'ingreso', 'trabajo'],
    },
    'freelance_eur': {
      'text': 'Ingreso freelance €850,50',
      'expected_amount': 850.50,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.income,
      'expected_description': 'ingreso freelance',
      'expected_category_keywords': ['freelance', 'ingreso', 'trabajo'],
    },
    'bono_eur': {
      'text': 'Ganado €1.200 bono',
      'expected_amount': 1200.0,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.income,
      'expected_description': 'bono',
      'expected_category_keywords': ['bono', 'ingreso', 'trabajo'],
    },
    'venta_eur': {
      'text': 'Vendido €500 regalo',
      'expected_amount': 500.0,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.income,
      'expected_description': 'regalo',
      'expected_category_keywords': ['venta', 'regalo', 'ingreso'],
    },
    'reembolso_eur': {
      'text': 'Reembolso de €75,25',
      'expected_amount': 75.25,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.income,
      'expected_description': 'reembolso',
      'expected_category_keywords': ['reembolso', 'ingreso'],
    },
  };

  /// Loan transactions in Spanish
  static const Map<String, Map<String, dynamic>> loanTransactions = {
    'prestar_eur': {
      'text': 'Prestado €200 a amigo',
      'expected_amount': 200.0,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.loan,
      'expected_description': 'prestado a amigo',
      'expected_category_keywords': ['préstamo', 'prestar', 'amigo'],
    },
    'pedir_prestado_eur': {
      'text': 'Pedir prestado €500 del banco',
      'expected_amount': 500.0,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.loan,
      'expected_description': 'pedir prestado del banco',
      'expected_category_keywords': ['pedir prestado', 'préstamo', 'banco'],
    },
    'deuda_eur': {
      'text': 'Deuda €1.000 crédito',
      'expected_amount': 1000.0,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.loan,
      'expected_description': 'deuda crédito',
      'expected_category_keywords': ['deuda', 'crédito', 'préstamo'],
    },
  };

  /// Complex transaction descriptions with multiple keywords in Spanish
  static const Map<String, Map<String, dynamic>> complexTransactions = {
    'restaurante_con_tags': {
      'text': 'Cena en restaurante italiano €45,99 #comida #cena #italiano',
      'expected_amount': 45.99,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'cena en restaurante italiano',
      'expected_tags': ['comida', 'cena', 'italiano'],
      'expected_category_keywords': ['restaurante', 'comida', 'cena'],
    },
    'compras_supermercado': {
      'text': 'Comprado €89,50 en supermercado para comida #compras #supermercado',
      'expected_amount': 89.50,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'comprado en supermercado para comida',
      'expected_tags': ['compras', 'supermercado'],
      'expected_category_keywords': ['compras', 'supermercado', 'comida'],
    },
    'salario_con_detalles': {
      'text': 'Recibido €2.850,75 salario mensual trabajo #salario #trabajo #mensual',
      'expected_amount': 2850.75,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.income,
      'expected_description': 'recibido salario mensual trabajo',
      'expected_tags': ['salario', 'trabajo', 'mensual'],
      'expected_category_keywords': ['salario', 'trabajo', 'ingreso'],
    },
  };

  /// Negative amount transactions (should be detected as expenses)
  static const Map<String, Map<String, dynamic>> negativeAmountTransactions = {
    'gasto_negativo': {
      'text': '-€50,00 para cena',
      'expected_amount': 50.0,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'para cena',
      'expected_category_keywords': ['cena', 'comida'],
    },
    'compra_negativa': {
      'text': '-€125,50 comprado ropa',
      'expected_amount': 125.50,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'comprado ropa',
      'expected_category_keywords': ['compra', 'ropa'],
    },
  };

  /// Edge cases and challenging parsing scenarios in Spanish
  static const Map<String, Map<String, dynamic>> edgeCases = {
    'sin_tipo_claro': {
      'text': '€100,00 para María',
      'expected_amount': 100.0,
      'expected_currency': 'EUR',
      'expected_type': null, // Should require type disambiguation
      'expected_description': 'para María',
      'expected_category_keywords': [],
    },
    'multiple_cantidades': {
      'text': 'Gasté €25,50 en café y €15,75 en pastel',
      'expected_amount': 25.50, // Should parse first amount
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'gasté en café y €15,75 en pastel',
      'expected_category_keywords': ['café', 'comida'],
    },
    'formato_miles': {
      'text': 'Recibido €1.500,25 salario',
      'expected_amount': 1500.25,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.income,
      'expected_description': 'salario',
      'expected_category_keywords': ['salario', 'ingreso'],
    },
    'sin_separador_decimal': {
      'text': 'Gasté €50 en comida',
      'expected_amount': 50.0,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'comida',
      'expected_category_keywords': ['comida'],
    },
  };

  /// All Spanish sample transactions combined
  static Map<String, Map<String, dynamic>> get allTransactions {
    return {
      ...simpleExpenses,
      ...incomeTransactions,
      ...loanTransactions,
      ...complexTransactions,
      ...negativeAmountTransactions,
      ...edgeCases,
    };
  }

  /// Get transactions by type
  static Map<String, Map<String, dynamic>> getTransactionsByType(TransactionType type) {
    return allTransactions.entries
        .where((entry) => entry.value['expected_type'] == type)
        .fold<Map<String, Map<String, dynamic>>>(
          {},
          (map, entry) => map..[entry.key] = entry.value,
        );
  }

  /// Get transactions with specific currency
  static Map<String, Map<String, dynamic>> getTransactionsByCurrency(String currency) {
    return allTransactions.entries
        .where((entry) => entry.value['expected_currency'] == currency)
        .fold<Map<String, Map<String, dynamic>>>(
          {},
          (map, entry) => map..[entry.key] = entry.value,
        );
  }

  /// Get transactions with tags
  static Map<String, Map<String, dynamic>> getTransactionsWithTags() {
    return allTransactions.entries
        .where((entry) => entry.value.containsKey('expected_tags'))
        .fold<Map<String, Map<String, dynamic>>>(
          {},
          (map, entry) => map..[entry.key] = entry.value,
        );
  }

  /// Get transactions that should require type disambiguation
  static Map<String, Map<String, dynamic>> getAmbiguousTransactions() {
    return allTransactions.entries
        .where((entry) => entry.value['expected_type'] == null)
        .fold<Map<String, Map<String, dynamic>>>(
          {},
          (map, entry) => map..[entry.key] = entry.value,
        );
  }
}
