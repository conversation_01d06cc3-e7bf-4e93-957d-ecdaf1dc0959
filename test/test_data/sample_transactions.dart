import '../../lib/models/transaction_model.dart';

/// Comprehensive collection of sample transaction texts with expected parsing results
/// Used for testing the parsing pipeline with real-world examples
class SampleTransactions {
  
  /// Simple expense transactions with clear amount and currency
  static const Map<String, Map<String, dynamic>> simpleExpenses = {
    'coffee_usd': {
      'text': 'Spent \$5.50 on coffee',
      'expected_amount': 5.50,
      'expected_currency': 'USD',
      'expected_type': TransactionType.expense,
      'expected_description': 'coffee',
      'expected_category_keywords': ['coffee', 'food', 'beverage'],
    },
    'lunch_eur': {
      'text': 'Paid €12.75 for lunch',
      'expected_amount': 12.75,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'lunch',
      'expected_category_keywords': ['lunch', 'food', 'restaurant'],
    },
    'gas_gbp': {
      'text': 'Gas station £45.20',
      'expected_amount': 45.20,
      'expected_currency': 'GBP',
      'expected_type': TransactionType.expense,
      'expected_description': 'gas station',
      'expected_category_keywords': ['gas', 'fuel', 'transport'],
    },
    'dinner_jpy': {
      'text': 'Dinner ¥2500',
      'expected_amount': 2500.0,
      'expected_currency': 'JPY',
      'expected_type': TransactionType.expense,
      'expected_description': 'dinner',
      'expected_category_keywords': ['dinner', 'food', 'restaurant'],
    },
  };

  /// Income transactions
  static const Map<String, Map<String, dynamic>> incomeTransactions = {
    'salary_usd': {
      'text': 'Received \$3500 salary payment',
      'expected_amount': 3500.0,
      'expected_currency': 'USD',
      'expected_type': TransactionType.income,
      'expected_description': 'salary payment',
      'expected_category_keywords': ['salary', 'income', 'work'],
    },
    'freelance_eur': {
      'text': 'Freelance income €850.50',
      'expected_amount': 850.50,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.income,
      'expected_description': 'freelance income',
      'expected_category_keywords': ['freelance', 'income', 'work'],
    },
    'bonus_gbp': {
      'text': 'Bonus payment £1200',
      'expected_amount': 1200.0,
      'expected_currency': 'GBP',
      'expected_type': TransactionType.income,
      'expected_description': 'bonus payment',
      'expected_category_keywords': ['bonus', 'income', 'work'],
    },
  };

  /// Loan transactions
  static const Map<String, Map<String, dynamic>> loanTransactions = {
    'lend_usd': {
      'text': 'Lent \$200 to friend',
      'expected_amount': 200.0,
      'expected_currency': 'USD',
      'expected_type': TransactionType.loan,
      'expected_description': 'lent to friend',
      'expected_category_keywords': ['loan', 'lend', 'friend'],
    },
    'borrow_eur': {
      'text': 'Borrowed €500 from bank',
      'expected_amount': 500.0,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.loan,
      'expected_description': 'borrowed from bank',
      'expected_category_keywords': ['borrow', 'loan', 'bank'],
    },
  };

  /// Complex transaction descriptions with multiple keywords
  static const Map<String, Map<String, dynamic>> complexTransactions = {
    'restaurant_with_tags': {
      'text': 'Dinner at Italian restaurant \$45.99 #food #dining #italian',
      'expected_amount': 45.99,
      'expected_currency': 'USD',
      'expected_type': TransactionType.expense,
      'expected_description': 'dinner at italian restaurant',
      'expected_tags': ['food', 'dining', 'italian'],
      'expected_category_keywords': ['restaurant', 'food', 'dining'],
    },
    'grocery_shopping': {
      'text': 'Weekly grocery shopping at Whole Foods €125.30 #groceries #food #weekly',
      'expected_amount': 125.30,
      'expected_currency': 'EUR',
      'expected_type': TransactionType.expense,
      'expected_description': 'weekly grocery shopping at whole foods',
      'expected_tags': ['groceries', 'food', 'weekly'],
      'expected_category_keywords': ['grocery', 'food', 'shopping'],
    },
    'utility_bill': {
      'text': 'Electricity bill payment \$89.45 #utilities #bills #monthly',
      'expected_amount': 89.45,
      'expected_currency': 'USD',
      'expected_type': TransactionType.expense,
      'expected_description': 'electricity bill payment',
      'expected_tags': ['utilities', 'bills', 'monthly'],
      'expected_category_keywords': ['utilities', 'electricity', 'bills'],
    },
  };

  /// Different currency formats and symbols
  static const Map<String, Map<String, dynamic>> currencyVariations = {
    'indian_rupee': {
      'text': 'Bought groceries ₹1250.75',
      'expected_amount': 1250.75,
      'expected_currency': 'INR',
      'expected_type': TransactionType.expense,
      'expected_description': 'bought groceries',
      'expected_category_keywords': ['groceries', 'food'],
    },
    'korean_won': {
      'text': 'Coffee ₩5000',
      'expected_amount': 5000.0,
      'expected_currency': 'KRW',
      'expected_type': TransactionType.expense,
      'expected_description': 'coffee',
      'expected_category_keywords': ['coffee', 'beverage'],
    },
    'chinese_yuan': {
      'text': 'Taxi fare ¥45.50',
      'expected_amount': 45.50,
      'expected_currency': 'CNY',
      'expected_type': TransactionType.expense,
      'expected_description': 'taxi fare',
      'expected_category_keywords': ['taxi', 'transport'],
    },
    'thai_baht': {
      'text': 'Street food ฿120',
      'expected_amount': 120.0,
      'expected_currency': 'THB',
      'expected_type': TransactionType.expense,
      'expected_description': 'street food',
      'expected_category_keywords': ['food', 'street'],
    },
  };

  /// Edge cases and boundary conditions
  static const Map<String, Map<String, dynamic>> edgeCases = {
    'very_small_amount': {
      'text': 'Parking meter \$0.25',
      'expected_amount': 0.25,
      'expected_currency': 'USD',
      'expected_type': TransactionType.expense,
      'expected_description': 'parking meter',
      'expected_category_keywords': ['parking', 'transport'],
    },
    'large_amount': {
      'text': 'Car purchase \$45999.99',
      'expected_amount': 45999.99,
      'expected_currency': 'USD',
      'expected_type': TransactionType.expense,
      'expected_description': 'car purchase',
      'expected_category_keywords': ['car', 'vehicle', 'purchase'],
    },
    'zero_amount': {
      'text': 'Free coffee \$0.00',
      'expected_amount': 0.0,
      'expected_currency': 'USD',
      'expected_type': TransactionType.expense,
      'expected_description': 'free coffee',
      'expected_category_keywords': ['coffee', 'free'],
    },
    'whole_number': {
      'text': 'Movie tickets \$20',
      'expected_amount': 20.0,
      'expected_currency': 'USD',
      'expected_type': TransactionType.expense,
      'expected_description': 'movie tickets',
      'expected_category_keywords': ['movie', 'entertainment'],
    },
  };

  /// Malformed and problematic inputs
  static const Map<String, Map<String, dynamic>> malformedInputs = {
    'empty_text': {
      'text': '',
      'should_fail': true,
      'expected_error': 'Empty or invalid input',
    },
    'whitespace_only': {
      'text': '   \n\t  ',
      'should_fail': true,
      'expected_error': 'Empty or invalid input',
    },
    'no_amount': {
      'text': 'Went to the store',
      'should_fail': true,
      'expected_error': 'Could not extract amount',
    },
    'invalid_currency': {
      'text': 'Paid XYZ123.45 for lunch',
      'should_fail': true,
      'expected_error': 'Invalid currency',
    },
    'multiple_amounts': {
      'text': 'Paid \$10 and \$20 for items',
      'should_fail': false, // Should pick first amount
      'expected_amount': 10.0,
      'expected_currency': 'USD',
      'expected_type': TransactionType.expense,
    },
    'negative_amount': {
      'text': 'Refund \$-25.50',
      'should_fail': false, // Should handle as positive
      'expected_amount': 25.50,
      'expected_currency': 'USD',
      'expected_type': TransactionType.income, // Refund treated as income
    },
  };

  /// Real-world examples from different regions
  static const Map<String, Map<String, dynamic>> realWorldExamples = {
    'us_grocery': {
      'text': 'Walmart grocery run \$87.34 bought milk, bread, eggs #groceries',
      'expected_amount': 87.34,
      'expected_currency': 'USD',
      'expected_type': TransactionType.expense,
      'expected_description': 'walmart grocery run bought milk, bread, eggs',
      'expected_tags': ['groceries'],
      'expected_category_keywords': ['grocery', 'food', 'walmart'],
    },
    'uk_pub': {
      'text': 'Drinks at the pub £28.50 with mates #social #drinks',
      'expected_amount': 28.50,
      'expected_currency': 'GBP',
      'expected_type': TransactionType.expense,
      'expected_description': 'drinks at the pub with mates',
      'expected_tags': ['social', 'drinks'],
      'expected_category_keywords': ['drinks', 'pub', 'social'],
    },
    'japan_convenience': {
      'text': 'Konbini snacks ¥850 seven eleven',
      'expected_amount': 850.0,
      'expected_currency': 'JPY',
      'expected_type': TransactionType.expense,
      'expected_description': 'konbini snacks seven eleven',
      'expected_category_keywords': ['snacks', 'food', 'convenience'],
    },
    'india_auto': {
      'text': 'Auto rickshaw ₹120 from station to home',
      'expected_amount': 120.0,
      'expected_currency': 'INR',
      'expected_type': TransactionType.expense,
      'expected_description': 'auto rickshaw from station to home',
      'expected_category_keywords': ['transport', 'rickshaw', 'travel'],
    },
  };

  /// Get all test cases as a combined map
  static Map<String, Map<String, dynamic>> getAllTestCases() {
    final allCases = <String, Map<String, dynamic>>{};
    allCases.addAll(simpleExpenses);
    allCases.addAll(incomeTransactions);
    allCases.addAll(loanTransactions);
    allCases.addAll(complexTransactions);
    allCases.addAll(currencyVariations);
    allCases.addAll(edgeCases);
    allCases.addAll(malformedInputs);
    allCases.addAll(realWorldExamples);
    return allCases;
  }

  /// Get only valid test cases (excluding malformed inputs)
  static Map<String, Map<String, dynamic>> getValidTestCases() {
    final validCases = <String, Map<String, dynamic>>{};
    validCases.addAll(simpleExpenses);
    validCases.addAll(incomeTransactions);
    validCases.addAll(loanTransactions);
    validCases.addAll(complexTransactions);
    validCases.addAll(currencyVariations);
    validCases.addAll(edgeCases);
    validCases.addAll(realWorldExamples);
    return validCases;
  }

  /// Get test cases that should fail parsing
  static Map<String, Map<String, dynamic>> getFailureTestCases() {
    return Map.fromEntries(
      malformedInputs.entries.where(
        (entry) => entry.value['should_fail'] == true,
      ),
    );
  }

  /// Get test cases for specific currency
  static Map<String, Map<String, dynamic>> getTestCasesForCurrency(String currency) {
    return Map.fromEntries(
      getAllTestCases().entries.where(
        (entry) => entry.value['expected_currency'] == currency,
      ),
    );
  }

  /// Get test cases for specific transaction type
  static Map<String, Map<String, dynamic>> getTestCasesForType(TransactionType type) {
    return Map.fromEntries(
      getAllTestCases().entries.where(
        (entry) => entry.value['expected_type'] == type,
      ),
    );
  }

  /// Get test cases with tags
  static Map<String, Map<String, dynamic>> getTestCasesWithTags() {
    return Map.fromEntries(
      getAllTestCases().entries.where(
        (entry) => entry.value.containsKey('expected_tags'),
      ),
    );
  }
}
