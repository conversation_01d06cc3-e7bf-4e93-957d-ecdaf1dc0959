import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/parse_result.dart';
import '../../lib/models/pending_transaction_state.dart';
import '../../lib/models/transaction_model.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('ParseStatus Extension Tests', () {
    group('isSoftFail Getter', () {
      test('returns true for soft-fail statuses', () {
        expect(ParseStatus.needsType.isSoftFail, isTrue);
        expect(ParseStatus.needsCategory.isSoftFail, isTrue);
        expect(ParseStatus.needsAmountConfirmation.isSoftFail, isTrue);
        expect(ParseStatus.missingAmount.isSoftFail, isTrue);
        expect(ParseStatus.ambiguousAmount.isSoftFail, isTrue);
      });

      test('returns false for non-soft-fail statuses', () {
        expect(ParseStatus.success.isSoftFail, isFalse);
        expect(ParseStatus.failed.isSoftFail, isFalse);
      });

      test('comprehensive truth table for all ParseStatus enum values', () {
        final expectedSoftFailStatuses = {
          ParseStatus.needsType,
          ParseStatus.needsCategory,
          ParseStatus.needsAmountConfirmation,
          ParseStatus.missingAmount,
          ParseStatus.ambiguousAmount,
        };

        final expectedNonSoftFailStatuses = {
          ParseStatus.success,
          ParseStatus.failed,
        };

        // Test all soft-fail statuses
        for (final status in expectedSoftFailStatuses) {
          expect(
            status.isSoftFail,
            isTrue,
            reason: '$status should be a soft-fail status',
          );
        }

        // Test all non-soft-fail statuses
        for (final status in expectedNonSoftFailStatuses) {
          expect(
            status.isSoftFail,
            isFalse,
            reason: '$status should not be a soft-fail status',
          );
        }

        // Verify we've covered all enum values
        final allStatuses = ParseStatus.values.toSet();
        final testedStatuses = {...expectedSoftFailStatuses, ...expectedNonSoftFailStatuses};
        expect(
          testedStatuses,
          equals(allStatuses),
          reason: 'All ParseStatus enum values should be tested',
        );
      });
    });

    group('toPendingStage Method', () {
      test('needsType maps to typeSelection', () {
        expect(
          ParseStatus.needsType.toPendingStage(),
          equals(PendingStage.typeSelection),
        );
      });

      test('needsCategory maps to categorySelection', () {
        expect(
          ParseStatus.needsCategory.toPendingStage(),
          equals(PendingStage.categorySelection),
        );
      });

      test('needsAmountConfirmation maps to amountConfirmation', () {
        expect(
          ParseStatus.needsAmountConfirmation.toPendingStage(),
          equals(PendingStage.amountConfirmation),
        );
      });

      test('ambiguousAmount maps to amountConfirmation', () {
        expect(
          ParseStatus.ambiguousAmount.toPendingStage(),
          equals(PendingStage.amountConfirmation),
        );
      });

      test('missingAmount maps to missingAmount', () {
        expect(
          ParseStatus.missingAmount.toPendingStage(),
          equals(PendingStage.missingAmount),
        );
      });

      test('success returns null', () {
        expect(ParseStatus.success.toPendingStage(), isNull);
      });

      test('failed returns null', () {
        expect(ParseStatus.failed.toPendingStage(), isNull);
      });

      test('comprehensive mapping test for all enum values', () {
        final expectedMappings = {
          ParseStatus.needsType: PendingStage.typeSelection,
          ParseStatus.needsCategory: PendingStage.categorySelection,
          ParseStatus.needsAmountConfirmation: PendingStage.amountConfirmation,
          ParseStatus.ambiguousAmount: PendingStage.amountConfirmation,
          ParseStatus.missingAmount: PendingStage.missingAmount,
          ParseStatus.success: null,
          ParseStatus.failed: null,
        };

        for (final entry in expectedMappings.entries) {
          expect(
            entry.key.toPendingStage(),
            equals(entry.value),
            reason: '${entry.key} should map to ${entry.value}',
          );
        }

        // Verify we've covered all enum values
        expect(
          expectedMappings.keys.toSet(),
          equals(ParseStatus.values.toSet()),
          reason: 'All ParseStatus enum values should have a mapping defined',
        );
      });
    });

    group('requiresAmountConfirmation Getter', () {
      test('returns true for amount confirmation statuses', () {
        expect(ParseStatus.needsAmountConfirmation.requiresAmountConfirmation, isTrue);
        expect(ParseStatus.ambiguousAmount.requiresAmountConfirmation, isTrue);
      });

      test('returns false for all other ParseStatus values', () {
        expect(ParseStatus.needsType.requiresAmountConfirmation, isFalse);
        expect(ParseStatus.needsCategory.requiresAmountConfirmation, isFalse);
        expect(ParseStatus.missingAmount.requiresAmountConfirmation, isFalse);
        expect(ParseStatus.success.requiresAmountConfirmation, isFalse);
        expect(ParseStatus.failed.requiresAmountConfirmation, isFalse);
      });

      test('comprehensive test for all enum values', () {
        final expectedAmountConfirmationStatuses = {
          ParseStatus.needsAmountConfirmation,
          ParseStatus.ambiguousAmount,
        };

        final expectedNonAmountConfirmationStatuses = {
          ParseStatus.needsType,
          ParseStatus.needsCategory,
          ParseStatus.missingAmount,
          ParseStatus.success,
          ParseStatus.failed,
        };

        // Test amount confirmation statuses
        for (final status in expectedAmountConfirmationStatuses) {
          expect(
            status.requiresAmountConfirmation,
            isTrue,
            reason: '$status should require amount confirmation',
          );
        }

        // Test non-amount confirmation statuses
        for (final status in expectedNonAmountConfirmationStatuses) {
          expect(
            status.requiresAmountConfirmation,
            isFalse,
            reason: '$status should not require amount confirmation',
          );
        }

        // Verify we've covered all enum values
        final allStatuses = ParseStatus.values.toSet();
        final testedStatuses = {
          ...expectedAmountConfirmationStatuses,
          ...expectedNonAmountConfirmationStatuses,
        };
        expect(
          testedStatuses,
          equals(allStatuses),
          reason: 'All ParseStatus enum values should be tested',
        );
      });
    });

    group('Consistency with ParseResult', () {
      late Transaction testTransaction;

      setUp(() {
        testTransaction = TestHelpers.createTestTransaction();
      });

      test('isSoftFail matches the existing requiresUserInput getter behavior', () {
        final testCases = [
          ParseResult.success(testTransaction),
          ParseResult.needsCategory(testTransaction),
          ParseResult.needsType(testTransaction),
          ParseResult.needsAmountConfirmation(testTransaction, [100.0], ['100']),
          ParseResult.missingAmount(testTransaction),
          ParseResult.ambiguousAmount(testTransaction, [100.0, 200.0], ['100', '200']),
          ParseResult.failed(testTransaction, 'Test error'),
        ];

        for (final parseResult in testCases) {
          expect(
            parseResult.status.isSoftFail,
            equals(parseResult.requiresUserInput),
            reason: 'isSoftFail should match requiresUserInput for ${parseResult.status}',
          );
        }
      });

      test('extension methods work correctly with ParseResult instances created by TestHelpers', () {
        final successResult = TestHelpers.createSuccessParseResult();
        final needsCategoryResult = TestHelpers.createNeedsCategoryParseResult();

        expect(successResult.status.isSoftFail, isFalse);
        expect(successResult.status.toPendingStage(), isNull);
        expect(successResult.status.requiresAmountConfirmation, isFalse);

        expect(needsCategoryResult.status.isSoftFail, isTrue);
        expect(needsCategoryResult.status.toPendingStage(), equals(PendingStage.categorySelection));
        expect(needsCategoryResult.status.requiresAmountConfirmation, isFalse);
      });

      test('mapping logic aligns with PendingTransactionState factory constructor validation', () {
        // Test that the extension mapping matches what the factory constructors expect
        final testTransaction = TestHelpers.createTestTransaction();

        // needsType should map to typeSelection and work with forTypeSelection
        final needsTypeResult = ParseResult.needsType(testTransaction);
        expect(needsTypeResult.status.toPendingStage(), equals(PendingStage.typeSelection));
        expect(
          () => PendingTransactionState.forTypeSelection('test', needsTypeResult),
          returnsNormally,
        );

        // needsCategory should map to categorySelection and work with forCategorySelection
        final needsCategoryResult = ParseResult.needsCategory(testTransaction);
        expect(needsCategoryResult.status.toPendingStage(), equals(PendingStage.categorySelection));
        expect(
          () => PendingTransactionState.forCategorySelection('test', needsCategoryResult),
          returnsNormally,
        );

        // needsAmountConfirmation should map to amountConfirmation and work with forAmountConfirmation
        final needsAmountResult = ParseResult.needsAmountConfirmation(testTransaction, [100.0], ['100']);
        expect(needsAmountResult.status.toPendingStage(), equals(PendingStage.amountConfirmation));
        expect(
          () => PendingTransactionState.forAmountConfirmation('test', needsAmountResult),
          returnsNormally,
        );

        // ambiguousAmount should map to amountConfirmation and work with forAmountConfirmation
        final ambiguousAmountResult = ParseResult.ambiguousAmount(testTransaction, [100.0, 200.0], ['100', '200']);
        expect(ambiguousAmountResult.status.toPendingStage(), equals(PendingStage.amountConfirmation));
        expect(
          () => PendingTransactionState.forAmountConfirmation('test', ambiguousAmountResult),
          returnsNormally,
        );

        // missingAmount should map to missingAmount and work with forMissingAmount
        final missingAmountResult = ParseResult.missingAmount(testTransaction);
        expect(missingAmountResult.status.toPendingStage(), equals(PendingStage.missingAmount));
        expect(
          () => PendingTransactionState.forMissingAmount('test', missingAmountResult),
          returnsNormally,
        );

        // success and failed should map to null and not work with any factory constructor
        final successResult = ParseResult.success(testTransaction);
        expect(successResult.status.toPendingStage(), isNull);
        expect(
          () => PendingTransactionState.forCategorySelection('test', successResult),
          throwsA(isA<ArgumentError>()),
        );

        final failedResult = ParseResult.failed(testTransaction, 'error');
        expect(failedResult.status.toPendingStage(), isNull);
        expect(
          () => PendingTransactionState.forTypeSelection('test', failedResult),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Enum Completeness', () {
      test('all ParseStatus enum values are handled by extension methods', () {
        // This test ensures that if new ParseStatus values are added,
        // the extension methods will handle them appropriately
        
        for (final status in ParseStatus.values) {
          // isSoftFail should return a boolean for all values
          expect(status.isSoftFail, isA<bool>());
          
          // toPendingStage should return either a PendingStage or null for all values
          final pendingStage = status.toPendingStage();
          expect(pendingStage == null || pendingStage is PendingStage, isTrue);
          
          // requiresAmountConfirmation should return a boolean for all values
          expect(status.requiresAmountConfirmation, isA<bool>());
        }
      });

      test('defensive testing - adding new ParseStatus values would be caught', () {
        // This test documents the current enum values and will fail if new ones are added
        // without updating the extension methods
        
        final expectedEnumValues = {
          ParseStatus.success,
          ParseStatus.needsCategory,
          ParseStatus.needsType,
          ParseStatus.needsAmountConfirmation,
          ParseStatus.failed,
          ParseStatus.missingAmount,
          ParseStatus.ambiguousAmount,
        };

        expect(
          ParseStatus.values.toSet(),
          equals(expectedEnumValues),
          reason: 'If this test fails, new ParseStatus values have been added. '
                  'Update the extension methods and this test accordingly.',
        );
      });
    });
  });
}
