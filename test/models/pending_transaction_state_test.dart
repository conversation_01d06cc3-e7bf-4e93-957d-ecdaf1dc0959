import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/pending_transaction_state.dart';
import '../../lib/models/parse_result.dart';
import '../../lib/models/transaction_model.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('PendingTransactionState Tests', () {
    late Transaction testTransaction;
    late ParseResult needsTypeResult;
    late ParseResult needsCategoryResult;
    late ParseResult needsAmountConfirmationResult;
    late ParseResult ambiguousAmountResult;
    late ParseResult missingAmountResult;
    late ParseResult successResult;
    late ParseResult failedResult;
    const String testOriginalText = 'spent 25 on lunch';

    setUp(() {
      testTransaction = TestHelpers.createTestTransaction(
        amount: 25.0,
        type: TransactionType.expense,
        description: 'lunch',
        currencyCode: 'USD',
      );

      needsTypeResult = ParseResult.needsType(testTransaction);
      needsCategoryResult = ParseResult.needsCategory(testTransaction);
      needsAmountConfirmationResult = ParseResult.needsAmountConfirmation(
        testTransaction,
        [25.0, 50.0],
        ['25', '50'],
      );
      ambiguousAmountResult = ParseResult.ambiguousAmount(
        testTransaction,
        [25.0, 50.0],
        ['25', '50'],
      );
      missingAmountResult = ParseResult.missingAmount(testTransaction);
      successResult = ParseResult.success(testTransaction);
      failedResult = ParseResult.failed(testTransaction, 'Test error');
    });

    group('Factory Constructor Validation', () {
      test('forTypeSelection succeeds with needsType status', () {
        final pendingState = PendingTransactionState.forTypeSelection(
          testOriginalText,
          needsTypeResult,
        );

        expect(pendingState.parseResult, equals(needsTypeResult));
        expect(pendingState.originalText, equals(testOriginalText));
        expect(pendingState.stage, equals(PendingStage.typeSelection));
      });

      test('forTypeSelection throws ArgumentError with other statuses', () {
        expect(
          () => PendingTransactionState.forTypeSelection(
            testOriginalText,
            needsCategoryResult,
          ),
          throwsA(isA<ArgumentError>()),
        );

        expect(
          () => PendingTransactionState.forTypeSelection(
            testOriginalText,
            successResult,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('forCategorySelection succeeds with needsCategory status', () {
        final pendingState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        expect(pendingState.parseResult, equals(needsCategoryResult));
        expect(pendingState.originalText, equals(testOriginalText));
        expect(pendingState.stage, equals(PendingStage.categorySelection));
      });

      test('forCategorySelection throws ArgumentError with other statuses', () {
        expect(
          () => PendingTransactionState.forCategorySelection(
            testOriginalText,
            needsTypeResult,
          ),
          throwsA(isA<ArgumentError>()),
        );

        expect(
          () => PendingTransactionState.forCategorySelection(
            testOriginalText,
            failedResult,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('forAmountConfirmation succeeds with needsAmountConfirmation status', () {
        final pendingState = PendingTransactionState.forAmountConfirmation(
          testOriginalText,
          needsAmountConfirmationResult,
        );

        expect(pendingState.parseResult, equals(needsAmountConfirmationResult));
        expect(pendingState.originalText, equals(testOriginalText));
        expect(pendingState.stage, equals(PendingStage.amountConfirmation));
      });

      test('forAmountConfirmation succeeds with ambiguousAmount status', () {
        final pendingState = PendingTransactionState.forAmountConfirmation(
          testOriginalText,
          ambiguousAmountResult,
        );

        expect(pendingState.parseResult, equals(ambiguousAmountResult));
        expect(pendingState.originalText, equals(testOriginalText));
        expect(pendingState.stage, equals(PendingStage.amountConfirmation));
      });

      test('forAmountConfirmation throws ArgumentError with other statuses', () {
        expect(
          () => PendingTransactionState.forAmountConfirmation(
            testOriginalText,
            needsTypeResult,
          ),
          throwsA(isA<ArgumentError>()),
        );

        expect(
          () => PendingTransactionState.forAmountConfirmation(
            testOriginalText,
            successResult,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('forMissingAmount succeeds with missingAmount status', () {
        final pendingState = PendingTransactionState.forMissingAmount(
          testOriginalText,
          missingAmountResult,
        );

        expect(pendingState.parseResult, equals(missingAmountResult));
        expect(pendingState.originalText, equals(testOriginalText));
        expect(pendingState.stage, equals(PendingStage.missingAmount));
      });

      test('forMissingAmount throws ArgumentError with other statuses', () {
        expect(
          () => PendingTransactionState.forMissingAmount(
            testOriginalText,
            needsCategoryResult,
          ),
          throwsA(isA<ArgumentError>()),
        );

        expect(
          () => PendingTransactionState.forMissingAmount(
            testOriginalText,
            successResult,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Getter Behavior', () {
      test('stage getters return correct boolean values', () {
        final typeSelection = PendingTransactionState.forTypeSelection(
          testOriginalText,
          needsTypeResult,
        );
        expect(typeSelection.isTypeSelection, isTrue);
        expect(typeSelection.isCategorySelection, isFalse);
        expect(typeSelection.isAmountConfirmation, isFalse);
        expect(typeSelection.isMissingAmount, isFalse);

        final categorySelection = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );
        expect(categorySelection.isTypeSelection, isFalse);
        expect(categorySelection.isCategorySelection, isTrue);
        expect(categorySelection.isAmountConfirmation, isFalse);
        expect(categorySelection.isMissingAmount, isFalse);

        final amountConfirmation = PendingTransactionState.forAmountConfirmation(
          testOriginalText,
          needsAmountConfirmationResult,
        );
        expect(amountConfirmation.isTypeSelection, isFalse);
        expect(amountConfirmation.isCategorySelection, isFalse);
        expect(amountConfirmation.isAmountConfirmation, isTrue);
        expect(amountConfirmation.isMissingAmount, isFalse);

        final missingAmount = PendingTransactionState.forMissingAmount(
          testOriginalText,
          missingAmountResult,
        );
        expect(missingAmount.isTypeSelection, isFalse);
        expect(missingAmount.isCategorySelection, isFalse);
        expect(missingAmount.isAmountConfirmation, isFalse);
        expect(missingAmount.isMissingAmount, isTrue);
      });

      test('transaction getter returns parseResult.transaction', () {
        final pendingState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        expect(pendingState.transaction, equals(needsCategoryResult.transaction));
        expect(pendingState.transaction, equals(testTransaction));
      });

      test('candidateAmounts and candidateTexts getters work correctly', () {
        final pendingState = PendingTransactionState.forAmountConfirmation(
          testOriginalText,
          needsAmountConfirmationResult,
        );

        expect(pendingState.candidateAmounts, equals([25.0, 50.0]));
        expect(pendingState.candidateTexts, equals(['25', '50']));

        final pendingStateWithoutCandidates = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        expect(pendingStateWithoutCandidates.candidateAmounts, isNull);
        expect(pendingStateWithoutCandidates.candidateTexts, isNull);
      });

      test('ambiguityType getter returns parseResult.ambiguityType', () {
        final pendingState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        expect(pendingState.ambiguityType, equals(needsCategoryResult.ambiguityType));
      });
    });

    group('Equality and HashCode', () {
      test('two instances with identical fields are equal and have same hashCode', () {
        final pendingState1 = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );
        final pendingState2 = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        expect(pendingState1, equals(pendingState2));
        expect(pendingState1.hashCode, equals(pendingState2.hashCode));
      });

      test('instances with different parseResult are not equal', () {
        final pendingState1 = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );
        final pendingState2 = PendingTransactionState.forTypeSelection(
          testOriginalText,
          needsTypeResult,
        );

        expect(pendingState1, isNot(equals(pendingState2)));
      });

      test('instances with different originalText are not equal', () {
        final pendingState1 = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );
        final pendingState2 = PendingTransactionState.forCategorySelection(
          'different text',
          needsCategoryResult,
        );

        expect(pendingState1, isNot(equals(pendingState2)));
      });

      test('instances with different stage are not equal', () {
        // This test is implicitly covered by different parseResult test above
        // since stage is determined by the factory constructor used
        final pendingState1 = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );
        final pendingState2 = PendingTransactionState.forAmountConfirmation(
          testOriginalText,
          needsAmountConfirmationResult,
        );

        expect(pendingState1, isNot(equals(pendingState2)));
      });

      test('hashCode consistency across multiple calls', () {
        final pendingState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        final hashCode1 = pendingState.hashCode;
        final hashCode2 = pendingState.hashCode;
        final hashCode3 = pendingState.hashCode;

        expect(hashCode1, equals(hashCode2));
        expect(hashCode2, equals(hashCode3));
      });
    });

    group('CopyWith Method', () {
      test('copyWith with single field changes preserves other fields', () {
        final originalState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        final newText = 'new original text';
        final copiedState = originalState.copyWith(originalText: newText);

        expect(copiedState.originalText, equals(newText));
        expect(copiedState.parseResult, equals(originalState.parseResult));
        expect(copiedState.stage, equals(originalState.stage));
      });

      test('copyWith with multiple field changes', () {
        final originalState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        final newText = 'new text';
        final newParseResult = needsTypeResult;
        final newStage = PendingStage.typeSelection;

        final copiedState = originalState.copyWith(
          originalText: newText,
          parseResult: newParseResult,
          stage: newStage,
        );

        expect(copiedState.originalText, equals(newText));
        expect(copiedState.parseResult, equals(newParseResult));
        expect(copiedState.stage, equals(newStage));
      });

      test('copyWith with no parameters returns equal instance', () {
        final originalState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        final copiedState = originalState.copyWith();

        expect(copiedState, equals(originalState));
        expect(copiedState.originalText, equals(originalState.originalText));
        expect(copiedState.parseResult, equals(originalState.parseResult));
        expect(copiedState.stage, equals(originalState.stage));
      });
    });

    group('Immutability', () {
      test('all fields are final and cannot be modified after construction', () {
        final pendingState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        // This test verifies that the fields are final by attempting to access them
        // If they weren't final, this would be a compile-time error
        expect(pendingState.parseResult, isNotNull);
        expect(pendingState.originalText, isNotNull);
        expect(pendingState.stage, isNotNull);
      });

      test('copyWith returns a new instance rather than modifying the original', () {
        final originalState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        final copiedState = originalState.copyWith(originalText: 'new text');

        expect(identical(originalState, copiedState), isFalse);
        expect(originalState.originalText, equals(testOriginalText));
        expect(copiedState.originalText, equals('new text'));
      });
    });

    group('ToString Method', () {
      test('toString includes all relevant fields', () {
        final pendingState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );

        final stringRepresentation = pendingState.toString();

        expect(stringRepresentation, contains('PendingTransactionState'));
        expect(stringRepresentation, contains('parseResult'));
        expect(stringRepresentation, contains('originalText'));
        expect(stringRepresentation, contains('stage'));
        expect(stringRepresentation, contains(testOriginalText));
        expect(stringRepresentation, contains('categorySelection'));
      });

      test('toString with different stages and data combinations', () {
        final typeSelection = PendingTransactionState.forTypeSelection(
          testOriginalText,
          needsTypeResult,
        );
        expect(typeSelection.toString(), contains('typeSelection'));

        final amountConfirmation = PendingTransactionState.forAmountConfirmation(
          'amount text',
          needsAmountConfirmationResult,
        );
        expect(amountConfirmation.toString(), contains('amountConfirmation'));
        expect(amountConfirmation.toString(), contains('amount text'));

        final missingAmount = PendingTransactionState.forMissingAmount(
          'missing amount text',
          missingAmountResult,
        );
        expect(missingAmount.toString(), contains('missingAmount'));
        expect(missingAmount.toString(), contains('missing amount text'));
      });
    });

    group('Edge Cases', () {
      test('with empty originalText', () {
        final pendingState = PendingTransactionState.forCategorySelection(
          '',
          needsCategoryResult,
        );

        expect(pendingState.originalText, equals(''));
        expect(pendingState.parseResult, equals(needsCategoryResult));
        expect(pendingState.stage, equals(PendingStage.categorySelection));
      });

      test('with null optional fields in ParseResult', () {
        final parseResultWithNulls = ParseResult.needsCategory(testTransaction);
        final pendingState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          parseResultWithNulls,
        );

        expect(pendingState.candidateAmounts, isNull);
        expect(pendingState.candidateTexts, isNull);
        expect(pendingState.ambiguityType, isNull);
      });

      test('with various transaction types and currencies', () {
        final incomeTransaction = TestHelpers.createTestTransaction(
          type: TransactionType.income,
          currencyCode: 'EUR',
          amount: 1500.0,
        );
        final incomeParseResult = ParseResult.needsCategory(incomeTransaction);

        final pendingState = PendingTransactionState.forCategorySelection(
          'salary 1500 euros',
          incomeParseResult,
        );

        expect(pendingState.transaction.type, equals(TransactionType.income));
        expect(pendingState.transaction.currencyCode, equals('EUR'));
        expect(pendingState.transaction.amount, equals(1500.0));
      });

      test('with long originalText and complex ParseResult data', () {
        final longText = 'This is a very long transaction description that contains multiple words and details about the transaction that was performed by the user in the chat interface';
        final complexParseResult = ParseResult.ambiguousAmount(
          testTransaction,
          [10.0, 20.0, 30.0, 40.0, 50.0],
          ['ten', 'twenty', 'thirty', 'forty', 'fifty'],
        );

        final pendingState = PendingTransactionState.forAmountConfirmation(
          longText,
          complexParseResult,
        );

        expect(pendingState.originalText, equals(longText));
        expect(pendingState.candidateAmounts, hasLength(5));
        expect(pendingState.candidateTexts, hasLength(5));
        expect(pendingState.candidateAmounts, contains(30.0));
        expect(pendingState.candidateTexts, contains('thirty'));
      });
    });

    group('Integration with ParseResult', () {
      test('all data from ParseResult is accessible through convenience getters', () {
        final pendingState = PendingTransactionState.forAmountConfirmation(
          testOriginalText,
          needsAmountConfirmationResult,
        );

        expect(pendingState.transaction, equals(needsAmountConfirmationResult.transaction));
        expect(pendingState.candidateAmounts, equals(needsAmountConfirmationResult.candidateAmounts));
        expect(pendingState.candidateTexts, equals(needsAmountConfirmationResult.candidateTexts));
        expect(pendingState.ambiguityType, equals(needsAmountConfirmationResult.ambiguityType));
      });

      test('with ParseResult instances created by TestHelpers', () {
        final helperSuccessResult = TestHelpers.createSuccessParseResult();
        final helperNeedsCategoryResult = TestHelpers.createNeedsCategoryParseResult();

        final pendingState = PendingTransactionState.forCategorySelection(
          testOriginalText,
          helperNeedsCategoryResult,
        );

        expect(pendingState.parseResult, equals(helperNeedsCategoryResult));
        expect(pendingState.transaction, equals(helperNeedsCategoryResult.transaction));

        // Verify that success result would throw error (as expected)
        expect(
          () => PendingTransactionState.forCategorySelection(
            testOriginalText,
            helperSuccessResult,
          ),
          throwsA(isA<ArgumentError>()),
        );
      });

      test('with different ParseResult factory constructors', () {
        final successTransaction = TestHelpers.createTestTransaction();
        final successResult = ParseResult.success(successTransaction);
        final needsCategoryResult = ParseResult.needsCategory(successTransaction);
        final needsTypeResult = ParseResult.needsType(successTransaction);
        final failedResult = ParseResult.failed(successTransaction, 'Test error');

        // Test that each factory constructor works with its corresponding ParseResult
        final categoryPending = PendingTransactionState.forCategorySelection(
          testOriginalText,
          needsCategoryResult,
        );
        expect(categoryPending.stage, equals(PendingStage.categorySelection));

        final typePending = PendingTransactionState.forTypeSelection(
          testOriginalText,
          needsTypeResult,
        );
        expect(typePending.stage, equals(PendingStage.typeSelection));

        // Test that mismatched combinations throw errors
        expect(
          () => PendingTransactionState.forCategorySelection(testOriginalText, successResult),
          throwsA(isA<ArgumentError>()),
        );
        expect(
          () => PendingTransactionState.forTypeSelection(testOriginalText, failedResult),
          throwsA(isA<ArgumentError>()),
        );
      });
    });
  });
}
