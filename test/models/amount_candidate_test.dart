import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/amount_candidate.dart';

void main() {
  group('AmountCandidate Tests', () {
    group('Object Creation', () {
      test('should create AmountCandidate with all properties', () {
        final candidate = AmountCandidate(
          amount: 100.50,
          currency: 'USD',
          start: 5,
          end: 11,
          sourceText: '100.50',
          source: AmountSource.mlKit,
        );

        expect(candidate.amount, equals(100.50));
        expect(candidate.currency, equals('USD'));
        expect(candidate.start, equals(5));
        expect(candidate.end, equals(11));
        expect(candidate.sourceText, equals('100.50'));
        expect(candidate.source, equals(AmountSource.mlKit));
      });

      test('should create AmountCandidate with null currency', () {
        final candidate = AmountCandidate(
          amount: 50.0,
          currency: null,
          start: 0,
          end: 2,
          sourceText: '50',
          source: AmountSource.rawNumberFinder,
        );

        expect(candidate.amount, equals(50.0));
        expect(candidate.currency, isNull);
        expect(candidate.source, equals(AmountSource.rawNumberFinder));
      });
    });

    group('Factory Constructors', () {
      test('should create AmountCandidate from ML Kit', () {
        final candidate = AmountCandidate.fromMLKit(
          amount: 200.0,
          currency: 'EUR',
          start: 10,
          end: 15,
          sourceText: '€200',
        );

        expect(candidate.amount, equals(200.0));
        expect(candidate.currency, equals('EUR'));
        expect(candidate.source, equals(AmountSource.mlKit));
        expect(candidate.sourceText, equals('€200'));
      });

      test('should create AmountCandidate from raw number finder', () {
        final candidate = AmountCandidate.fromRawNumberFinder(
          amount: 1000.0,
          currency: 'VND',
          start: 20,
          end: 24,
          sourceText: '1000',
        );

        expect(candidate.amount, equals(1000.0));
        expect(candidate.currency, equals('VND'));
        expect(candidate.source, equals(AmountSource.rawNumberFinder));
        expect(candidate.sourceText, equals('1000'));
      });
    });

    group('Equality Comparison', () {
      test('should be equal when amount and position overlap', () {
        final candidate1 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final candidate2 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 6,
          end: 9,
          sourceText: '100',
          source: AmountSource.rawNumberFinder,
        );

        expect(candidate1, equals(candidate2));
      });

      test('should not be equal when amounts differ significantly', () {
        final candidate1 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final candidate2 = AmountCandidate(
          amount: 200.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '200',
          source: AmountSource.mlKit,
        );

        expect(candidate1, isNot(equals(candidate2)));
      });

      test('should be equal with small floating point differences', () {
        final candidate1 = AmountCandidate(
          amount: 100.001,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final candidate2 = AmountCandidate(
          amount: 100.002,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.rawNumberFinder,
        );

        expect(candidate1, equals(candidate2));
      });

      test('should not be equal when positions do not overlap', () {
        final candidate1 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final candidate2 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 15,
          end: 18,
          sourceText: '100',
          source: AmountSource.rawNumberFinder,
        );

        expect(candidate1, isNot(equals(candidate2)));
      });
    });

    group('Hash Code', () {
      test('should have same hash code for equal objects', () {
        final candidate1 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final candidate2 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 6,
          end: 9,
          sourceText: '100',
          source: AmountSource.rawNumberFinder,
        );

        expect(candidate1.hashCode, equals(candidate2.hashCode));
      });
    });

    group('String Representation', () {
      test('should provide readable toString with currency', () {
        final candidate = AmountCandidate(
          amount: 100.50,
          currency: 'USD',
          start: 5,
          end: 11,
          sourceText: '\$100.50',
          source: AmountSource.mlKit,
        );

        final result = candidate.toString();
        expect(result, contains('100.5'));
        expect(result, contains('USD'));
        expect(result, contains('5-11'));
        expect(result, contains('\$100.50'));
        expect(result, contains('mlKit'));
      });

      test('should provide readable toString without currency', () {
        final candidate = AmountCandidate(
          amount: 50.0,
          currency: null,
          start: 0,
          end: 2,
          sourceText: '50',
          source: AmountSource.rawNumberFinder,
        );

        final result = candidate.toString();
        expect(result, contains('50.0'));
        expect(result, contains('0-2'));
        expect(result, contains('50'));
        expect(result, contains('rawNumberFinder'));
        expect(result, isNot(contains('null')));
      });
    });

    group('Map Conversion', () {
      test('should convert to Map correctly', () {
        final candidate = AmountCandidate(
          amount: 100.50,
          currency: 'USD',
          start: 5,
          end: 11,
          sourceText: '\$100.50',
          source: AmountSource.mlKit,
        );

        final map = candidate.toMap();
        expect(map['amount'], equals(100.50));
        expect(map['currency'], equals('USD'));
        expect(map['start'], equals(5));
        expect(map['end'], equals(11));
        expect(map['sourceText'], equals('\$100.50'));
        expect(map['source'], equals('mlKit'));
      });

      test('should convert from Map correctly', () {
        final map = {
          'amount': 200.0,
          'currency': 'EUR',
          'start': 10,
          'end': 15,
          'sourceText': '€200',
          'source': 'rawNumberFinder',
        };

        final candidate = AmountCandidate.fromMap(map);
        expect(candidate.amount, equals(200.0));
        expect(candidate.currency, equals('EUR'));
        expect(candidate.start, equals(10));
        expect(candidate.end, equals(15));
        expect(candidate.sourceText, equals('€200'));
        expect(candidate.source, equals(AmountSource.rawNumberFinder));
      });

      test('should handle null currency in Map conversion', () {
        final candidate = AmountCandidate(
          amount: 50.0,
          currency: null,
          start: 0,
          end: 2,
          sourceText: '50',
          source: AmountSource.mlKit,
        );

        final map = candidate.toMap();
        expect(map['currency'], isNull);

        final reconstructed = AmountCandidate.fromMap(map);
        expect(reconstructed.currency, isNull);
        expect(reconstructed, equals(candidate));
      });
    });

    group('Copy With', () {
      test('should create copy with updated fields', () {
        final original = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final copy = original.copyWith(
          amount: 200.0,
          currency: 'EUR',
        );

        expect(copy.amount, equals(200.0));
        expect(copy.currency, equals('EUR'));
        expect(copy.start, equals(5)); // Unchanged
        expect(copy.end, equals(8)); // Unchanged
        expect(copy.sourceText, equals('100')); // Unchanged
        expect(copy.source, equals(AmountSource.mlKit)); // Unchanged
      });

      test('should create identical copy when no fields updated', () {
        final original = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final copy = original.copyWith();
        expect(copy, equals(original));
      });
    });

    group('Utility Methods', () {
      test('should correctly identify same amount', () {
        final candidate1 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final candidate2 = AmountCandidate(
          amount: 100.005,
          currency: 'EUR',
          start: 15,
          end: 18,
          sourceText: '100',
          source: AmountSource.rawNumberFinder,
        );

        expect(candidate1.hasSameAmount(candidate2), isTrue);
      });

      test('should correctly identify different amounts', () {
        final candidate1 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final candidate2 = AmountCandidate(
          amount: 200.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '200',
          source: AmountSource.rawNumberFinder,
        );

        expect(candidate1.hasSameAmount(candidate2), isFalse);
      });

      test('should correctly identify overlapping positions', () {
        final candidate1 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 10,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final candidate2 = AmountCandidate(
          amount: 200.0,
          currency: 'USD',
          start: 8,
          end: 12,
          sourceText: '200',
          source: AmountSource.rawNumberFinder,
        );

        expect(candidate1.overlapsPosition(candidate2), isTrue);
      });

      test('should correctly identify non-overlapping positions', () {
        final candidate1 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final candidate2 = AmountCandidate(
          amount: 200.0,
          currency: 'USD',
          start: 15,
          end: 18,
          sourceText: '200',
          source: AmountSource.rawNumberFinder,
        );

        expect(candidate1.overlapsPosition(candidate2), isFalse);
      });

      test('should extract text span correctly', () {
        final candidate = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 6,
          end: 9,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final fullText = 'Spent 100 on coffee';
        final span = candidate.getTextSpan(fullText);
        expect(span, equals('100'));
      });

      test('should fallback to sourceText for invalid positions', () {
        final candidate = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: -1,
          end: 100,
          sourceText: '\$100',
          source: AmountSource.mlKit,
        );

        final fullText = 'Short text';
        final span = candidate.getTextSpan(fullText);
        expect(span, equals('\$100'));
      });
    });

    group('Edge Cases', () {
      test('should handle zero amount', () {
        final candidate = AmountCandidate(
          amount: 0.0,
          currency: 'USD',
          start: 0,
          end: 1,
          sourceText: '0',
          source: AmountSource.mlKit,
        );

        expect(candidate.amount, equals(0.0));
        expect(candidate.toString(), contains('0.0'));
      });

      test('should handle very large amounts', () {
        final candidate = AmountCandidate(
          amount: 1000000000.0,
          currency: 'USD',
          start: 0,
          end: 2,
          sourceText: '1B',
          source: AmountSource.rawNumberFinder,
        );

        expect(candidate.amount, equals(1000000000.0));
        expect(candidate.toString(), contains('1000000000.0'));
      });

      test('should handle empty source text', () {
        final candidate = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 0,
          end: 3,
          sourceText: '',
          source: AmountSource.mlKit,
        );

        expect(candidate.sourceText, equals(''));
        expect(candidate.toString(), contains('""'));
      });
    });

    group('Collections Behavior', () {
      test('should work correctly in List operations', () {
        final candidate1 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final candidate2 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 6,
          end: 9,
          sourceText: '100',
          source: AmountSource.rawNumberFinder,
        );

        final list = [candidate1];
        expect(list.contains(candidate2), isTrue);
      });

      test('should work correctly in Set operations', () {
        final candidate1 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 5,
          end: 8,
          sourceText: '100',
          source: AmountSource.mlKit,
        );

        final candidate2 = AmountCandidate(
          amount: 100.0,
          currency: 'USD',
          start: 6,
          end: 9,
          sourceText: '100',
          source: AmountSource.rawNumberFinder,
        );

        final set = {candidate1, candidate2};
        expect(set.length, equals(1)); // Should be deduplicated
      });
    });
  });
}
