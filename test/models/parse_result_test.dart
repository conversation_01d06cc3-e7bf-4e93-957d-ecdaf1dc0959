import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/parse_result.dart';
import '../../lib/models/transaction_model.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('ParseResult Tests', () {
    late Transaction testTransaction;

    setUp(() {
      testTransaction = TestHelpers.createTestTransaction(
        amount: 25.50,
        type: TransactionType.expense,
        description: 'Test transaction',
        currencyCode: 'USD',
      );
    });

    group('Factory Constructors', () {
      test('ParseResult.success should create successful result', () {
        final result = ParseResult.success(testTransaction);

        expect(result.transaction, equals(testTransaction));
        expect(result.status, equals(ParseStatus.success));
        expect(result.needsCategorySelection, isFalse);
        expect(result.needsTypeSelection, isFalse);
        expect(result.error, isNull);
        expect(result.isSuccess, isTrue);
        expect(result.requiresUserInput, isFalse);
        expect(result.hasError, isFalse);
      });

      test('ParseResult.needsCategory should create result requiring category', () {
        final result = ParseResult.needsCategory(testTransaction);

        expect(result.transaction, equals(testTransaction));
        expect(result.status, equals(ParseStatus.needsCategory));
        expect(result.needsCategorySelection, isTrue);
        expect(result.needsTypeSelection, isFalse);
        expect(result.error, isNull);
        expect(result.isSuccess, isFalse); // Only ParseStatus.success is considered successful
        expect(result.requiresUserInput, isTrue);
        expect(result.hasError, isFalse);
      });

      test('ParseResult.needsType should create result requiring type selection', () {
        final result = ParseResult.needsType(testTransaction);

        expect(result.transaction, equals(testTransaction));
        expect(result.status, equals(ParseStatus.needsType));
        expect(result.needsCategorySelection, isFalse);
        expect(result.needsTypeSelection, isTrue);
        expect(result.error, isNull);
        expect(result.isSuccess, isFalse); // Only ParseStatus.success is considered successful
        expect(result.requiresUserInput, isTrue);
        expect(result.hasError, isFalse);
      });

      test('ParseResult.needsAmountConfirmation should create result requiring amount confirmation', () {
        final candidateAmounts = [100.0, 68.0, 2000000.0];
        final candidateTexts = ['100', '68', '2m'];
        final result = ParseResult.needsAmountConfirmation(testTransaction, candidateAmounts, candidateTexts);

        expect(result.transaction, equals(testTransaction));
        expect(result.status, equals(ParseStatus.needsAmountConfirmation));
        expect(result.candidateAmounts, equals(candidateAmounts));
        expect(result.candidateTexts, equals(candidateTexts));
        expect(result.needsCategorySelection, isFalse);
        expect(result.needsTypeSelection, isFalse);
        expect(result.needsAmountConfirmation, isTrue);
        expect(result.error, isNull);
        expect(result.isSuccess, isFalse);
        expect(result.requiresUserInput, isTrue);
        expect(result.hasError, isFalse);
      });

      test('ParseResult.failed should create failed result', () {
        const errorMessage = 'Test error message';
        final result = ParseResult.failed(testTransaction, errorMessage);

        expect(result.transaction, equals(testTransaction));
        expect(result.status, equals(ParseStatus.failed));
        expect(result.needsCategorySelection, isFalse);
        expect(result.needsTypeSelection, isFalse);
        expect(result.error, equals(errorMessage));
        expect(result.isSuccess, isFalse);
        expect(result.requiresUserInput, isFalse); // Failed status does not require user input
        expect(result.hasError, isTrue);
      });

      test('ParseResult.missingAmount should create missing amount result', () {
        final result = ParseResult.missingAmount(testTransaction);

        expect(result.transaction, equals(testTransaction));
        expect(result.status, equals(ParseStatus.missingAmount));
        expect(result.needsCategorySelection, isFalse);
        expect(result.needsTypeSelection, isFalse);
        expect(result.needsAmountConfirmation, isFalse);
        expect(result.needsMissingAmountHandling, isTrue);
        expect(result.needsAmbiguousAmountHandling, isFalse);
        expect(result.error, isNull);
        expect(result.isSuccess, isFalse);
        expect(result.requiresUserInput, isTrue);
        expect(result.hasError, isFalse);
      });

      test('ParseResult.ambiguousAmount should create ambiguous amount result', () {
        final candidateAmounts = [100.0, 200.0];
        final candidateTexts = ['100', '200'];
        final result = ParseResult.ambiguousAmount(testTransaction, candidateAmounts, candidateTexts);

        expect(result.transaction, equals(testTransaction));
        expect(result.status, equals(ParseStatus.ambiguousAmount));
        expect(result.needsCategorySelection, isFalse);
        expect(result.needsTypeSelection, isFalse);
        expect(result.needsAmountConfirmation, isFalse);
        expect(result.needsMissingAmountHandling, isFalse);
        expect(result.needsAmbiguousAmountHandling, isTrue);
        expect(result.candidateAmounts, equals(candidateAmounts));
        expect(result.candidateTexts, equals(candidateTexts));
        expect(result.error, isNull);
        expect(result.isSuccess, isFalse);
        expect(result.requiresUserInput, isTrue);
        expect(result.hasError, isFalse);
      });
    });

    group('Ambiguity Type Tests', () {
      test('should store and retrieve ambiguity type correctly', () {
        const ambiguityType = 'missing_amount';
        final result = ParseResult.missingAmount(testTransaction, ambiguityType: ambiguityType);

        expect(result.ambiguityType, equals(ambiguityType));
      });

      test('should handle null ambiguity type for backward compatibility', () {
        final result = ParseResult.success(testTransaction);

        expect(result.ambiguityType, isNull);
      });

      test('should include ambiguity type in toString when present', () {
        const ambiguityType = 'ambiguous_amount';
        final result = ParseResult.ambiguousAmount(
          testTransaction,
          [100.0, 200.0],
          ['100', '200'],
          ambiguityType: ambiguityType
        );

        final stringRepresentation = result.toString();
        expect(stringRepresentation, contains('ambiguityType: $ambiguityType'));
      });

      test('should include null ambiguity type in toString when not present', () {
        final result = ParseResult.success(testTransaction);

        final stringRepresentation = result.toString();
        expect(stringRepresentation, contains('ambiguityType: null'));
      });

      test('should support ambiguity type in all factory constructors', () {
        const ambiguityType = AmbiguityType.missingAmount;

        final successResult = ParseResult.success(testTransaction, ambiguityType: ambiguityType);
        expect(successResult.ambiguityType, equals(ambiguityType));

        final needsCategoryResult = ParseResult.needsCategory(testTransaction, ambiguityType: ambiguityType);
        expect(needsCategoryResult.ambiguityType, equals(ambiguityType));

        final needsTypeResult = ParseResult.needsType(testTransaction, ambiguityType: ambiguityType);
        expect(needsTypeResult.ambiguityType, equals(ambiguityType));

        final needsAmountResult = ParseResult.needsAmountConfirmation(
          testTransaction, [100.0], ['100'], ambiguityType: ambiguityType);
        expect(needsAmountResult.ambiguityType, equals(ambiguityType));

        final failedResult = ParseResult.failed(testTransaction, 'Error', ambiguityType: ambiguityType);
        expect(failedResult.ambiguityType, equals(ambiguityType));

        final missingAmountResult = ParseResult.missingAmount(testTransaction, ambiguityType: ambiguityType);
        expect(missingAmountResult.ambiguityType, equals(ambiguityType));

        final ambiguousAmountResult = ParseResult.ambiguousAmount(
          testTransaction, [100.0], ['100'], ambiguityType: ambiguityType);
        expect(ambiguousAmountResult.ambiguityType, equals(ambiguityType));
      });

      test('should validate ambiguity type and throw error for invalid values', () {
        // Test invalid ambiguity type
        expect(
          () => ParseResult.success(testTransaction, ambiguityType: 'invalid_type'),
          throwsA(isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            contains('Invalid ambiguityType: invalid_type'),
          )),
        );

        // Test another factory constructor with invalid type
        expect(
          () => ParseResult.needsCategory(testTransaction, ambiguityType: 'another_invalid_type'),
          throwsA(isA<ArgumentError>().having(
            (e) => e.message,
            'message',
            contains('Invalid ambiguityType: another_invalid_type'),
          )),
        );

        // Test that null is valid (no exception should be thrown)
        expect(
          () => ParseResult.success(testTransaction, ambiguityType: null),
          returnsNormally,
        );

        // Test that valid types work (no exception should be thrown)
        expect(
          () => ParseResult.success(testTransaction, ambiguityType: AmbiguityType.ambiguousAmount),
          returnsNormally,
        );
        expect(
          () => ParseResult.success(testTransaction, ambiguityType: AmbiguityType.ambiguousType),
          returnsNormally,
        );
        expect(
          () => ParseResult.success(testTransaction, ambiguityType: AmbiguityType.ambiguousCategory),
          returnsNormally,
        );
      });
    });

    group('AmbiguityType Constants', () {
      test('should have correct constant values', () {
        expect(AmbiguityType.missingAmount, equals('missing_amount'));
        expect(AmbiguityType.ambiguousAmount, equals('ambiguous_amount'));
        expect(AmbiguityType.ambiguousType, equals('ambiguous_type'));
        expect(AmbiguityType.ambiguousCategory, equals('ambiguous_category'));
      });

      test('should validate ambiguity types correctly', () {
        // Valid types
        expect(AmbiguityType.isValid(null), isTrue);
        expect(AmbiguityType.isValid(AmbiguityType.missingAmount), isTrue);
        expect(AmbiguityType.isValid(AmbiguityType.ambiguousAmount), isTrue);
        expect(AmbiguityType.isValid(AmbiguityType.ambiguousType), isTrue);
        expect(AmbiguityType.isValid(AmbiguityType.ambiguousCategory), isTrue);

        // Invalid types
        expect(AmbiguityType.isValid('invalid_type'), isFalse);
        expect(AmbiguityType.isValid('unknown'), isFalse);
        expect(AmbiguityType.isValid(''), isFalse);
      });

      test('should contain all valid types in validTypes list', () {
        expect(AmbiguityType.validTypes, hasLength(4));
        expect(AmbiguityType.validTypes, contains(AmbiguityType.missingAmount));
        expect(AmbiguityType.validTypes, contains(AmbiguityType.ambiguousAmount));
        expect(AmbiguityType.validTypes, contains(AmbiguityType.ambiguousType));
        expect(AmbiguityType.validTypes, contains(AmbiguityType.ambiguousCategory));
      });
    });

    group('Helper Methods', () {
      test('isSuccess should return correct values', () {
        // Successful result
        final successResult = ParseResult.success(testTransaction);
        expect(successResult.isSuccess, isTrue);

        // Needs category - not considered successful (requires user input)
        final needsCategoryResult = ParseResult.needsCategory(testTransaction);
        expect(needsCategoryResult.isSuccess, isFalse);

        // Needs type - not considered successful (requires user input)
        final needsTypeResult = ParseResult.needsType(testTransaction);
        expect(needsTypeResult.isSuccess, isFalse);

        // Needs amount confirmation - not considered successful (requires user input)
        final needsAmountResult = ParseResult.needsAmountConfirmation(testTransaction, [100.0, 200.0], ['100', '200']);
        expect(needsAmountResult.isSuccess, isFalse);

        // Failed result
        final failedResult = ParseResult.failed(testTransaction, 'Error');
        expect(failedResult.isSuccess, isFalse);
      });

      test('requiresUserInput should return correct values', () {
        // Complete success - no user input needed
        final successResult = ParseResult.success(testTransaction);
        expect(successResult.requiresUserInput, isFalse);

        // Needs category selection
        final needsCategoryResult = ParseResult.needsCategory(testTransaction);
        expect(needsCategoryResult.requiresUserInput, isTrue);

        // Needs type selection
        final needsTypeResult = ParseResult.needsType(testTransaction);
        expect(needsTypeResult.requiresUserInput, isTrue);

        // Needs amount confirmation
        final needsAmountResult = ParseResult.needsAmountConfirmation(testTransaction, [100.0, 200.0], ['100', '200']);
        expect(needsAmountResult.requiresUserInput, isTrue);

        // Missing amount requires user input
        final missingAmountResult = ParseResult.missingAmount(testTransaction);
        expect(missingAmountResult.requiresUserInput, isTrue);

        // Ambiguous amount requires user input
        final ambiguousAmountResult = ParseResult.ambiguousAmount(testTransaction, [100.0, 200.0], ['100', '200']);
        expect(ambiguousAmountResult.requiresUserInput, isTrue);

        // Failed result does not require user input (according to implementation)
        final failedResult = ParseResult.failed(testTransaction, 'Error');
        expect(failedResult.requiresUserInput, isFalse);
      });

      test('hasError should return correct values', () {
        // Successful results
        final successResult = ParseResult.success(testTransaction);
        expect(successResult.hasError, isFalse);

        final needsCategoryResult = ParseResult.needsCategory(testTransaction);
        expect(needsCategoryResult.hasError, isFalse);

        final needsTypeResult = ParseResult.needsType(testTransaction);
        expect(needsTypeResult.hasError, isFalse);

        // Failed result
        final failedResult = ParseResult.failed(testTransaction, 'Error');
        expect(failedResult.hasError, isTrue);
      });

      test('needsMissingAmountHandling should return correct values', () {
        // Only missingAmount status should return true
        final missingAmountResult = ParseResult.missingAmount(testTransaction);
        expect(missingAmountResult.needsMissingAmountHandling, isTrue);

        // All other statuses should return false
        final successResult = ParseResult.success(testTransaction);
        expect(successResult.needsMissingAmountHandling, isFalse);

        final needsCategoryResult = ParseResult.needsCategory(testTransaction);
        expect(needsCategoryResult.needsMissingAmountHandling, isFalse);

        final needsTypeResult = ParseResult.needsType(testTransaction);
        expect(needsTypeResult.needsMissingAmountHandling, isFalse);

        final needsAmountResult = ParseResult.needsAmountConfirmation(testTransaction, [100.0], ['100']);
        expect(needsAmountResult.needsMissingAmountHandling, isFalse);

        final ambiguousAmountResult = ParseResult.ambiguousAmount(testTransaction, [100.0, 200.0], ['100', '200']);
        expect(ambiguousAmountResult.needsMissingAmountHandling, isFalse);

        final failedResult = ParseResult.failed(testTransaction, 'Error');
        expect(failedResult.needsMissingAmountHandling, isFalse);
      });

      test('needsAmbiguousAmountHandling should return correct values', () {
        // Only ambiguousAmount status should return true
        final ambiguousAmountResult = ParseResult.ambiguousAmount(testTransaction, [100.0, 200.0], ['100', '200']);
        expect(ambiguousAmountResult.needsAmbiguousAmountHandling, isTrue);

        // All other statuses should return false
        final successResult = ParseResult.success(testTransaction);
        expect(successResult.needsAmbiguousAmountHandling, isFalse);

        final needsCategoryResult = ParseResult.needsCategory(testTransaction);
        expect(needsCategoryResult.needsAmbiguousAmountHandling, isFalse);

        final needsTypeResult = ParseResult.needsType(testTransaction);
        expect(needsTypeResult.needsAmbiguousAmountHandling, isFalse);

        final needsAmountResult = ParseResult.needsAmountConfirmation(testTransaction, [100.0], ['100']);
        expect(needsAmountResult.needsAmbiguousAmountHandling, isFalse);

        final missingAmountResult = ParseResult.missingAmount(testTransaction);
        expect(missingAmountResult.needsAmbiguousAmountHandling, isFalse);

        final failedResult = ParseResult.failed(testTransaction, 'Error');
        expect(failedResult.needsAmbiguousAmountHandling, isFalse);
      });

      test('needsCategorySelection should return correct values', () {
        final successResult = ParseResult.success(testTransaction);
        expect(successResult.needsCategorySelection, isFalse);

        final needsCategoryResult = ParseResult.needsCategory(testTransaction);
        expect(needsCategoryResult.needsCategorySelection, isTrue);

        final needsTypeResult = ParseResult.needsType(testTransaction);
        expect(needsTypeResult.needsCategorySelection, isFalse);

        final failedResult = ParseResult.failed(testTransaction, 'Error');
        expect(failedResult.needsCategorySelection, isFalse);
      });

      test('needsTypeSelection should return correct values', () {
        final successResult = ParseResult.success(testTransaction);
        expect(successResult.needsTypeSelection, isFalse);

        final needsCategoryResult = ParseResult.needsCategory(testTransaction);
        expect(needsCategoryResult.needsTypeSelection, isFalse);

        final needsTypeResult = ParseResult.needsType(testTransaction);
        expect(needsTypeResult.needsTypeSelection, isTrue);

        final failedResult = ParseResult.failed(testTransaction, 'Error');
        expect(failedResult.needsTypeSelection, isFalse);
      });
    });

    group('toString Method', () {
      test('should return properly formatted string for success', () {
        final result = ParseResult.success(testTransaction);
        final stringRepresentation = result.toString();

        expect(stringRepresentation, contains('ParseResult'));
        expect(stringRepresentation, contains('transaction:'));
        expect(stringRepresentation, contains('status: ParseStatus.success'));
        expect(stringRepresentation, contains('error: null'));
      });

      test('should return properly formatted string for needsCategory', () {
        final result = ParseResult.needsCategory(testTransaction);
        final stringRepresentation = result.toString();

        expect(stringRepresentation, contains('ParseResult'));
        expect(stringRepresentation, contains('status: ParseStatus.needsCategory'));
        expect(stringRepresentation, contains('error: null'));
      });

      test('should return properly formatted string for needsType', () {
        final result = ParseResult.needsType(testTransaction);
        final stringRepresentation = result.toString();

        expect(stringRepresentation, contains('ParseResult'));
        expect(stringRepresentation, contains('status: ParseStatus.needsType'));
        expect(stringRepresentation, contains('error: null'));
      });

      test('should return properly formatted string for failed', () {
        const errorMessage = 'Test error';
        final result = ParseResult.failed(testTransaction, errorMessage);
        final stringRepresentation = result.toString();

        expect(stringRepresentation, contains('ParseResult'));
        expect(stringRepresentation, contains('status: ParseStatus.failed'));
        expect(stringRepresentation, contains('error: $errorMessage'));
      });
    });

    group('State Validation', () {
      test('successful result should have consistent state', () {
        final result = ParseResult.success(testTransaction);

        expect(result.status, equals(ParseStatus.success));
        expect(result.isSuccess, isTrue);
        expect(result.hasError, isFalse);
        expect(result.error, isNull);
        expect(result.needsCategorySelection, isFalse);
        expect(result.needsTypeSelection, isFalse);
        expect(result.requiresUserInput, isFalse);
      });

      test('needsCategory result should have consistent state', () {
        final result = ParseResult.needsCategory(testTransaction);

        expect(result.status, equals(ParseStatus.needsCategory));
        expect(result.isSuccess, isFalse); // Only ParseStatus.success is considered successful
        expect(result.hasError, isFalse);
        expect(result.error, isNull);
        expect(result.needsCategorySelection, isTrue);
        expect(result.needsTypeSelection, isFalse);
        expect(result.requiresUserInput, isTrue);
      });

      test('needsType result should have consistent state', () {
        final result = ParseResult.needsType(testTransaction);

        expect(result.status, equals(ParseStatus.needsType));
        expect(result.isSuccess, isFalse); // Only ParseStatus.success is considered successful
        expect(result.hasError, isFalse);
        expect(result.error, isNull);
        expect(result.needsCategorySelection, isFalse);
        expect(result.needsTypeSelection, isTrue);
        expect(result.requiresUserInput, isTrue);
      });

      test('failed result should have consistent state', () {
        const errorMessage = 'Test error';
        final result = ParseResult.failed(testTransaction, errorMessage);

        expect(result.status, equals(ParseStatus.failed));
        expect(result.isSuccess, isFalse);
        expect(result.hasError, isTrue);
        expect(result.error, equals(errorMessage));
        expect(result.needsCategorySelection, isFalse);
        expect(result.needsTypeSelection, isFalse);
        expect(result.requiresUserInput, isFalse); // Failed status does not require user input according to implementation
      });
    });

    group('Edge Cases', () {
      test('should handle transaction with minimal data', () {
        final minimalTransaction = Transaction(
          id: 'test-id',
          amount: 0.0,
          type: TransactionType.expense,
          categoryId: '',
          date: DateTime.now(),
          description: '',
        );
        
        final result = ParseResult.success(minimalTransaction);
        expect(result.transaction, equals(minimalTransaction));
        expect(result.isSuccess, isTrue);
      });

      test('should handle empty error message', () {
        final result = ParseResult.failed(testTransaction, '');
        
        expect(result.error, equals(''));
        expect(result.hasError, isTrue);
        expect(result.isSuccess, isFalse);
      });

      test('should handle very long error message', () {
        final longError = 'A' * 1000; // Very long error message
        final result = ParseResult.failed(testTransaction, longError);
        
        expect(result.error, equals(longError));
        expect(result.hasError, isTrue);
        expect(result.isSuccess, isFalse);
      });

      test('should handle transaction with special characters in description', () {
        final specialTransaction = TestHelpers.createTestTransaction(
          description: 'Test with émojis 🍕💰 and special chars !@#\$%^&*()',
        );
        
        final result = ParseResult.success(specialTransaction);
        expect(result.transaction.description, contains('émojis'));
        expect(result.transaction.description, contains('🍕💰'));
        expect(result.isSuccess, isTrue);
      });
    });

    group('Different Transaction Types', () {
      test('should work with expense transactions', () {
        final expenseTransaction = TestHelpers.createTestTransaction(
          type: TransactionType.expense,
          amount: 50.0,
        );
        
        final result = ParseResult.success(expenseTransaction);
        expect(result.transaction.type, equals(TransactionType.expense));
        expect(result.isSuccess, isTrue);
      });

      test('should work with income transactions', () {
        final incomeTransaction = TestHelpers.createTestTransaction(
          type: TransactionType.income,
          amount: 1000.0,
        );
        
        final result = ParseResult.needsCategory(incomeTransaction);
        expect(result.transaction.type, equals(TransactionType.income));
        expect(result.requiresUserInput, isTrue);
      });

      test('should work with loan transactions', () {
        final loanTransaction = TestHelpers.createTestTransaction(
          type: TransactionType.loan,
          amount: 200.0,
        );
        
        final result = ParseResult.failed(loanTransaction, 'Could not find category');
        expect(result.transaction.type, equals(TransactionType.loan));
        expect(result.hasError, isTrue);
      });
    });

    group('Different Currencies', () {
      test('should work with various currencies', () {
        final currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY', 'INR'];
        
        for (final currency in currencies) {
          final transaction = TestHelpers.createTestTransaction(
            currencyCode: currency,
            amount: 100.0,
          );
          
          final result = ParseResult.success(transaction);
          expect(result.transaction.currencyCode, equals(currency));
          expect(result.isSuccess, isTrue);
        }
      });
    });

    group('ParseStatus Enum Tests', () {
      test('should have all expected enum values', () {
        expect(ParseStatus.values, hasLength(7));
        expect(ParseStatus.values, contains(ParseStatus.success));
        expect(ParseStatus.values, contains(ParseStatus.needsCategory));
        expect(ParseStatus.values, contains(ParseStatus.needsType));
        expect(ParseStatus.values, contains(ParseStatus.needsAmountConfirmation));
        expect(ParseStatus.values, contains(ParseStatus.failed));
        expect(ParseStatus.values, contains(ParseStatus.missingAmount));
        expect(ParseStatus.values, contains(ParseStatus.ambiguousAmount));
      });

      test('should handle enum transitions correctly', () {
        // Test all possible status combinations
        final statuses = [
          ParseStatus.success,
          ParseStatus.needsCategory,
          ParseStatus.needsType,
          ParseStatus.needsAmountConfirmation,
          ParseStatus.failed,
          ParseStatus.missingAmount,
          ParseStatus.ambiguousAmount,
        ];

        for (final status in statuses) {
          final result = ParseResult(
            transaction: testTransaction,
            status: status,
            error: status == ParseStatus.failed ? 'Test error' : null,
            candidateAmounts: status == ParseStatus.ambiguousAmount ? [100.0, 200.0] : null,
            candidateTexts: status == ParseStatus.ambiguousAmount ? ['100', '200'] : null,
          );

          expect(result.status, equals(status));
          expect(result.isSuccess, equals(status == ParseStatus.success)); // Only success status is considered successful
          expect(result.hasError, equals(status == ParseStatus.failed));
          expect(result.requiresUserInput, equals(status == ParseStatus.needsCategory ||
                                                  status == ParseStatus.needsType ||
                                                  status == ParseStatus.needsAmountConfirmation ||
                                                  status == ParseStatus.missingAmount ||
                                                  status == ParseStatus.ambiguousAmount));
        }
      });
    });

    group('Type Disambiguation Scenarios', () {
      test('should handle partial transaction for type selection', () {
        // Create a partial transaction with amount and currency but unclear type
        final partialTransaction = Transaction(
          id: 'partial-id',
          amount: 200000.0, // 200k VND
          type: TransactionType.expense, // Default type, will be updated
          categoryId: 'other',
          date: DateTime.now(),
          description: 'to travel',
          currencyCode: 'VND',
        );

        final result = ParseResult.needsType(partialTransaction);

        expect(result.status, equals(ParseStatus.needsType));
        expect(result.needsTypeSelection, isTrue);
        expect(result.needsCategorySelection, isFalse);
        expect(result.transaction.amount, equals(200000.0));
        expect(result.transaction.currencyCode, equals('VND'));
        expect(result.transaction.description, equals('to travel'));
        expect(result.isSuccess, isFalse); // Only ParseStatus.success is considered successful
        expect(result.requiresUserInput, isTrue);
      });

      test('should handle type disambiguation with different currencies', () {
        final currencies = ['VND', 'USD', 'EUR', 'JPY'];
        final amounts = [200000.0, 25.50, 100.0, 5000.0];

        for (int i = 0; i < currencies.length; i++) {
          final transaction = Transaction(
            id: 'test-$i',
            amount: amounts[i],
            type: TransactionType.expense,
            categoryId: 'other',
            date: DateTime.now(),
            description: 'unclear transaction type',
            currencyCode: currencies[i],
          );

          final result = ParseResult.needsType(transaction);

          expect(result.status, equals(ParseStatus.needsType));
          expect(result.transaction.currencyCode, equals(currencies[i]));
          expect(result.transaction.amount, equals(amounts[i]));
          expect(result.needsTypeSelection, isTrue);
        }
      });
    });

    group('Using Test Helpers', () {
      test('should work with helper methods', () {
        final successResult = TestHelpers.createSuccessParseResult();
        expect(successResult.isSuccess, isTrue);
        expect(successResult.requiresUserInput, isFalse);
        expect(successResult.status, equals(ParseStatus.success));

        final needsCategoryResult = TestHelpers.createNeedsCategoryParseResult();
        expect(needsCategoryResult.isSuccess, isFalse); // Only ParseStatus.success is considered successful
        expect(needsCategoryResult.requiresUserInput, isTrue);
        expect(needsCategoryResult.status, equals(ParseStatus.needsCategory));

        final failedResult = TestHelpers.createFailedParseResult();
        expect(failedResult.isSuccess, isFalse);
        expect(failedResult.hasError, isTrue);
        expect(failedResult.status, equals(ParseStatus.failed));
      });

      test('should validate results using helper matchers', () {
        final successResult = TestHelpers.createSuccessParseResult();
        expect(ParseResultMatchers.isSuccess(successResult), isTrue);
        expect(ParseResultMatchers.needsCategory(successResult), isFalse);
        expect(ParseResultMatchers.isFailed(successResult), isFalse);

        final needsCategoryResult = TestHelpers.createNeedsCategoryParseResult();
        expect(ParseResultMatchers.isSuccess(needsCategoryResult), isFalse);
        expect(ParseResultMatchers.needsCategory(needsCategoryResult), isTrue);
        expect(ParseResultMatchers.isFailed(needsCategoryResult), isFalse);

        final failedResult = TestHelpers.createFailedParseResult();
        expect(ParseResultMatchers.isSuccess(failedResult), isFalse);
        expect(ParseResultMatchers.needsCategory(failedResult), isFalse);
        expect(ParseResultMatchers.isFailed(failedResult), isTrue);
      });
    });
  });
}
