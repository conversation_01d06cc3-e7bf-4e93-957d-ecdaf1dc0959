import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/category_suggestion.dart';

void main() {
  group('CategorySuggestion', () {
    test('should create CategorySuggestion with all properties', () {
      const suggestion = CategorySuggestion(
        categoryId: 'food',
        confidence: 0.85,
        matchReason: 'Keyword match: restaurant',
        source: CategorySuggestionSource.keyword,
      );

      expect(suggestion.categoryId, equals('food'));
      expect(suggestion.confidence, equals(0.85));
      expect(suggestion.matchReason, equals('Keyword match: restaurant'));
      expect(suggestion.source, equals(CategorySuggestionSource.keyword));
    });

    test('should serialize to JSON correctly', () {
      const suggestion = CategorySuggestion(
        categoryId: 'transport',
        confidence: 0.92,
        matchReason: 'Learned association',
        source: CategorySuggestionSource.learned,
      );

      final json = suggestion.toJson();

      expect(json['categoryId'], equals('transport'));
      expect(json['confidence'], equals(0.92));
      expect(json['matchReason'], equals('Learned association'));
      expect(json['source'], equals('learned'));
    });

    test('should deserialize from JSON correctly', () {
      final json = {
        'categoryId': 'shopping',
        'confidence': 0.75,
        'matchReason': 'Recent usage',
        'source': 'recent',
      };

      final suggestion = CategorySuggestion.fromJson(json);

      expect(suggestion.categoryId, equals('shopping'));
      expect(suggestion.confidence, equals(0.75));
      expect(suggestion.matchReason, equals('Recent usage'));
      expect(suggestion.source, equals(CategorySuggestionSource.recent));
    });

    test('should handle confidence bounds correctly', () {
      const highConfidence = CategorySuggestion(
        categoryId: 'test1',
        confidence: 1.0,
        matchReason: 'Perfect match',
        source: CategorySuggestionSource.learned,
      );

      const lowConfidence = CategorySuggestion(
        categoryId: 'test2',
        confidence: 0.0,
        matchReason: 'Weak match',
        source: CategorySuggestionSource.frequency,
      );

      expect(highConfidence.confidence, equals(1.0));
      expect(lowConfidence.confidence, equals(0.0));
    });

    test('should support equality comparison', () {
      const suggestion1 = CategorySuggestion(
        categoryId: 'food',
        confidence: 0.8,
        matchReason: 'Test',
        source: CategorySuggestionSource.keyword,
      );

      const suggestion2 = CategorySuggestion(
        categoryId: 'food',
        confidence: 0.8,
        matchReason: 'Test',
        source: CategorySuggestionSource.keyword,
      );

      const suggestion3 = CategorySuggestion(
        categoryId: 'transport',
        confidence: 0.8,
        matchReason: 'Test',
        source: CategorySuggestionSource.keyword,
      );

      expect(suggestion1, equals(suggestion2));
      expect(suggestion1, isNot(equals(suggestion3)));
    });
  });

  group('CategorySuggestionResult', () {
    test('should create empty result', () {
      const result = CategorySuggestionResult(suggestions: []);

      expect(result.suggestions, isEmpty);
      expect(result.fallbackCategoryId, isNull);
    });

    test('should create result with suggestions and fallback', () {
      const suggestions = [
        CategorySuggestion(
          categoryId: 'food',
          confidence: 0.9,
          matchReason: 'High confidence match',
          source: CategorySuggestionSource.learned,
        ),
        CategorySuggestion(
          categoryId: 'transport',
          confidence: 0.7,
          matchReason: 'Medium confidence match',
          source: CategorySuggestionSource.keyword,
        ),
      ];

      const result = CategorySuggestionResult(
        suggestions: suggestions,
        fallbackCategoryId: 'food',
      );

      expect(result.suggestions, hasLength(2));
      expect(result.fallbackCategoryId, equals('food'));
      expect(result.suggestions.first.categoryId, equals('food'));
      expect(result.suggestions.last.categoryId, equals('transport'));
    });

    test('should serialize to JSON correctly', () {
      const suggestions = [
        CategorySuggestion(
          categoryId: 'food',
          confidence: 0.85,
          matchReason: 'Test match',
          source: CategorySuggestionSource.keyword,
        ),
      ];

      const result = CategorySuggestionResult(
        suggestions: suggestions,
        fallbackCategoryId: 'food',
      );

      final json = result.toJson();

      expect(json['suggestions'], isA<List>());
      expect(json['suggestions'], hasLength(1));
      expect(json['fallbackCategoryId'], equals('food'));
    });

    test('should deserialize from JSON correctly', () {
      final json = {
        'suggestions': [
          {
            'categoryId': 'shopping',
            'confidence': 0.75,
            'matchReason': 'Keyword match',
            'source': 'keyword',
          }
        ],
        'fallbackCategoryId': 'shopping',
      };

      final result = CategorySuggestionResult.fromJson(json);

      expect(result.suggestions, hasLength(1));
      expect(result.suggestions.first.categoryId, equals('shopping'));
      expect(result.fallbackCategoryId, equals('shopping'));
    });
  });

  group('CategorySuggestionSource', () {
    test('should convert enum to string correctly', () {
      expect(CategorySuggestionSource.learned.toString(), equals('CategorySuggestionSource.learned'));
      expect(CategorySuggestionSource.keyword.toString(), equals('CategorySuggestionSource.keyword'));
      expect(CategorySuggestionSource.recent.toString(), equals('CategorySuggestionSource.recent'));
      expect(CategorySuggestionSource.frequency.toString(), equals('CategorySuggestionSource.frequency'));
    });

    test('should have all expected enum values', () {
      const expectedValues = [
        CategorySuggestionSource.learned,
        CategorySuggestionSource.keyword,
        CategorySuggestionSource.recent,
        CategorySuggestionSource.frequency,
      ];

      expect(CategorySuggestionSource.values, equals(expectedValues));
    });
  });
}
