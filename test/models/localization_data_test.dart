import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/models/localization_data.dart';

void main() {
  group('LocalizationData', () {
    group('fromJson', () {
      test('should create LocalizationData from valid JSON', () {
        final json = {
          'locale': 'en-US',
          'decimal_separator': '.',
          'thousands_separator': ',',
          'expense_keywords': ['spent', 'paid', 'bought'],
          'income_keywords': ['received', 'earned', 'income'],
          'loan_keywords': ['borrowed', 'lent', 'loan'],
          'currency_symbols': ['\$', '€', '£'],
          'special_patterns': {
            'for_keyword': r'\bfor\b',
            'payment_exclusion': r'pay(?!ment received)',
          },
        };

        final localizationData = LocalizationData.fromJson(json);

        expect(localizationData.locale, equals('en-US'));
        expect(localizationData.decimalSeparator, equals('.'));
        expect(localizationData.thousandsSeparator, equals(','));
        expect(localizationData.expenseKeywords, equals(['spent', 'paid', 'bought']));
        expect(localizationData.incomeKeywords, equals(['received', 'earned', 'income']));
        expect(localizationData.loanKeywords, equals(['borrowed', 'lent', 'loan']));
        expect(localizationData.currencySymbols, equals(['\$', '€', '£']));
        expect(localizationData.specialPatterns['for_keyword'], equals(r'\bfor\b'));
        expect(localizationData.specialPatterns['payment_exclusion'], equals(r'pay(?!ment received)'));
      });

      test('should throw FormatException for missing locale', () {
        final json = {
          'decimal_separator': '.',
          'thousands_separator': ',',
          'expense_keywords': ['spent'],
          'income_keywords': ['received'],
          'loan_keywords': ['borrowed'],
          'currency_symbols': ['\$'],
        };

        expect(
          () => LocalizationData.fromJson(json),
          throwsA(isA<FormatException>().having(
            (e) => e.message,
            'message',
            contains('Missing or empty locale field'),
          )),
        );
      });

      test('should throw FormatException for empty locale', () {
        final json = {
          'locale': '',
          'decimal_separator': '.',
          'thousands_separator': ',',
          'expense_keywords': ['spent'],
          'income_keywords': ['received'],
          'loan_keywords': ['borrowed'],
          'currency_symbols': ['\$'],
        };

        expect(
          () => LocalizationData.fromJson(json),
          throwsA(isA<FormatException>().having(
            (e) => e.message,
            'message',
            contains('Missing or empty locale field'),
          )),
        );
      });

      test('should throw FormatException for missing decimal_separator', () {
        final json = {
          'locale': 'en-US',
          'thousands_separator': ',',
          'expense_keywords': ['spent'],
          'income_keywords': ['received'],
          'loan_keywords': ['borrowed'],
          'currency_symbols': ['\$'],
        };

        expect(
          () => LocalizationData.fromJson(json),
          throwsA(isA<FormatException>().having(
            (e) => e.message,
            'message',
            contains('Missing or empty decimal_separator field'),
          )),
        );
      });

      test('should throw FormatException for missing thousands_separator', () {
        final json = {
          'locale': 'en-US',
          'decimal_separator': '.',
          'expense_keywords': ['spent'],
          'income_keywords': ['received'],
          'loan_keywords': ['borrowed'],
          'currency_symbols': ['\$'],
        };

        expect(
          () => LocalizationData.fromJson(json),
          throwsA(isA<FormatException>().having(
            (e) => e.message,
            'message',
            contains('Missing or empty thousands_separator field'),
          )),
        );
      });

      test('should throw FormatException for missing expense_keywords', () {
        final json = {
          'locale': 'en-US',
          'decimal_separator': '.',
          'thousands_separator': ',',
          'income_keywords': ['received'],
          'loan_keywords': ['borrowed'],
          'currency_symbols': ['\$'],
        };

        expect(
          () => LocalizationData.fromJson(json),
          throwsA(isA<FormatException>().having(
            (e) => e.message,
            'message',
            contains('Missing expense_keywords field'),
          )),
        );
      });

      test('should throw FormatException for empty expense_keywords array', () {
        final json = {
          'locale': 'en-US',
          'decimal_separator': '.',
          'thousands_separator': ',',
          'expense_keywords': <String>[],
          'income_keywords': ['received'],
          'loan_keywords': ['borrowed'],
          'currency_symbols': ['\$'],
        };

        expect(
          () => LocalizationData.fromJson(json),
          throwsA(isA<FormatException>().having(
            (e) => e.message,
            'message',
            contains('expense_keywords cannot be empty'),
          )),
        );
      });

      test('should throw FormatException for non-string in expense_keywords', () {
        final json = {
          'locale': 'en-US',
          'decimal_separator': '.',
          'thousands_separator': ',',
          'expense_keywords': ['spent', 123, 'bought'],
          'income_keywords': ['received'],
          'loan_keywords': ['borrowed'],
          'currency_symbols': ['\$'],
        };

        expect(
          () => LocalizationData.fromJson(json),
          throwsA(isA<FormatException>().having(
            (e) => e.message,
            'message',
            contains('expense_keywords[1] must be a string'),
          )),
        );
      });

      test('should throw FormatException for empty string in expense_keywords', () {
        final json = {
          'locale': 'en-US',
          'decimal_separator': '.',
          'thousands_separator': ',',
          'expense_keywords': ['spent', '', 'bought'],
          'income_keywords': ['received'],
          'loan_keywords': ['borrowed'],
          'currency_symbols': ['\$'],
        };

        expect(
          () => LocalizationData.fromJson(json),
          throwsA(isA<FormatException>().having(
            (e) => e.message,
            'message',
            contains('expense_keywords[1] cannot be empty'),
          )),
        );
      });

      test('should handle empty special_patterns', () {
        final json = {
          'locale': 'en-US',
          'decimal_separator': '.',
          'thousands_separator': ',',
          'expense_keywords': ['spent'],
          'income_keywords': ['received'],
          'loan_keywords': ['borrowed'],
          'currency_symbols': ['\$'],
        };

        final localizationData = LocalizationData.fromJson(json);
        expect(localizationData.specialPatterns, isEmpty);
      });

      test('should filter non-string values from special_patterns', () {
        final json = {
          'locale': 'en-US',
          'decimal_separator': '.',
          'thousands_separator': ',',
          'expense_keywords': ['spent'],
          'income_keywords': ['received'],
          'loan_keywords': ['borrowed'],
          'currency_symbols': ['\$'],
          'special_patterns': {
            'valid_pattern': r'\bfor\b',
            'invalid_pattern': 123,
            'another_valid': r'test',
          },
        };

        final localizationData = LocalizationData.fromJson(json);
        expect(localizationData.specialPatterns, hasLength(2));
        expect(localizationData.specialPatterns['valid_pattern'], equals(r'\bfor\b'));
        expect(localizationData.specialPatterns['another_valid'], equals(r'test'));
        expect(localizationData.specialPatterns.containsKey('invalid_pattern'), isFalse);
      });
    });

    group('fromJsonString', () {
      test('should create LocalizationData from valid JSON string', () {
        const jsonString = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": ["spent", "paid"],
          "income_keywords": ["received", "earned"],
          "loan_keywords": ["borrowed", "lent"],
          "currency_symbols": ["\$", "€"]
        }
        ''';

        final localizationData = LocalizationData.fromJsonString(jsonString);
        expect(localizationData.locale, equals('en-US'));
        expect(localizationData.expenseKeywords, equals(['spent', 'paid']));
      });

      test('should throw FormatException for invalid JSON string', () {
        const invalidJsonString = '{ "locale": "en-US", invalid }';

        expect(
          () => LocalizationData.fromJsonString(invalidJsonString),
          throwsA(isA<FormatException>().having(
            (e) => e.message,
            'message',
            contains('Failed to parse JSON string'),
          )),
        );
      });
    });

    group('toJson', () {
      test('should convert LocalizationData to JSON map', () {
        const localizationData = LocalizationData(
          locale: 'en-US',
          decimalSeparator: '.',
          thousandsSeparator: ',',
          expenseKeywords: ['spent', 'paid'],
          incomeKeywords: ['received', 'earned'],
          loanKeywords: ['borrowed', 'lent'],
          currencySymbols: ['\$', '€'],
          specialPatterns: {'for_keyword': r'\bfor\b'},
        );

        final json = localizationData.toJson();

        expect(json['locale'], equals('en-US'));
        expect(json['decimal_separator'], equals('.'));
        expect(json['thousands_separator'], equals(','));
        expect(json['expense_keywords'], equals(['spent', 'paid']));
        expect(json['income_keywords'], equals(['received', 'earned']));
        expect(json['loan_keywords'], equals(['borrowed', 'lent']));
        expect(json['currency_symbols'], equals(['\$', '€']));
        expect(json['special_patterns'], equals({'for_keyword': r'\bfor\b'}));
      });
    });

    group('equality and hashCode', () {
      test('should be equal for identical data', () {
        const data1 = LocalizationData(
          locale: 'en-US',
          decimalSeparator: '.',
          thousandsSeparator: ',',
          expenseKeywords: ['spent', 'paid'],
          incomeKeywords: ['received'],
          loanKeywords: ['borrowed'],
          currencySymbols: ['\$'],
          specialPatterns: {},
        );

        const data2 = LocalizationData(
          locale: 'en-US',
          decimalSeparator: '.',
          thousandsSeparator: ',',
          expenseKeywords: ['spent', 'paid'],
          incomeKeywords: ['received'],
          loanKeywords: ['borrowed'],
          currencySymbols: ['\$'],
          specialPatterns: {},
        );

        expect(data1, equals(data2));
        expect(data1.hashCode, equals(data2.hashCode));
      });

      test('should not be equal for different data', () {
        const data1 = LocalizationData(
          locale: 'en-US',
          decimalSeparator: '.',
          thousandsSeparator: ',',
          expenseKeywords: ['spent'],
          incomeKeywords: ['received'],
          loanKeywords: ['borrowed'],
          currencySymbols: ['\$'],
          specialPatterns: {},
        );

        const data2 = LocalizationData(
          locale: 'es-ES',
          decimalSeparator: ',',
          thousandsSeparator: '.',
          expenseKeywords: ['gasté'],
          incomeKeywords: ['recibido'],
          loanKeywords: ['prestado'],
          currencySymbols: ['€'],
          specialPatterns: {},
        );

        expect(data1, isNot(equals(data2)));
        expect(data1.hashCode, isNot(equals(data2.hashCode)));
      });
    });

    group('toString', () {
      test('should provide meaningful string representation', () {
        const localizationData = LocalizationData(
          locale: 'en-US',
          decimalSeparator: '.',
          thousandsSeparator: ',',
          expenseKeywords: ['spent', 'paid'],
          incomeKeywords: ['received'],
          loanKeywords: ['borrowed'],
          currencySymbols: ['\$'],
          specialPatterns: {},
        );

        final stringRepresentation = localizationData.toString();
        expect(stringRepresentation, contains('en-US'));
        expect(stringRepresentation, contains('expenseKeywords: 2'));
        expect(stringRepresentation, contains('incomeKeywords: 1'));
        expect(stringRepresentation, contains('loanKeywords: 1'));
      });
    });
  });
}
