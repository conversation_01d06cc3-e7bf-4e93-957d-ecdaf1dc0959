import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import '../../lib/services/parser/transaction_parsing_service.dart';
import '../../lib/services/parser/parsing_config.dart';
import '../../lib/services/parser/parse_logger.dart';
import '../../lib/models/transaction_model.dart';
import '../../lib/models/parse_result.dart';
import '../../lib/models/pending_transaction_state.dart';
import '../mocks/mock_storage_service.dart';
import '../mocks/mock_entity_extractor.dart';
import '../helpers/test_helpers.dart';

/// Memory-based log output for performance testing
class MemoryLogOutput extends LogOutput {
  final List<OutputEvent> events = [];

  @override
  void output(OutputEvent event) {
    events.add(event);
  }

  void clear() {
    events.clear();
  }

  List<String> get messages => events.map((e) => e.lines.join('\n')).toList();
}

/// Comprehensive performance regression test to validate that all 4 refinements
/// maintain or improve performance compared to baseline expectations.
/// 
/// Tests:
/// 1. Strategy Pattern Performance - Chain execution efficiency
/// 2. Configuration Impact - Custom config overhead
/// 3. Logging Performance - Structured logging impact
/// 4. State Management Performance - PendingTransactionState efficiency
/// 5. Memory Usage - Leak detection and consumption
/// 6. Concurrent Performance - Multiple operations scaling
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Complete Refinement Performance Tests', () {
    late MockStorageService mockStorage;
    late MockEntityExtractor mockExtractor;
    late MemoryLogOutput memoryOutput;

    setUp(() async {
      TransactionParsingService.resetInstance();
      mockStorage = MockStorageService();
      mockExtractor = MockEntityExtractor();
      
      // Set up memory log output for performance testing
      memoryOutput = MemoryLogOutput();
      ParseLogger.setLogOutput(memoryOutput);
      
      await mockStorage.init();
      mockStorage.setString('default_currency', 'USD');
      mockStorage.setString('categories', '[]');
    });

    tearDown(() {
      TransactionParsingService.resetInstance();
      memoryOutput.clear();
    });

    group('Strategy Pattern Performance', () {
      test('Single Parse Operation - Should complete within 100ms', () async {
        mockExtractor.setMockEntities([
          MockEntity(text: 'coffee', label: 'OTHER'),
          MockEntity(text: '5', label: 'MONEY'),
        ]);

        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        // Test various transaction complexities
        final testCases = [
          'coffee \$5',           // Simple
          'lunch 12.50',          // No currency symbol
          'gas station £45.20',   // Different currency
          'restaurant bill 25 or 30 pounds', // Complex/ambiguous
        ];

        for (final text in testCases) {
          memoryOutput.clear();
          
          final stopwatch = Stopwatch()..start();
          final result = await service.parseTransaction(text);
          stopwatch.stop();

          // Performance target: <100ms for simple transactions
          expect(stopwatch.elapsedMilliseconds, lessThan(100),
              reason: 'Parse operation took ${stopwatch.elapsedMilliseconds}ms for "$text"');
          
          expect(result, isNotNull);
        }
      });

      test('Strategy Chain Efficiency - Measure time per strategy', () async {
        mockExtractor.setMockEntities([
          MockEntity(text: 'coffee', label: 'OTHER'),
          MockEntity(text: '5', label: 'MONEY'),
        ]);

        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        memoryOutput.clear();
        
        final stopwatch = Stopwatch()..start();
        final result = await service.parseTransaction('coffee 5 dollars');
        stopwatch.stop();

        // Strategy chain should add minimal overhead
        expect(stopwatch.elapsedMilliseconds, lessThan(50));
        
        // Verify all strategies were considered (from logs)
        final messages = memoryOutput.messages;
        expect(messages.any((msg) => msg.contains('Checking learned associations')), isTrue);
        expect(messages.any((msg) => msg.contains('ML Kit')), isTrue);
      });

      test('Learned Association Fast Path - Should be fastest', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        // Learn an association first
        final learnedTransaction = TestHelpers.createTestTransaction(
          amount: 5.0,
          description: 'coffee',
          categoryId: 'food',
        );
        await service.learnCategory('coffee 5', learnedTransaction);

        memoryOutput.clear();

        // Measure learned association performance
        final stopwatch = Stopwatch()..start();
        final result = await service.parseTransaction('coffee 5');
        stopwatch.stop();

        // Learned associations should be very fast (<20ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(20),
            reason: 'Learned association took ${stopwatch.elapsedMilliseconds}ms');
        
        expect(result.isSuccess, isTrue);
        expect(result.transaction.amount, equals(5.0));
      });
    });

    group('Configuration Impact Performance', () {
      test('Default vs Custom Configuration - Overhead comparison', () async {
        mockExtractor.setMockEntities([
          MockEntity(text: 'coffee', label: 'OTHER'),
          MockEntity(text: '5', label: 'MONEY'),
        ]);

        // Test with default configuration
        final defaultService = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        final defaultStopwatch = Stopwatch()..start();
        await defaultService.parseTransaction('coffee 5 dollars');
        defaultStopwatch.stop();

        TransactionParsingService.resetInstance();

        // Test with custom configuration
        const customConfig = ParsingConfig(
          defaultCurrency: 'EUR',
          strictEmbeddedLetterThreshold: 2,
          abbreviationPattern: '[kKmM]',
        );

        final customService = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
          config: customConfig,
        );

        final customStopwatch = Stopwatch()..start();
        await customService.parseTransaction('coffee 5 dollars');
        customStopwatch.stop();

        // Custom configuration should not add significant overhead (max 10% increase)
        final overhead = customStopwatch.elapsedMilliseconds - defaultStopwatch.elapsedMilliseconds;
        final overheadPercentage = (overhead / defaultStopwatch.elapsedMilliseconds) * 100;
        
        expect(overheadPercentage, lessThan(10),
            reason: 'Custom config overhead: ${overheadPercentage.toStringAsFixed(1)}%');
      });
    });

    group('Logging Performance Impact', () {
      test('With vs Without Logging - Performance comparison', () async {
        mockExtractor.setMockEntities([
          MockEntity(text: 'coffee', label: 'OTHER'),
          MockEntity(text: '5', label: 'MONEY'),
        ]);

        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        // Test with logging enabled
        memoryOutput.clear();
        final withLoggingStopwatch = Stopwatch()..start();
        await service.parseTransaction('coffee 5 dollars');
        withLoggingStopwatch.stop();

        // Test with minimal logging (using a no-op output)
        ParseLogger.setLogOutput(_NoOpLogOutput());
        final withoutLoggingStopwatch = Stopwatch()..start();
        await service.parseTransaction('coffee 5 dollars');
        withoutLoggingStopwatch.stop();

        // Logging should add minimal overhead (<20% increase)
        final overhead = withLoggingStopwatch.elapsedMilliseconds - withoutLoggingStopwatch.elapsedMilliseconds;
        final overheadPercentage = (overhead / withoutLoggingStopwatch.elapsedMilliseconds) * 100;
        
        expect(overheadPercentage, lessThan(20),
            reason: 'Logging overhead: ${overheadPercentage.toStringAsFixed(1)}%');
      });
    });

    group('State Management Performance', () {
      test('PendingTransactionState Creation - Should be efficient', () async {
        mockExtractor.setMockEntities([
          MockEntity(text: 'restaurant', label: 'OTHER'),
          MockEntity(text: '25', label: 'MONEY'),
          MockEntity(text: '30', label: 'MONEY'),
        ]);

        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        final result = await service.parseTransaction('restaurant 25 or 30');
        
        // Measure state creation performance
        final stopwatch = Stopwatch()..start();
        
        final pendingState = PendingTransactionState.forAmountConfirmation(
          'restaurant 25 or 30',
          result,
        );
        
        stopwatch.stop();

        // State creation should be very fast (<5ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(5),
            reason: 'State creation took ${stopwatch.elapsedMilliseconds}ms');
        
        expect(pendingState.isAmountConfirmation, isTrue);
        expect(pendingState.candidateAmounts, isNotEmpty);
      });

      test('State Transitions - Multiple state changes performance', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        final transaction = TestHelpers.createTestTransaction();
        final parseResult = ParseResult.needsCategory(transaction);

        final stopwatch = Stopwatch()..start();

        // Simulate multiple state transitions
        final state1 = PendingTransactionState.forCategorySelection('test', parseResult);
        final state2 = PendingTransactionState.forAmountConfirmation('test', parseResult);
        final state3 = PendingTransactionState.forTypeSelection('test', parseResult);

        stopwatch.stop();

        // Multiple state transitions should be fast (<10ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(10));
        
        expect(state1.isCategorySelection, isTrue);
        expect(state2.isAmountConfirmation, isTrue);
        expect(state3.isTypeSelection, isTrue);
      });
    });

    group('Memory Usage and Leak Detection', () {
      test('Memory Stability - Multiple operations should not leak', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        // Perform many operations to detect memory leaks
        for (int i = 0; i < 100; i++) {
          mockExtractor.setMockEntities([
            MockEntity(text: 'test$i', label: 'OTHER'),
            MockEntity(text: '${i + 1}', label: 'MONEY'),
          ]);

          await service.parseTransaction('test$i ${i + 1}');
          
          // Clear logs to prevent memory accumulation in test
          memoryOutput.clear();
        }

        // If we reach here without memory issues, the test passes
        expect(true, isTrue);
      });

      test('Service Cleanup - Dispose should free resources', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        // Use the service
        await service.parseTransaction('test transaction 5');

        // Measure cleanup performance
        final stopwatch = Stopwatch()..start();
        service.dispose();
        stopwatch.stop();

        // Cleanup should be fast (<10ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(10));
      });
    });

    group('Concurrent Performance', () {
      test('Multiple Concurrent Operations - Should scale linearly', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        mockExtractor.setMockEntities([
          MockEntity(text: 'coffee', label: 'OTHER'),
          MockEntity(text: '5', label: 'MONEY'),
        ]);

        // Test concurrent operations
        final futures = <Future<ParseResult>>[];
        final stopwatch = Stopwatch()..start();

        for (int i = 0; i < 5; i++) {
          futures.add(service.parseTransaction('coffee ${i + 5}'));
        }

        final results = await Future.wait(futures);
        stopwatch.stop();

        // All operations should complete successfully
        expect(results.length, equals(5));
        for (final result in results) {
          expect(result, isNotNull);
        }

        // Concurrent operations should not take much longer than sequential
        // Target: <250ms for 5 concurrent operations (vs ~250ms sequential)
        expect(stopwatch.elapsedMilliseconds, lessThan(250));
      });
    });

    group('Performance Regression Detection', () {
      test('Baseline Performance Validation - All scenarios within targets', () async {
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        final performanceTargets = {
          'Simple transaction': {'text': 'coffee \$5', 'target': 50},
          'Complex transaction': {'text': 'restaurant bill 25 or 30', 'target': 100},
          'Currency conversion': {'text': 'lunch €12.50', 'target': 75},
          'Multiple amounts': {'text': 'shopping 45.99 or 50.00', 'target': 100},
        };

        for (final entry in performanceTargets.entries) {
          final scenario = entry.key;
          final data = entry.value;
          final text = data['text'] as String;
          final target = data['target'] as int;

          mockExtractor.setMockEntities([
            MockEntity(text: 'test', label: 'OTHER'),
            MockEntity(text: '5', label: 'MONEY'),
          ]);

          final stopwatch = Stopwatch()..start();
          final result = await service.parseTransaction(text);
          stopwatch.stop();

          expect(stopwatch.elapsedMilliseconds, lessThan(target),
              reason: '$scenario took ${stopwatch.elapsedMilliseconds}ms (target: ${target}ms)');
          
          expect(result, isNotNull);
        }
      });
    });
  });
}

/// No-op log output for performance testing without logging overhead
class _NoOpLogOutput extends LogOutput {
  @override
  void output(OutputEvent event) {
    // Do nothing - no-op for performance testing
  }
}
