import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/parser/learned_association_service.dart';
import '../../lib/services/parser/transaction_parsing_service.dart';
import '../../lib/models/transaction_model.dart';
import '../mocks/mock_storage_service.dart';
import '../mocks/mock_entity_extractor.dart';

void main() {
  group('Learning Performance Tests', () {
    late MockStorageService mockStorage;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
    });

    tearDown(() async {
      // Clean up services
      final learnedService = await LearnedAssociationService.getInstance(mockStorage);
      await learnedService.clearAllData();
      LearnedAssociationService.resetInstance();
      TransactionParsingService.resetInstance();
    });

    group('Learning Bypass Performance', () {
      test('should be faster with learned associations than full ML Kit parsing', () async {
        // Use mock extractor for reliable performance testing
        final mockExtractor = MockEntityExtractorFactory.createUSDScenario('\$5.50', 11, 16);
        final mlKitService = await TransactionParsingService.getInstance(mockStorage, entityExtractor: mockExtractor);
        const testText = 'coffee at starbucks';
        const categoryId = 'food';

        // Learn an association first
        await mlKitService.learnCategory(testText, categoryId);

        // Measure time for learned association lookup
        final stopwatchLearned = Stopwatch()..start();
        for (int i = 0; i < 100; i++) {
          await mlKitService.parseTransaction(testText);
        }
        stopwatchLearned.stop();
        final learnedTime = stopwatchLearned.elapsedMicroseconds;

        // Clear learned associations and measure ML Kit parsing time
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        await learnedService.clearAllData();

        final stopwatchMlKit = Stopwatch()..start();
        for (int i = 0; i < 100; i++) {
          await mlKitService.parseTransaction('unknown transaction text $i');
        }
        stopwatchMlKit.stop();
        final mlKitTime = stopwatchMlKit.elapsedMicroseconds;

        // Learned associations should be significantly faster
        expect(learnedTime, lessThan(mlKitTime));
        
        print('Learned association time: ${learnedTime}μs');
        print('ML Kit parsing time: ${mlKitTime}μs');
        print('Performance improvement: ${(mlKitTime / learnedTime).toStringAsFixed(2)}x faster');
      });

      test('should handle large numbers of learned associations efficiently', () async {
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        
        // Learn 1000 associations
        final stopwatchLearning = Stopwatch()..start();
        for (int i = 0; i < 1000; i++) {
          await learnedService.learn(
            'transaction $i',
            type: TransactionType.expense,
            categoryId: 'category_$i',
          );
        }
        stopwatchLearning.stop();

        // Measure lookup performance with many associations
        final stopwatchLookup = Stopwatch()..start();
        for (int i = 0; i < 100; i++) {
          await learnedService.getAssociation('transaction ${i * 10}');
        }
        stopwatchLookup.stop();

        // Should complete within reasonable time
        expect(stopwatchLearning.elapsedMilliseconds, lessThan(5000)); // 5 seconds
        expect(stopwatchLookup.elapsedMilliseconds, lessThan(1000)); // 1 second

        print('Learning 1000 associations: ${stopwatchLearning.elapsedMilliseconds}ms');
        print('100 lookups with 1000 associations: ${stopwatchLookup.elapsedMilliseconds}ms');
      });

      test('should maintain performance with text normalization', () async {
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        
        // Test texts with various complexities
        final testTexts = [
          'simple text',
          'Text with MIXED case and Numbers 123',
          'Complex! @#\$ Text & More... with Special Characters',
          'Very Long Transaction Description with Many Words and Details About the Purchase',
        ];

        for (final text in testTexts) {
          final stopwatch = Stopwatch()..start();
          
          // Learn and lookup 50 times
          for (int i = 0; i < 50; i++) {
            await learnedService.learn(
              '$text $i',
              type: TransactionType.expense,
              categoryId: 'test_category',
            );
            await learnedService.getAssociation('$text $i');
          }
          
          stopwatch.stop();
          
          // Should complete within reasonable time regardless of text complexity
          expect(stopwatch.elapsedMilliseconds, lessThan(2000)); // 2 seconds
          
          final displayText = text.length > 20 ? text.substring(0, 20) + '...' : text;
          print('Text: "$displayText" - Time: ${stopwatch.elapsedMilliseconds}ms');
        }
      });
    });

    group('Memory Usage Tests', () {
      test('should not cause memory leaks with repeated operations', () async {
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        
        // Perform many learn/clear cycles
        for (int cycle = 0; cycle < 10; cycle++) {
          // Learn many associations
          for (int i = 0; i < 100; i++) {
            await learnedService.learn(
              'cycle $cycle transaction $i',
              type: TransactionType.expense,
              categoryId: 'category_$i',
            );
          }
          
          // Clear all data
          await learnedService.clearAllData();
        }

        // Should complete without issues
        expect(true, isTrue); // If we reach here, no memory issues occurred
      });

      test('should handle concurrent operations safely', () async {
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        
        // Create multiple concurrent operations
        final futures = <Future>[];
        
        // Concurrent learning operations
        for (int i = 0; i < 20; i++) {
          futures.add(learnedService.learn(
            'concurrent transaction $i',
            type: TransactionType.expense,
            categoryId: 'category_$i',
          ));
        }
        
        // Concurrent lookup operations
        for (int i = 0; i < 20; i++) {
          futures.add(learnedService.getAssociation('concurrent transaction $i'));
        }
        
        // Wait for all operations to complete
        final stopwatch = Stopwatch()..start();
        await Future.wait(futures);
        stopwatch.stop();
        
        // Should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(3000)); // 3 seconds
        
        print('40 concurrent operations completed in: ${stopwatch.elapsedMilliseconds}ms');
      });
    });

    group('Storage Performance Tests', () {
      test('should efficiently persist and load large datasets', () async {
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        
        // Learn many associations
        final stopwatchLearning = Stopwatch()..start();
        for (int i = 0; i < 500; i++) {
          await learnedService.learn(
            'persistence test $i',
            type: i % 2 == 0 ? TransactionType.expense : TransactionType.income,
            categoryId: 'category_${i % 10}',
          );
        }
        stopwatchLearning.stop();

        // Force persistence by resetting service
        LearnedAssociationService.resetInstance();
        
        // Measure loading time
        final stopwatchLoading = Stopwatch()..start();
        final newService = await LearnedAssociationService.getInstance(mockStorage);
        final allAssociations = await newService.getAllAssociations();
        stopwatchLoading.stop();

        // Verify data integrity
        expect(allAssociations.length, equals(500));
        
        // Should load efficiently
        expect(stopwatchLoading.elapsedMilliseconds, lessThan(1000)); // 1 second
        
        print('Learning 500 associations: ${stopwatchLearning.elapsedMilliseconds}ms');
        print('Loading 500 associations: ${stopwatchLoading.elapsedMilliseconds}ms');
      });

      test('should handle storage errors gracefully without performance degradation', () async {
        final learnedService = await LearnedAssociationService.getInstance(mockStorage);
        
        // Corrupt storage
        await mockStorage.setString('learned_associations', 'invalid json');
        
        // Operations should still work (fallback to empty state)
        final stopwatch = Stopwatch()..start();
        
        for (int i = 0; i < 50; i++) {
          await learnedService.learn(
            'error test $i',
            type: TransactionType.expense,
            categoryId: 'test_category',
          );
          await learnedService.getAssociation('error test $i');
        }
        
        stopwatch.stop();
        
        // Should complete within reasonable time even with storage errors
        expect(stopwatch.elapsedMilliseconds, lessThan(2000)); // 2 seconds
        
        print('50 operations with storage errors: ${stopwatch.elapsedMilliseconds}ms');
      });
    });

    group('Integration Performance Tests', () {
      test('should maintain performance in end-to-end learning workflow', () async {
        // Use mock extractor for consistent performance testing
        final mockExtractor = MockEntityExtractorFactory.createUSDScenario('\$25.50', 0, 6);
        final mlKitService = await TransactionParsingService.getInstance(mockStorage, entityExtractor: mockExtractor);
        
        final testTransactions = [
          'coffee at starbucks',
          'uber ride downtown',
          'grocery shopping at walmart',
          'salary payment from company',
          'amazon online purchase',
        ];

        final stopwatch = Stopwatch()..start();
        
        // Simulate complete learning workflow
        for (int cycle = 0; cycle < 20; cycle++) {
          for (final text in testTransactions) {
            // Parse transaction (will use learned associations after first cycle)
            final result = await mlKitService.parseTransaction(text);
            
            // Learn category if needed
            if (result.isSuccess) {
              await mlKitService.learnCategory(text, 'learned_category_${cycle % 3}');
            }
          }
        }
        
        stopwatch.stop();
        
        // Should complete efficiently
        expect(stopwatch.elapsedMilliseconds, lessThan(10000)); // 10 seconds
        
        print('End-to-end learning workflow (100 transactions): ${stopwatch.elapsedMilliseconds}ms');
      });
    });
  });
}
