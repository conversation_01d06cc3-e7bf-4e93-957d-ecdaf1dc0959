import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/models/parse_result.dart';
import 'package:dreamflow/models/transaction_model.dart';
import '../helpers/test_helpers.dart';

void main() {
  group('Soft Fail Performance Tests', () {
    group('ParseResult Performance', () {
      test('should create ParseResult objects efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        // Create many ParseResult objects
        final results = <ParseResult>[];
        for (int i = 0; i < 10000; i++) {
          final transaction = TestHelpers.createTestTransaction(
            amount: i.toDouble(),
            description: 'Transaction $i',
          );
          
          // Alternate between different result types
          if (i % 4 == 0) {
            results.add(ParseResult.success(transaction));
          } else if (i % 4 == 1) {
            results.add(ParseResult.needsType(transaction));
          } else if (i % 4 == 2) {
            results.add(ParseResult.needsCategory(transaction));
          } else {
            results.add(ParseResult.failed('Error $i'));
          }
        }
        
        stopwatch.stop();
        
        // Should complete in reasonable time (under 1 second)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
        expect(results.length, equals(10000));
        
        // Verify all results are valid
        for (int i = 0; i < results.length; i++) {
          expect(results[i], isNotNull);
          expect(results[i].status, isNotNull);
        }
      });

      test('should handle rapid state transitions efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        // Simulate rapid state transitions
        for (int i = 0; i < 1000; i++) {
          final transaction = TestHelpers.createTestTransaction(
            amount: i.toDouble(),
          );
          
          // Transition through all states
          final needsTypeResult = ParseResult.needsType(transaction);
          expect(needsTypeResult.status, equals(ParseStatus.needsType));
          
          final withType = needsTypeResult.transaction.copyWith(
            type: TransactionType.expense,
          );
          final needsCategoryResult = ParseResult.needsCategory(withType);
          expect(needsCategoryResult.status, equals(ParseStatus.needsCategory));
          
          final withCategory = needsCategoryResult.transaction.copyWith(
            category: TestHelpers.createTestCategory(),
          );
          final successResult = ParseResult.success(withCategory);
          expect(successResult.status, equals(ParseStatus.success));
        }
        
        stopwatch.stop();
        
        // Should complete quickly (under 500ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
      });

      test('should handle large transaction objects efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        // Create transaction with large amounts of data
        final largeTransaction = TestHelpers.createTestTransaction(
          description: 'A' * 10000, // Very long description
          tags: List.generate(1000, (index) => 'tag$index'), // Many tags
        );
        
        // Perform operations on large transaction
        for (int i = 0; i < 100; i++) {
          final needsTypeResult = ParseResult.needsType(largeTransaction);
          final withType = needsTypeResult.transaction.copyWith(
            type: TransactionType.expense,
          );
          final successResult = ParseResult.success(withType);
          
          // Verify data integrity
          expect(successResult.transaction.description?.length, equals(10000));
          expect(successResult.transaction.tags?.length, equals(1000));
        }
        
        stopwatch.stop();
        
        // Should handle large objects efficiently (under 200ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(200));
      });

      test('should handle concurrent operations efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        // Simulate concurrent parse operations
        final futures = <Future<ParseResult>>[];
        
        for (int i = 0; i < 100; i++) {
          futures.add(Future.microtask(() {
            final transaction = TestHelpers.createTestTransaction(
              amount: i.toDouble(),
              description: 'Concurrent transaction $i',
            );
            
            // Simulate processing time
            return ParseResult.needsType(transaction);
          }));
        }
        
        // Wait for all operations to complete
        return Future.wait(futures).then((results) {
          stopwatch.stop();
          
          // Should complete quickly
          expect(stopwatch.elapsedMilliseconds, lessThan(100));
          expect(results.length, equals(100));
          
          // Verify all results
          for (int i = 0; i < results.length; i++) {
            expect(results[i].status, equals(ParseStatus.needsType));
            expect(results[i].transaction, isNotNull);
          }
        });
      });
    });

    group('ChatMessage Performance', () {
      test('should create ChatMessage objects efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        final messages = <ChatMessage>[];
        for (int i = 0; i < 5000; i++) {
          if (i % 3 == 0) {
            messages.add(ChatMessage.user(
              id: 'user-$i',
              text: 'User message $i',
              timestamp: DateTime.now(),
            ));
          } else if (i % 3 == 1) {
            messages.add(ChatMessage.system(
              id: 'system-$i',
              text: 'System message $i',
              timestamp: DateTime.now(),
            ));
          } else {
            messages.add(ChatMessage.systemWithQuickReplies(
              id: 'quick-$i',
              text: 'Quick reply message $i',
              timestamp: DateTime.now(),
              quickReplies: ['Option A', 'Option B', 'Option C'],
              quickReplyId: 'quick-$i',
            ));
          }
        }
        
        stopwatch.stop();
        
        // Should create messages efficiently (under 500ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
        expect(messages.length, equals(5000));
        
        // Verify message types are correct
        int userCount = 0, systemCount = 0, quickReplyCount = 0;
        for (final message in messages) {
          if (message.type == ChatMessageType.user) userCount++;
          else if (message.type == ChatMessageType.system) systemCount++;
          else if (message.type == ChatMessageType.systemWithQuickReplies) quickReplyCount++;
        }
        
        expect(userCount, greaterThan(1500));
        expect(systemCount, greaterThan(1500));
        expect(quickReplyCount, greaterThan(1500));
      });

      test('should handle messages with large quick reply lists efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        // Create messages with many quick reply options
        final messages = <ChatMessage>[];
        for (int i = 0; i < 100; i++) {
          final quickReplies = List.generate(100, (index) => 'Option $index');
          
          messages.add(ChatMessage.systemWithQuickReplies(
            id: 'large-quick-$i',
            text: 'Message with many options $i',
            timestamp: DateTime.now(),
            quickReplies: quickReplies,
            quickReplyId: 'large-quick-$i',
          ));
        }
        
        stopwatch.stop();
        
        // Should handle large quick reply lists efficiently (under 100ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
        expect(messages.length, equals(100));
        
        // Verify all messages have correct number of quick replies
        for (final message in messages) {
          expect(message.quickReplies?.length, equals(100));
        }
      });
    });

    group('Memory Usage Tests', () {
      test('should not leak memory during rapid object creation', () {
        // Create and discard many objects to test for memory leaks
        for (int batch = 0; batch < 10; batch++) {
          final objects = <Object>[];
          
          // Create many objects
          for (int i = 0; i < 1000; i++) {
            final transaction = TestHelpers.createTestTransaction(
              amount: i.toDouble(),
            );
            objects.add(ParseResult.needsType(transaction));
            objects.add(ChatMessage.user(
              id: 'test-$i',
              text: 'Test message $i',
              timestamp: DateTime.now(),
            ));
          }
          
          // Verify objects were created
          expect(objects.length, equals(2000));
          
          // Clear objects (simulating garbage collection)
          objects.clear();
          expect(objects.length, equals(0));
        }
        
        // Test should complete without memory issues
        expect(true, isTrue);
      });

      test('should handle deep object graphs efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        // Create complex nested structures
        final complexTransactions = <Transaction>[];
        for (int i = 0; i < 100; i++) {
          final transaction = TestHelpers.createTestTransaction(
            amount: i.toDouble(),
            description: 'Complex transaction $i with detailed description',
            tags: List.generate(50, (index) => 'tag-$i-$index'),
            category: TestHelpers.createTestCategory(
              name: 'Category $i',
            ),
          );
          complexTransactions.add(transaction);
        }
        
        // Create ParseResults with these complex transactions
        final results = <ParseResult>[];
        for (final transaction in complexTransactions) {
          results.add(ParseResult.needsType(transaction));
          results.add(ParseResult.needsCategory(transaction));
          results.add(ParseResult.success(transaction));
        }
        
        stopwatch.stop();
        
        // Should handle complex objects efficiently (under 200ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(200));
        expect(results.length, equals(300));
        
        // Verify data integrity
        for (final result in results) {
          expect(result.transaction, isNotNull);
          if (result.transaction!.tags != null) {
            expect(result.transaction!.tags!.length, equals(50));
          }
        }
      });
    });

    group('Scalability Tests', () {
      test('should scale linearly with input size', () {
        final timings = <int, int>{};
        
        // Test with different input sizes
        final sizes = [100, 500, 1000, 2000];
        
        for (final size in sizes) {
          final stopwatch = Stopwatch()..start();
          
          final results = <ParseResult>[];
          for (int i = 0; i < size; i++) {
            final transaction = TestHelpers.createTestTransaction(
              amount: i.toDouble(),
            );
            results.add(ParseResult.needsType(transaction));
          }
          
          stopwatch.stop();
          timings[size] = stopwatch.elapsedMilliseconds;
          
          expect(results.length, equals(size));
        }
        
        // Verify roughly linear scaling
        // (allowing for some variance due to system factors)
        final ratio1 = timings[500]! / timings[100]!;
        final ratio2 = timings[1000]! / timings[500]!;
        final ratio3 = timings[2000]! / timings[1000]!;
        
        // Ratios should be roughly proportional to size increase
        expect(ratio1, lessThan(10)); // Should not be exponential
        expect(ratio2, lessThan(5));
        expect(ratio3, lessThan(5));
      });

      test('should handle sustained load efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        // Simulate sustained load over time
        for (int round = 0; round < 50; round++) {
          final results = <ParseResult>[];
          
          // Each round processes 100 transactions
          for (int i = 0; i < 100; i++) {
            final transaction = TestHelpers.createTestTransaction(
              amount: (round * 100 + i).toDouble(),
              description: 'Sustained load transaction $round-$i',
            );
            
            // Alternate between operations
            if (i % 2 == 0) {
              results.add(ParseResult.needsType(transaction));
            } else {
              results.add(ParseResult.success(transaction));
            }
          }
          
          expect(results.length, equals(100));
        }
        
        stopwatch.stop();
        
        // Should handle sustained load efficiently (under 2 seconds total)
        expect(stopwatch.elapsedMilliseconds, lessThan(2000));
      });

      test('should maintain performance with complex operations', () {
        final stopwatch = Stopwatch()..start();
        
        // Perform complex operations that simulate real usage
        for (int i = 0; i < 500; i++) {
          // Create initial transaction
          final transaction = TestHelpers.createTestTransaction(
            amount: i.toDouble(),
            description: 'Complex operation $i',
            tags: ['tag1', 'tag2', 'tag3'],
          );
          
          // Simulate soft fail flow
          final needsTypeResult = ParseResult.needsType(transaction);
          expect(needsTypeResult.needsTypeSelection, isTrue);
          
          // User selects type
          final withType = needsTypeResult.transaction.copyWith(
            type: TransactionType.expense,
          );
          
          // Now needs category
          final needsCategoryResult = ParseResult.needsCategory(withType);
          expect(needsCategoryResult.needsCategorySelection, isTrue);
          
          // User selects category
          final withCategory = needsCategoryResult.transaction.copyWith(
            category: TestHelpers.createTestCategory(),
          );
          
          // Final success
          final successResult = ParseResult.success(withCategory);
          expect(successResult.isSuccess, isTrue);
          
          // Verify data integrity throughout
          expect(successResult.transaction.amount, equals(i.toDouble()));
          expect(successResult.transaction.description, equals('Complex operation $i'));
          expect(successResult.transaction.tags, equals(['tag1', 'tag2', 'tag3']));
        }
        
        stopwatch.stop();
        
        // Should complete complex operations efficiently (under 1 second)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });
    });

    group('Edge Case Performance', () {
      test('should handle empty objects efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        for (int i = 0; i < 1000; i++) {
          // Create minimal transactions
          final transaction = TestHelpers.createTestTransaction(
            description: '',
            tags: [],
          );
          
          final result = ParseResult.needsType(transaction);
          expect(result.transaction.description, equals(''));
          expect(result.transaction.tags, equals([]));
        }
        
        stopwatch.stop();
        
        // Should handle empty objects quickly (under 100ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });

      test('should handle error conditions efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        for (int i = 0; i < 1000; i++) {
          final result = ParseResult.failed('Error message $i');
          expect(result.status, equals(ParseStatus.failed));
          expect(result.error, equals('Error message $i'));
        }
        
        stopwatch.stop();
        
        // Should handle errors quickly (under 50ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(50));
      });

      test('should handle rapid enum comparisons efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        final statuses = [
          ParseStatus.success,
          ParseStatus.needsType,
          ParseStatus.needsCategory,
          ParseStatus.failed,
        ];
        
        // Perform many enum comparisons
        for (int i = 0; i < 10000; i++) {
          final status = statuses[i % statuses.length];
          
          // Perform various comparisons
          final isSuccess = status == ParseStatus.success;
          final needsInput = status == ParseStatus.needsType || status == ParseStatus.needsCategory;
          final isFailed = status == ParseStatus.failed;
          
          // Verify comparisons work
          expect(isSuccess || needsInput || isFailed, isTrue);
        }
        
        stopwatch.stop();
        
        // Should handle enum comparisons very quickly (under 10ms)
        expect(stopwatch.elapsedMilliseconds, lessThan(10));
      });
    });
  });
}
