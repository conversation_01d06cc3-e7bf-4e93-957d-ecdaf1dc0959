import 'dart:ui';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/services/localization_service.dart';
import 'package:dreamflow/services/parser/fallback_parser_service.dart';
import 'package:dreamflow/models/transaction_model.dart';
import '../mocks/mock_storage_service.dart';
import '../test_data/sample_transactions.dart';
import '../test_data/spanish_sample_transactions.dart';

/// Integration tests for the localization system
/// Tests the complete flow from asset loading to transaction parsing
void main() {
  group('Localization Integration Tests', () {
    late MockStorageService mockStorage;
    late LocalizationService localizationService;
    late FallbackParserService fallbackParser;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      
      // Reset singleton for clean test state
      LocalizationService.resetInstance();
      localizationService = LocalizationService.createTestInstance();
      
      fallbackParser = FallbackParserService(
        mockStorage,
        localizationService: localizationService,
      );
    });

    tearDown(() {
      localizationService.clearCache();
      LocalizationService.resetInstance();
    });

    group('English Localization Flow', () {
      setUp(() {
        // Mock English localization file
        const mockEnglishJson = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": [
            "spent", "paid", "bought", "purchased", "expense", "pay", "cost",
            "spent on", "paid for", "charge", "bought for", "dinner", "lunch",
            "breakfast", "meal", "food", "coffee", "restaurant", "groceries",
            "shopping", "gas", "fuel", "for"
          ],
          "income_keywords": [
            "received", "earned", "income", "salary", "payment received",
            "got paid", "got money", "earned from", "money from", "receive",
            "selling", "sold", "gift", "bonus", "dividend", "interest",
            "return", "gain", "profit", "reward", "refund"
          ],
          "loan_keywords": [
            "borrowed", "lent", "loan", "debt", "credit", "lend",
            "borrowed from", "lent to", "borrow"
          ],
          "currency_symbols": ["\$", "€", "£", "¥", "₹"],
          "special_patterns": {
            "for_keyword": "\\\\bfor\\\\b",
            "income_before_for": "(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift).*?\\\\bfor\\\\b",
            "payment_exclusion": "pay(?!ment received)",
            "refund_from": "refund.*from"
          }
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'assets/l10n/en.json') {
              return mockEnglishJson;
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );
      });

      test('should load English localization and parse transactions end-to-end', () async {
        final testCases = SampleTransactions.simpleExpenses;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await fallbackParser.parseTransaction(text, locale: const Locale('en'));
          
          expect(result.isSuccess, isTrue, reason: 'Failed for: $text');
          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.currencyCode, equals(expectedCurrency), reason: 'Currency mismatch for: $text');
          expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
        }
      });

      test('should cache English localization data', () async {
        const locale = Locale('en');
        
        expect(localizationService.isLocaleCached(locale), isFalse);
        
        // First call should load from assets
        await localizationService.getPatternsForLocale(locale);
        expect(localizationService.isLocaleCached(locale), isTrue);
        expect(localizationService.cacheSize, equals(1));
        
        // Second call should use cache
        await localizationService.getPatternsForLocale(locale);
        expect(localizationService.cacheSize, equals(1));
      });
    });

    group('Spanish Localization Flow', () {
      setUp(() {
        // Mock Spanish localization file
        const mockSpanishJson = '''
        {
          "locale": "es-ES",
          "decimal_separator": ",",
          "thousands_separator": ".",
          "expense_keywords": [
            "gasté", "pagado", "comprado", "costo", "cena", "almuerzo",
            "desayuno", "comida", "café", "restaurante", "compras",
            "gasolina", "combustible", "gasto", "pagué", "compré",
            "gastado", "precio", "caro", "barato", "para"
          ],
          "income_keywords": [
            "recibido", "ganado", "salario", "vendido", "regalo",
            "ingreso", "cobrado", "dinero recibido", "pago recibido",
            "ganancia", "beneficio", "bono", "dividendo", "interés",
            "retorno", "reembolso", "venta"
          ],
          "loan_keywords": [
            "prestado", "préstamo", "deuda", "crédito", "prestar",
            "prestado de", "prestado a", "pedir prestado", "deber", "adeudo"
          ],
          "currency_symbols": ["\$", "€", "£", "¥", "₹"],
          "special_patterns": {
            "for_keyword": "\\\\bpara\\\\b",
            "income_before_for": "(recibido|ganado|salario|vendido|regalo|ingreso|cobrado|dinero recibido|pago recibido).*?\\\\bpara\\\\b",
            "payment_exclusion": "pag(?!o recibido)",
            "refund_from": "reembolso.*de"
          }
        }
        ''';

        const mockEnglishJson = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": ["spent", "paid", "bought"],
          "income_keywords": ["received", "earned", "income"],
          "loan_keywords": ["borrowed", "lent", "loan"],
          "currency_symbols": ["\$", "€", "£"],
          "special_patterns": {}
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              if (methodCall.arguments == 'assets/l10n/es.json') {
                return mockSpanishJson;
              } else if (methodCall.arguments == 'assets/l10n/en.json') {
                return mockEnglishJson;
              }
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );
      });

      test('should load Spanish localization and parse transactions end-to-end', () async {
        final testCases = SpanishSampleTransactions.simpleExpenses;
        
        for (final entry in testCases.entries) {
          final testData = entry.value;
          final text = testData['text'] as String;
          final expectedAmount = testData['expected_amount'] as double;
          final expectedCurrency = testData['expected_currency'] as String;
          final expectedType = testData['expected_type'] as TransactionType;

          final result = await fallbackParser.parseTransaction(text, locale: const Locale('es'));
          
          expect(result.isSuccess, isTrue, reason: 'Failed for: $text');
          expect(result.transaction.amount, equals(expectedAmount), reason: 'Amount mismatch for: $text');
          expect(result.transaction.currencyCode, equals(expectedCurrency), reason: 'Currency mismatch for: $text');
          expect(result.transaction.type, equals(expectedType), reason: 'Type mismatch for: $text');
        }
      });

      test('should handle Spanish decimal separators correctly', () async {
        const testCases = {
          'Gasté €25,50 en café': 25.50,
          'Recibido €1.500,75 salario': 1500.75,
          'Prestado €100,00 a amigo': 100.00,
        };

        for (final entry in testCases.entries) {
          final result = await fallbackParser.parseTransaction(entry.key, locale: const Locale('es'));
          expect(result.transaction.amount, equals(entry.value), reason: 'Failed for: ${entry.key}');
        }
      });
    });

    group('Fallback Behavior', () {
      setUp(() {
        // Mock only English localization file
        const mockEnglishJson = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": ["spent", "paid", "bought"],
          "income_keywords": ["received", "earned", "income"],
          "loan_keywords": ["borrowed", "lent", "loan"],
          "currency_symbols": ["\$", "€", "£"],
          "special_patterns": {}
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'assets/l10n/en.json') {
              return mockEnglishJson;
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );
      });

      test('should fallback to English when requested locale is unavailable', () async {
        const text = 'Spent \$25.50 on coffee';
        
        // Request French locale, should fallback to English
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('fr'));
        
        expect(result.isSuccess, isTrue);
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.type, equals(TransactionType.expense));
      });

      test('should handle mixed language content gracefully', () async {
        // English text with Spanish locale request
        const text = 'Spent \$25.50 on coffee';
        final result = await fallbackParser.parseTransaction(text, locale: const Locale('es'));
        
        // Should still parse using English fallback
        expect(result.isSuccess, isTrue);
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.type, equals(TransactionType.expense));
      });
    });

    group('Multi-locale Caching', () {
      setUp(() {
        const mockEnglishJson = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": ["spent", "paid"],
          "income_keywords": ["received", "earned"],
          "loan_keywords": ["borrowed", "lent"],
          "currency_symbols": ["\$"],
          "special_patterns": {}
        }
        ''';

        const mockSpanishJson = '''
        {
          "locale": "es-ES",
          "decimal_separator": ",",
          "thousands_separator": ".",
          "expense_keywords": ["gasté", "pagado"],
          "income_keywords": ["recibido", "ganado"],
          "loan_keywords": ["prestado"],
          "currency_symbols": ["€"],
          "special_patterns": {}
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              if (methodCall.arguments == 'assets/l10n/en.json') {
                return mockEnglishJson;
              } else if (methodCall.arguments == 'assets/l10n/es.json') {
                return mockSpanishJson;
              }
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );
      });

      test('should cache multiple locales independently', () async {
        expect(localizationService.cacheSize, equals(0));
        
        // Load English
        await localizationService.getPatternsForLocale(const Locale('en'));
        expect(localizationService.cacheSize, equals(1));
        expect(localizationService.isLocaleCached(const Locale('en')), isTrue);
        
        // Load Spanish
        await localizationService.getPatternsForLocale(const Locale('es'));
        expect(localizationService.cacheSize, equals(2));
        expect(localizationService.isLocaleCached(const Locale('es')), isTrue);
        
        // Both should still be cached
        expect(localizationService.isLocaleCached(const Locale('en')), isTrue);
        expect(localizationService.availableLocales, containsAll(['en', 'es']));
      });

      test('should parse transactions in different locales using cached data', () async {
        // Parse English transaction
        const englishText = 'Spent \$25.50 on coffee';
        final englishResult = await fallbackParser.parseTransaction(englishText, locale: const Locale('en'));
        expect(englishResult.isSuccess, isTrue);
        expect(englishResult.transaction.amount, equals(25.50));
        
        // Parse Spanish transaction
        const spanishText = 'Gasté €30,75 en café';
        final spanishResult = await fallbackParser.parseTransaction(spanishText, locale: const Locale('es'));
        expect(spanishResult.isSuccess, isTrue);
        expect(spanishResult.transaction.amount, equals(30.75));
        
        // Both locales should be cached
        expect(localizationService.cacheSize, equals(2));
      });
    });

    group('Error Handling', () {
      setUp(() {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            throw PlatformException(code: 'NOT_FOUND');
          },
        );
      });

      test('should handle complete localization failure gracefully', () async {
        const text = 'Spent \$25.50 on coffee';
        
        expect(
          () => fallbackParser.parseTransaction(text, locale: const Locale('en')),
          throwsA(isA<Exception>()),
        );
      });
    });
  });
}
