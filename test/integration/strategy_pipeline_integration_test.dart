import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/parser/transaction_parsing_service.dart';
import '../../lib/models/transaction_model.dart';
import '../../lib/models/parse_result.dart';
import '../mocks/mock_storage_service.dart';
import '../mocks/mock_entity_extractor.dart';

/// Integration tests for the new strategy pipeline in TransactionParsingService
/// These tests verify end-to-end parsing behavior through the strategy chain:
/// 1. LearnedAssociationStrategy
/// 2. MlKitStrategy  
/// 3. FallbackRegexStrategy
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Strategy Pipeline Integration Tests', () {
    late MockStorageService mockStorage;

    setUp(() async {
      TransactionParsingService.resetInstance();
      mockStorage = MockStorageService();
      await mockStorage.init();
    });

    tearDown(() {
      TransactionParsingService.resetInstance();
    });

    group('Strategy Chain Execution', () {
      test('should execute strategies in correct order: Learned -> MLKit -> Fallback', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        // Test text that will go through all strategies
        const text = 'Spent \$25.50 on coffee';
        
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(25.50));
        expect(result.transaction.currencyCode, equals('USD'));
      });

      test('should use learned association when available (Strategy 1)', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty();
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        const text = 'Coffee at Starbucks \$5.50';
        const categoryId = 'food';

        // First, learn the association
        await service.learnCategory(text, categoryId);

        // Parse the same text - should use learned association (Strategy 1)
        final result = await service.parseTransaction(text);

        expect(result, isNotNull);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
        expect(result.transaction.amount, greaterThan(0));
      });

      test('should use MLKit strategy when no learned association exists (Strategy 2)', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Dinner at restaurant \$45.75',
          entityText: '\$45.75',
          start: 19,
          end: 25,
        );
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        const text = 'Dinner at restaurant \$45.75';
        
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(45.75));
        expect(result.transaction.currencyCode, equals('USD'));
      });

      test('should use fallback regex when MLKit fails (Strategy 3)', () async {
        final mockExtractor = MockEntityExtractorFactory.createEmpty(); // No entities
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        const text = 'Taxi ride 15.25 EUR';
        
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(15.25));
      });
    });

    group('Trust but Verify Integration', () {
      test('should consolidate MLKit and raw number finder candidates', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Hotel789 stay cost \$150.00',
          entityText: '\$150.00',
          start: 18,
          end: 25,
        );
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        const text = 'Hotel789 stay cost \$150.00';

        final result = await service.parseTransaction(text);

        expect(result, isNotNull);
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        // The strategy should extract some valid amount (either 150.00 or 789)
        expect(result.transaction.amount, greaterThan(0));
      });

      test('should handle multiple amounts in text', () async {
        final mockExtractor = MockEntityExtractorFactory.createWithMoneyEntity(
          text: 'Paid \$50 tip plus \$200 bill',
          entityText: '\$50',
          start: 5,
          end: 8,
        );
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );

        const text = 'Paid \$50 tip plus \$200 bill';

        final result = await service.parseTransaction(text);

        expect(result, isNotNull);
        // Should either succeed with one amount or trigger ambiguity confirmation
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        // Should extract some valid amount
        expect(result.transaction.amount, greaterThan(0));
      });
    });

    group('Error Handling and Edge Cases', () {
      test('should handle empty input gracefully', () async {
        final service = await TransactionParsingService.getInstance(mockStorage);
        
        final result = await service.parseTransaction('');
        
        expect(result, isNotNull);
        expect(result.status, equals(ParseStatus.failed));
        expect(result.transaction.amount, equals(0.0));
      });

      test('should handle whitespace-only input', () async {
        final service = await TransactionParsingService.getInstance(mockStorage);
        
        final result = await service.parseTransaction('   \n\t  ');
        
        expect(result, isNotNull);
        expect(result.status, equals(ParseStatus.failed));
        expect(result.transaction.amount, equals(0.0));
      });

      test('should handle MLKit initialization failure gracefully', () async {
        // Don't inject a mock extractor - let it try to initialize real MLKit and potentially fail
        final service = await TransactionParsingService.getInstance(mockStorage);
        
        const text = 'Coffee \$5.50';
        final result = await service.parseTransaction(text);
        
        expect(result, isNotNull);
        // Should succeed via fallback even if MLKit fails
        expect(result.isSuccess || result.requiresUserInput, isTrue);
      });
    });

    group('Strategy Pattern Validation', () {
      test('should maintain singleton behavior across strategy executions', () async {
        final service1 = await TransactionParsingService.getInstance(mockStorage);
        final service2 = await TransactionParsingService.getInstance(mockStorage);
        
        expect(identical(service1, service2), isTrue);
      });

      test('should preserve all public API methods from original MlKitParserService', () async {
        final service = await TransactionParsingService.getInstance(mockStorage);
        
        // Verify all expected methods exist and are callable
        expect(service.isReady, isA<bool>());
        expect(service.categoryFinder, isNotNull);
        
        // Test main parsing method
        final parseResult = await service.parseTransaction('Test \$10');
        expect(parseResult, isNotNull);
        
        // Test learning method
        await service.learnCategory('Test text', 'test-category');
        
        // Test completion method
        final completeResult = await service.completeTransaction('Test text', 10.0);
        expect(completeResult, isNotNull);
        
        // Test disposal
        service.dispose();
      });

      test('should handle concurrent parsing requests correctly', () async {
        final service = await TransactionParsingService.getInstance(mockStorage);
        
        final futures = [
          service.parseTransaction('Coffee \$5.50'),
          service.parseTransaction('Lunch \$12.75'),
          service.parseTransaction('Taxi \$8.25'),
        ];
        
        final results = await Future.wait(futures);
        
        for (final result in results) {
          expect(result, isNotNull);
          expect(result.isSuccess || result.requiresUserInput, isTrue);
          expect(result.transaction.amount, greaterThan(0));
        }
      });
    });

    group('Performance and Resource Management', () {
      test('should initialize strategies efficiently', () async {
        final stopwatch = Stopwatch()..start();
        
        final service = await TransactionParsingService.getInstance(mockStorage);
        
        stopwatch.stop();
        
        expect(service, isNotNull);
        // Initialization should be reasonably fast (under 1 second)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });

      test('should dispose resources properly', () async {
        final service = await TransactionParsingService.getInstance(mockStorage);
        
        // Use the service
        await service.parseTransaction('Test \$10');
        
        // Dispose should not throw
        expect(() => service.dispose(), returnsNormally);
      });
    });
  });
}
