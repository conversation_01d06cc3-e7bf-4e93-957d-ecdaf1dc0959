import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import '../../lib/screens/chat_screen.dart';
import '../../lib/models/pending_transaction_state.dart';
import '../../lib/models/parse_result.dart';
import '../../lib/models/transaction_model.dart';
import '../../lib/widgets/quick_reply_widget.dart';
import '../../lib/services/parser/transaction_parsing_service.dart';
import '../helpers/test_helpers.dart';
import '../mocks/mock_storage_service.dart';

void main() {
  group('Pending State Transitions Integration Tests', () {
    late TransactionProvider transactionProvider;
    late MockStorageService mockStorage;
    late ValueNotifier<TransactionParsingService?> parsingServiceNotifier;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      transactionProvider = TransactionProvider(mockStorage);
      parsingServiceNotifier = ValueNotifier<TransactionParsingService?>(null);
    });

    Widget createTestWidget({Widget? child}) {
      return MultiProvider(
        providers: [
          ChangeNotifierProvider<TransactionProvider>.value(
            value: transactionProvider,
          ),
          ChangeNotifierProvider<ValueNotifier<TransactionParsingService?>>.value(
            value: parsingServiceNotifier,
          ),
        ],
        child: MaterialApp(
          home: child ?? ChatScreen(),
        ),
      );
    }

    group('State Creation and Validation', () {
      test('should create PendingTransactionState for typeSelection correctly', () {
        final transaction = TestHelpers.createTestTransaction();
        final parseResult = ParseResult.needsType(transaction);
        const originalText = 'buy coffee';

        final pendingState = PendingTransactionState.forTypeSelection(originalText, parseResult);

        expect(pendingState.stage, equals(PendingStage.typeSelection));
        expect(pendingState.originalText, equals(originalText));
        expect(pendingState.parseResult, equals(parseResult));
        expect(pendingState.transaction, equals(transaction));
        expect(pendingState.isTypeSelection, isTrue);
        expect(pendingState.isCategorySelection, isFalse);
        expect(pendingState.isAmountConfirmation, isFalse);
        expect(pendingState.isMissingAmount, isFalse);
      });

      test('should create PendingTransactionState for categorySelection correctly', () {
        final transaction = TestHelpers.createTestTransaction();
        final parseResult = ParseResult.needsCategory(transaction);
        const originalText = 'expense for food';

        final pendingState = PendingTransactionState.forCategorySelection(originalText, parseResult);

        expect(pendingState.stage, equals(PendingStage.categorySelection));
        expect(pendingState.originalText, equals(originalText));
        expect(pendingState.parseResult, equals(parseResult));
        expect(pendingState.transaction, equals(transaction));
        expect(pendingState.isTypeSelection, isFalse);
        expect(pendingState.isCategorySelection, isTrue);
        expect(pendingState.isAmountConfirmation, isFalse);
        expect(pendingState.isMissingAmount, isFalse);
      });

      test('should create PendingTransactionState for amountConfirmation correctly', () {
        final transaction = TestHelpers.createTestTransaction();
        final candidateAmounts = [10.0, 20.0];
        final candidateTexts = ['10', '20'];
        final parseResult = ParseResult.needsAmountConfirmation(
          transaction,
          candidateAmounts,
          candidateTexts,
        );
        const originalText = 'paid 10 or 20 dollars';

        final pendingState = PendingTransactionState.forAmountConfirmation(originalText, parseResult);

        expect(pendingState.stage, equals(PendingStage.amountConfirmation));
        expect(pendingState.originalText, equals(originalText));
        expect(pendingState.parseResult, equals(parseResult));
        expect(pendingState.transaction, equals(transaction));
        expect(pendingState.candidateAmounts, equals(candidateAmounts));
        expect(pendingState.candidateTexts, equals(candidateTexts));
        expect(pendingState.isTypeSelection, isFalse);
        expect(pendingState.isCategorySelection, isFalse);
        expect(pendingState.isAmountConfirmation, isTrue);
        expect(pendingState.isMissingAmount, isFalse);
      });

      test('should throw error for invalid ParseStatus/PendingStage combinations', () {
        final transaction = TestHelpers.createTestTransaction();
        final parseResult = ParseResult.needsType(transaction);
        const originalText = 'test';

        // Should throw when trying to create category selection with needsType result
        expect(
          () => PendingTransactionState.forCategorySelection(originalText, parseResult),
          throwsArgumentError,
        );

        // Should throw when trying to create amount confirmation with needsType result
        expect(
          () => PendingTransactionState.forAmountConfirmation(originalText, parseResult),
          throwsArgumentError,
        );
      });
    });

    group('State Transition Flow', () {
      testWidgets('should handle simple type selection flow', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Create a transaction that needs type selection
        final transaction = TestHelpers.createTestTransaction();
        final parseResult = ParseResult.needsType(transaction);

        // Verify the parse result structure
        expect(parseResult.status, equals(ParseStatus.needsType));
        expect(parseResult.needsTypeSelection, isTrue);
        expect(parseResult.transaction, equals(transaction));
      });

      testWidgets('should handle simple category selection flow', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Create a transaction that needs category selection
        final transaction = TestHelpers.createTestTransaction();
        final parseResult = ParseResult.needsCategory(transaction);

        // Verify the parse result structure
        expect(parseResult.status, equals(ParseStatus.needsCategory));
        expect(parseResult.needsCategorySelection, isTrue);
        expect(parseResult.transaction, equals(transaction));
      });

      testWidgets('should handle simple amount confirmation flow', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Create a transaction that needs amount confirmation
        final transaction = TestHelpers.createTestTransaction();
        final candidateAmounts = [50.0, 100.0];
        final candidateTexts = ['50', '100'];
        final parseResult = ParseResult.needsAmountConfirmation(
          transaction,
          candidateAmounts,
          candidateTexts,
        );

        // Verify the parse result structure
        expect(parseResult.status, equals(ParseStatus.needsAmountConfirmation));
        expect(parseResult.candidateAmounts, equals(candidateAmounts));
        expect(parseResult.candidateTexts, equals(candidateTexts));
        expect(parseResult.transaction, equals(transaction));
      });
    });

    group('State Cleanup and Isolation', () {
      test('should properly clear pending state', () {
        final transaction = TestHelpers.createTestTransaction();
        final parseResult = ParseResult.needsType(transaction);
        const originalText = 'buy coffee';

        final pendingState = PendingTransactionState.forTypeSelection(originalText, parseResult);

        // Verify state is created
        expect(pendingState, isNotNull);
        expect(pendingState.stage, equals(PendingStage.typeSelection));

        // In a real scenario, this would be set to null to clear the state
        // We can't test the actual clearing here since it's handled by ChatScreen
        // But we can verify that the state object itself is properly structured
        expect(pendingState.originalText, isNotEmpty);
        expect(pendingState.parseResult, isNotNull);
      });

      test('should maintain state isolation between different transactions', () {
        final transaction1 = TestHelpers.createTestTransaction(description: 'coffee');
        final transaction2 = TestHelpers.createTestTransaction(description: 'lunch');
        
        final parseResult1 = ParseResult.needsType(transaction1);
        final parseResult2 = ParseResult.needsCategory(transaction2);
        
        final pendingState1 = PendingTransactionState.forTypeSelection('buy coffee', parseResult1);
        final pendingState2 = PendingTransactionState.forCategorySelection('lunch expense', parseResult2);

        // Verify states are independent
        expect(pendingState1.stage, equals(PendingStage.typeSelection));
        expect(pendingState2.stage, equals(PendingStage.categorySelection));
        expect(pendingState1.originalText, equals('buy coffee'));
        expect(pendingState2.originalText, equals('lunch expense'));
        expect(pendingState1.transaction.description, equals('coffee'));
        expect(pendingState2.transaction.description, equals('lunch'));
      });
    });

    group('Quick Reply Integration', () {
      testWidgets('should handle quick reply data flow correctly', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();

        // Test amount confirmation with quick reply data
        final transaction = TestHelpers.createTestTransaction();
        final candidateAmounts = [25.0, 50.0, 75.0];
        final candidateTexts = ['25', '50', '75'];
        final parseResult = ParseResult.needsAmountConfirmation(
          transaction,
          candidateAmounts,
          candidateTexts,
        );

        final pendingState = PendingTransactionState.forAmountConfirmation(
          'spent 25 50 or 75 dollars',
          parseResult,
        );

        // Verify quick reply data flows correctly through state
        expect(pendingState.candidateAmounts, equals(candidateAmounts));
        expect(pendingState.candidateTexts, equals(candidateTexts));
        expect(pendingState.candidateAmounts?.length, equals(3));
        expect(pendingState.candidateTexts?.length, equals(3));
        
        // Verify individual candidates
        for (int i = 0; i < candidateAmounts.length; i++) {
          expect(pendingState.candidateAmounts![i], equals(candidateAmounts[i]));
          expect(pendingState.candidateTexts![i], equals(candidateTexts[i]));
        }
      });
    });

    group('Error Handling and Edge Cases', () {
      test('should handle empty candidate data gracefully', () {
        final transaction = TestHelpers.createTestTransaction();

        // Create parse result with empty candidates (edge case)
        final parseResult = ParseResult.needsAmountConfirmation(
          transaction,
          [], // empty candidates
          [],
        );

        final pendingState = PendingTransactionState.forAmountConfirmation(
          'test text',
          parseResult,
        );

        expect(pendingState.candidateAmounts, isEmpty);
        expect(pendingState.candidateTexts, isEmpty);
        expect(pendingState.stage, equals(PendingStage.amountConfirmation));
      });

      test('should handle mismatched candidate data lengths', () {
        final transaction = TestHelpers.createTestTransaction();

        final parseResult = ParseResult.needsAmountConfirmation(
          transaction,
          [10.0, 20.0], // 2 amounts
          ['10'], // 1 text - mismatched length
        );

        final pendingState = PendingTransactionState.forAmountConfirmation(
          'test text',
          parseResult,
        );

        expect(pendingState.candidateAmounts?.length, equals(2));
        expect(pendingState.candidateTexts?.length, equals(1));
        expect(pendingState.stage, equals(PendingStage.amountConfirmation));
      });
    });

    group('Backward Compatibility', () {
      test('should maintain compatibility with existing ParseResult structure', () {
        final transaction = TestHelpers.createTestTransaction();
        
        // Test all ParseStatus types that should work with PendingTransactionState
        final typeResult = ParseResult.needsType(transaction);
        final categoryResult = ParseResult.needsCategory(transaction);
        final amountResult = ParseResult.needsAmountConfirmation(transaction, [10.0], ['10']);

        // Verify all can create pending states
        expect(
          () => PendingTransactionState.forTypeSelection('test', typeResult),
          returnsNormally,
        );
        expect(
          () => PendingTransactionState.forCategorySelection('test', categoryResult),
          returnsNormally,
        );
        expect(
          () => PendingTransactionState.forAmountConfirmation('test', amountResult),
          returnsNormally,
        );
      });
    });
  });
}
