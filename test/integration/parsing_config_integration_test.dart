import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/parser/transaction_parsing_service.dart';
import '../../lib/services/parser/parsing_config.dart';
import '../../lib/models/transaction_model.dart';
import '../mocks/mock_storage_service.dart';
import '../mocks/mock_entity_extractor.dart';

void main() {
  group('ParsingConfig Integration Tests', () {
    late MockStorageService mockStorage;
    late MockEntityExtractor mockEntityExtractor;

    setUp(() {
      mockStorage = MockStorageService();
      mockEntityExtractor = MockEntityExtractor();
    });

    tearDown(() {
      TransactionParsingService.resetInstance();
      mockStorage.reset();
      mockEntityExtractor.reset();
    });

    group('End-to-End Configuration Tests', () {
      test('should propagate custom configuration through entire parsing pipeline', () async {
        const customConfig = ParsingConfig(
          defaultCurrency: 'EUR',
          strictEmbeddedLetterThreshold: 2,
          abbreviationPattern: '[kKmM]',
        );

        await mockStorage.saveDefaultCurrency('EUR');
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          config: customConfig,
        );

        // Test that configuration affects parsing behavior
        final result = await service.parseTransaction('25.50 coffee');
        
        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('EUR'));
      });

      test('should use default configuration when none provided', () async {
        await mockStorage.saveDefaultCurrency('USD');

        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          // No config parameter - should use defaults
        );

        final result = await service.parseTransaction('15.75 lunch');

        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('USD'));
      });

      test('should handle configuration with background initialization', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'GBP');

        await mockStorage.saveDefaultCurrency('GBP');
        
        final service = await TransactionParsingService.initializeInBackground(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          config: customConfig,
        );

        final result = await service.parseTransaction('£30.00 dinner');
        
        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('GBP'));
      });
    });

    group('Strategy Chain Configuration Tests', () {
      test('should pass configuration to all strategies in chain', () async {
        const customConfig = ParsingConfig(
          defaultCurrency: 'CAD',
          strictEmbeddedLetterThreshold: 1,
        );

        await mockStorage.saveDefaultCurrency('CAD');

        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          config: customConfig,
        );

        // Test different scenarios that would trigger different strategies
        final testCases = [
          'C\$20.00 coffee', // Should work with any strategy
          '50.75 lunch', // No currency symbol
          'spent 15.25', // Expense keyword
        ];

        for (final testText in testCases) {
          final result = await service.parseTransaction(testText);
          expect(result, isNotNull, reason: 'Failed for: $testText');
          expect(result.transaction.currencyCode, equals('CAD'));
        }
      });

      test('should maintain configuration consistency across strategy execution', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'JPY');

        await mockStorage.saveDefaultCurrency('JPY');
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          config: customConfig,
        );

        // Multiple parsing operations should all use the same configuration
        final operations = [
          '¥1000 sushi',
          '¥500 coffee',
          '¥2500 shopping',
          '¥750 transport',
        ];

        for (final text in operations) {
          final result = await service.parseTransaction(text);
          expect(result, isNotNull);
          expect(result.transaction.currencyCode, equals('JPY'));
        }
      });
    });

    group('Backward Compatibility Tests', () {
      test('should maintain backward compatibility when no config provided', () async {
        await mockStorage.saveDefaultCurrency('USD');
        
        // Old way of initializing without config
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
        );

        final result = await service.parseTransaction('\$25.00 coffee');
        
        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('USD'));
        // Should work exactly as before
      });

      test('should handle null config parameter gracefully', () async {
        await mockStorage.saveDefaultCurrency('USD');
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          config: null, // Explicitly null
        );

        final result = await service.parseTransaction('12.50 lunch');
        
        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('USD'));
      });
    });

    group('Configuration Validation Tests', () {
      test('should handle extreme configuration values gracefully', () async {
        const extremeConfig = ParsingConfig(
          embeddedLetterThreshold: 0,
          strictEmbeddedLetterThreshold: 100,
          thousandCutoff: 1.0,
          millionCutoff: 10.0,
          billionCutoff: 100.0,
          defaultCurrency: 'XYZ', // Non-standard currency
          abbreviationPattern: '[xyz]', // Custom pattern
        );

        await mockStorage.saveDefaultCurrency('XYZ');
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          config: extremeConfig,
        );

        // Should not crash with extreme values
        final result = await service.parseTransaction('100 test');
        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('XYZ'));
      });

      test('should handle configuration with empty/invalid patterns', () async {
        const invalidConfig = ParsingConfig(
          abbreviationPattern: '', // Empty pattern
          defaultCurrency: '', // Empty currency
        );

        await mockStorage.saveDefaultCurrency('USD');
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          config: invalidConfig,
        );

        // Should handle gracefully without crashing
        final result = await service.parseTransaction('50.00 test');
        expect(result, isNotNull);
      });
    });

    group('Performance Tests', () {
      test('should not significantly impact parsing performance', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'EUR');
        
        await mockStorage.saveDefaultCurrency('EUR');
        
        final serviceWithConfig = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          config: customConfig,
        );

        // Reset for comparison
        TransactionParsingService.resetInstance();
        
        final serviceWithoutConfig = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
        );

        const testText = '€25.50 coffee';
        
        // Both should complete in reasonable time
        final stopwatch1 = Stopwatch()..start();
        final result1 = await serviceWithConfig.parseTransaction(testText);
        stopwatch1.stop();
        
        final stopwatch2 = Stopwatch()..start();
        final result2 = await serviceWithoutConfig.parseTransaction(testText);
        stopwatch2.stop();

        expect(result1, isNotNull);
        expect(result2, isNotNull);
        
        // Performance difference should be minimal
        // This is a basic check - in practice, the difference should be negligible
        expect(stopwatch1.elapsedMilliseconds, lessThan(1000));
        expect(stopwatch2.elapsedMilliseconds, lessThan(1000));
      });

      test('should handle multiple configuration instances efficiently', () async {
        final configs = [
          const ParsingConfig(defaultCurrency: 'USD'),
          const ParsingConfig(defaultCurrency: 'EUR'),
          const ParsingConfig(defaultCurrency: 'GBP'),
          const ParsingConfig(defaultCurrency: 'JPY'),
        ];

        for (final config in configs) {
          TransactionParsingService.resetInstance();
          await mockStorage.saveDefaultCurrency(config.defaultCurrency);
          
          final service = await TransactionParsingService.getInstance(
            mockStorage,
            entityExtractor: mockEntityExtractor,
            config: config,
          );

          final result = await service.parseTransaction('100.00 test');
          expect(result, isNotNull);
          expect(result.transaction.currencyCode, equals(config.defaultCurrency));
        }
      });
    });

    group('Configuration Consistency Tests', () {
      test('should maintain configuration across service lifecycle', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'AUD');

        await mockStorage.saveDefaultCurrency('AUD');
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          config: customConfig,
        );

        // Multiple operations over time should maintain same configuration
        for (int i = 0; i < 5; i++) {
          final result = await service.parseTransaction('A\$${10 + i}.00 test $i');
          expect(result, isNotNull);
          expect(result.transaction.currencyCode, equals('AUD'));
        }
      });

      test('should handle singleton behavior with configuration', () async {
        const config1 = ParsingConfig(defaultCurrency: 'USD');
        
        await mockStorage.saveDefaultCurrency('USD');
        
        final service1 = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          config: config1,
        );

        // Second call should return same instance (singleton behavior)
        final service2 = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockEntityExtractor,
          config: const ParsingConfig(defaultCurrency: 'EUR'), // Different config
        );

        expect(identical(service1, service2), isTrue);
        
        // Should use the first configuration (singleton was already created)
        final result = await service2.parseTransaction('25.00 test');
        expect(result, isNotNull);
        expect(result.transaction.currencyCode, equals('USD')); // First config
      });
    });
  });
}
