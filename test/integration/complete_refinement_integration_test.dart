import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import '../../lib/services/parser/transaction_parsing_service.dart';
import '../../lib/services/parser/parsing_config.dart';
import '../../lib/services/parser/parse_logger.dart';
import '../../lib/services/parser/entity_extractor_base.dart';
import '../../lib/models/parse_result.dart';
import '../../lib/models/pending_transaction_state.dart';
import '../mocks/mock_storage_service.dart';
import '../mocks/mock_entity_extractor.dart';
import '../helpers/test_helpers.dart';

/// Memory-based log output for testing
class MemoryLogOutput extends LogOutput {
  final List<OutputEvent> events = [];

  @override
  void output(OutputEvent event) {
    events.add(event);
  }

  void clear() {
    events.clear();
  }

  List<String> get messages => events.map((e) => e.lines.join('\n')).toList();
  
  /// Get all messages containing a specific correlation ID
  List<String> getMessagesForId(String parseId) {
    return messages.where((msg) => msg.contains('[parse:$parseId]')).toList();
  }
  
  /// Extract correlation ID from a START message
  String? extractIdFromStartMessage() {
    for (final message in messages) {
      if (message.contains('START:')) {
        final match = RegExp(r'\[parse:([a-f0-9-]{8})\]').firstMatch(message);
        return match?.group(1);
      }
    }
    return null;
  }
}

/// Comprehensive master integration test that validates all 4 refinements working together
/// in complete end-to-end scenarios.
/// 
/// Tests:
/// 1. Strategy Pattern + Configuration + Logging + State Management
/// 2. Multi-Stage Soft-Fail with All Refinements  
/// 3. Performance with All Refinements
/// 4. Error Handling Across All Refinements
void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Complete Refinement Integration Tests', () {
    late MockStorageService mockStorage;
    late MockEntityExtractor mockExtractor;
    late MemoryLogOutput memoryOutput;

    setUp(() async {
      // Reset singleton and set up mocks
      TransactionParsingService.resetInstance();
      mockStorage = MockStorageService();
      mockExtractor = MockEntityExtractor();
      
      // Set up memory log output for validation
      memoryOutput = MemoryLogOutput();
      ParseLogger.setLogOutput(memoryOutput);
      
      // Configure mock storage defaults
      await mockStorage.init();
      mockStorage.setString('default_currency', 'USD');
      mockStorage.setString('categories', '[]');
    });

    tearDown(() {
      TransactionParsingService.resetInstance();
      memoryOutput.clear();
    });

    group('Complete Refinement Integration Tests', () {
      test('Strategy Pattern + Configuration + Logging + State Management - Complete Flow', () async {
        // Create custom configuration to test configuration propagation
        const customConfig = ParsingConfig(
          defaultCurrency: 'EUR',
          strictEmbeddedLetterThreshold: 1,
          abbreviationPattern: '[kKmM]',
        );

        // Configure storage for EUR currency
        await mockStorage.saveDefaultCurrency('EUR');
        
        // Configure mock extractor to return specific entities for "A2B coffee 5 EUR"
        mockExtractor.setMockResults([
          MockEntityAnnotation(text: 'A2B', start: 0, end: 3, entityType: EntityType.other),
          MockEntityAnnotation(text: 'coffee', start: 4, end: 10, entityType: EntityType.other),
          MockEntityAnnotation(text: '5', start: 11, end: 12, entityType: EntityType.money),
          MockEntityAnnotation(text: 'EUR', start: 13, end: 16, entityType: EntityType.other),
        ]);

        // Initialize service with custom configuration
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
          config: customConfig,
        );

        // Clear any initialization logs
        memoryOutput.clear();

        // Parse transaction that should trigger ML Kit strategy with custom config
        const testText = 'A2B coffee 5 EUR';
        final result = await service.parseTransaction(testText);

        // Validate parsing result - new logic correctly identifies ambiguous type first
        expect(result, isNotNull);
        expect(result.requiresUserInput, isTrue);
        expect(result.status, equals(ParseStatus.needsType));
        expect(result.transaction.amount, equals(5.0));
        expect(result.transaction.currencyCode, equals('EUR'));

        // Extract correlation ID from logs
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);
        expect(parseId!.length, equals(8));

        // Validate structured logging with correlation ID
        final logMessages = memoryOutput.getMessagesForId(parseId);
        expect(logMessages.length, greaterThan(3));
        
        // Verify START message
        expect(logMessages.any((msg) => msg.contains('START: "$testText"')), isTrue);
        
        // Verify strategy execution logs contain correlation ID and parsing details
        // The exact log messages may vary, but we should see parsing activity
        expect(logMessages.any((msg) => msg.contains('Strategy') || msg.contains('handled')), isTrue);

        // Validate configuration propagation - EUR currency used throughout
        expect(result.transaction.currencyCode, equals('EUR'));
        
        // Test state management with PendingTransactionState
        final pendingState = PendingTransactionState.forTypeSelection(
          testText,
          result,
        );

        expect(pendingState.isTypeSelection, isTrue);
        expect(pendingState.originalText, equals(testText));
        expect(pendingState.transaction.amount, equals(5.0));
        expect(pendingState.transaction.currencyCode, equals('EUR'));
        expect(pendingState.parseResult.status, equals(ParseStatus.needsType));
      });

      test('Multi-Stage Soft-Fail with All Refinements - Complex Scenario', () async {
        // Custom config for testing
        const customConfig = ParsingConfig(
          defaultCurrency: 'GBP',
          strictEmbeddedLetterThreshold: 2,
        );

        await mockStorage.saveDefaultCurrency('GBP');
        
        // Configure mock for ambiguous scenario
        mockExtractor.setMockResults([
          MockEntityAnnotation(text: 'restaurant', start: 0, end: 10, entityType: EntityType.other),
          MockEntityAnnotation(text: '25', start: 16, end: 18, entityType: EntityType.money),
          MockEntityAnnotation(text: '30', start: 22, end: 24, entityType: EntityType.money),
        ]);

        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
          config: customConfig,
        );

        memoryOutput.clear();

        // Parse text with multiple amounts (should trigger amount confirmation)
        const testText = 'restaurant bill 25 or 30 pounds';
        final result = await service.parseTransaction(testText);

        // Should require user input (either amount confirmation or type selection)
        expect(result.requiresUserInput, isTrue);
        // The result could be needsType or needsAmountConfirmation depending on the logic
        expect(result.status, anyOf([ParseStatus.needsType, ParseStatus.needsAmountConfirmation]));

        // Extract and validate correlation ID
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);

        final logMessages = memoryOutput.getMessagesForId(parseId!);
        expect(logMessages.any((msg) => msg.contains('Multiple amount candidates')), isTrue);
        
        // Test state transitions
        final pendingState = PendingTransactionState.forAmountConfirmation(
          testText,
          result,
        );
        
        expect(pendingState.isAmountConfirmation, isTrue);
        expect(pendingState.candidateAmounts, isNotEmpty);
        expect(pendingState.transaction.currencyCode, equals('GBP'));
      });

      test('Performance with All Refinements - End-to-End Timing', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'USD');
        
        mockExtractor.setMockResults([
          MockEntityAnnotation(text: 'coffee', start: 0, end: 6, entityType: EntityType.other),
          MockEntityAnnotation(text: '5', start: 7, end: 8, entityType: EntityType.money),
        ]);

        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
          config: customConfig,
        );

        // Test performance across multiple operations
        final testTexts = [
          'coffee 5 dollars',
          'lunch 12.50',
          'gas 45.99',
          'movie tickets 24.00',
          'grocery shopping 85.30',
        ];

        final stopwatch = Stopwatch()..start();
        
        for (final text in testTexts) {
          memoryOutput.clear();
          final operationStart = stopwatch.elapsedMilliseconds;
          
          final result = await service.parseTransaction(text);
          
          final operationTime = stopwatch.elapsedMilliseconds - operationStart;
          
          // Validate performance: each operation should complete within 100ms
          expect(operationTime, lessThan(100));
          expect(result, isNotNull);
          
          // Validate correlation ID generation for each operation
          final parseId = memoryOutput.extractIdFromStartMessage();
          expect(parseId, isNotNull);
          expect(parseId!.length, equals(8));
        }
        
        stopwatch.stop();
        
        // Total time for 5 operations should be reasonable
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
      });

      test('Error Handling Across All Refinements - Comprehensive Error Scenarios', () async {
        // Test with failing entity extractor
        final failingExtractor = MockEntityExtractor();
        failingExtractor.setShouldThrowError(true);

        const customConfig = ParsingConfig(defaultCurrency: 'USD');
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: failingExtractor,
          config: customConfig,
        );

        memoryOutput.clear();

        // Parse with failing ML Kit - should fall back gracefully
        const testText = 'coffee 5 dollars';
        final result = await service.parseTransaction(testText);

        // Should still get a result via fallback strategy
        expect(result, isNotNull);
        
        // Extract correlation ID and validate error logging
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);

        final logMessages = memoryOutput.getMessagesForId(parseId!);

        // Should contain start logs and complete parsing despite potential errors
        expect(logMessages.any((msg) => msg.contains('START:')), isTrue);
        // Error handling might be graceful without explicit error messages in logs
        
        // Validate that state management works even with errors
        if (result.requiresUserInput) {
          final pendingState = PendingTransactionState.forCategorySelection(
            testText,
            result,
          );
          expect(pendingState.originalText, equals(testText));
        }
      });
    });

    group('Specific Integration Scenarios', () {
      test('Learned Association + Custom Config + Logging - Fast Path', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'CAD');
        await mockStorage.saveDefaultCurrency('CAD');
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          config: customConfig,
        );

        // First, learn an association
        final learnTransaction = TestHelpers.createTestTransaction(
          amount: 5.0,
          description: 'coffee',
          categoryId: 'food',
          currencyCode: 'CAD',
        );
        
        await service.learnCategory('coffee 5', learnTransaction.categoryId);
        
        memoryOutput.clear();

        // Now parse the same pattern - should use learned association
        final result = await service.parseTransaction('coffee 5');

        expect(result, isNotNull);
        // Learned associations should improve parsing but might still need user input
        expect(result.isSuccess || result.requiresUserInput, isTrue);
        expect(result.transaction.amount, equals(5.0));
        expect(result.transaction.currencyCode, equals('CAD'));

        // Validate that learned association was used (fast path)
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);

        final logMessages = memoryOutput.getMessagesForId(parseId!);
        expect(logMessages.any((msg) => msg.contains('Found learned association')), isTrue);
      });

      test('Concurrent Operations - Multiple Parsing with Different Configs', () async {
        // Test concurrent parsing operations with different correlation IDs
        const config2 = ParsingConfig(defaultCurrency: 'EUR');
        
        mockExtractor.setMockResults([
          MockEntityAnnotation(text: 'coffee', start: 0, end: 6, entityType: EntityType.other),
          MockEntityAnnotation(text: '5', start: 7, end: 8, entityType: EntityType.money),
        ]);

        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
          config: config2,
        );

        memoryOutput.clear();

        // Parse concurrently (simulated)
        final future1 = service.parseTransaction('coffee 5 USD');
        final future2 = service.parseTransaction('tea 3 EUR');

        final results = await Future.wait([future1, future2]);

        // Both should complete successfully
        expect(results[0], isNotNull);
        expect(results[1], isNotNull);

        // Should have multiple correlation IDs in logs
        final allMessages = memoryOutput.messages;
        final startMessages = allMessages.where((msg) => msg.contains('START:')).toList();
        expect(startMessages.length, equals(2));

        // Each operation should have its own correlation ID
        final ids = startMessages.map((msg) {
          final match = RegExp(r'\[parse:([a-f0-9-]{8})\]').firstMatch(msg);
          return match?.group(1);
        }).where((id) => id != null).toList();
        
        expect(ids.length, equals(2));
        expect(ids[0] != ids[1], isTrue); // Different correlation IDs
      });
    });
  });
}
