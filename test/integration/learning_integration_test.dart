import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/parser/learned_association_service.dart';
import '../../lib/services/parser/transaction_parsing_service.dart';
import '../../lib/services/parser/category_finder_service.dart';
import '../../lib/models/transaction_model.dart';
import '../../lib/models/parse_result.dart';
import '../mocks/mock_storage_service.dart';
import '../mocks/mock_entity_extractor.dart';

void main() {
  group('Learning Integration Tests', () {
    late MockStorageService mockStorage;
    late LearnedAssociationService learnedService;
    late TransactionParsingService mlkitService;
    late CategoryFinderService categoryService;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      
      learnedService = await LearnedAssociationService.getInstance(mockStorage);
      // Use mock extractor for reliable integration testing
      final mockExtractor = MockEntityExtractorFactory.createUSDScenario('\$5.50', 0, 5);
      mlkitService = await TransactionParsingService.getInstance(mockStorage, entityExtractor: mockExtractor);
      categoryService = CategoryFinderService(mockStorage);
    });

    tearDown(() async {
      await learnedService.clearAllData();
      LearnedAssociationService.resetInstance();
      TransactionParsingService.resetInstance();
    });

    group('End-to-End Learning Workflow', () {
      test('should learn from user corrections and bypass parsing on next occurrence', () async {
        const originalText = 'coffee at local cafe';
        const expectedType = TransactionType.expense;
        const expectedCategoryId = 'food';

        // Step 1: First time parsing - no learned association
        final firstResult = await mlkitService.parseTransaction(originalText);
        
        // Should either succeed with ML Kit or require user input
        expect(firstResult, isNotNull);
        
        // Step 2: Simulate user correction by learning the association
        await learnedService.learn(originalText, 
            type: expectedType, 
            categoryId: expectedCategoryId);

        // Step 3: Parse the same text again - should bypass ML Kit
        final secondResult = await mlkitService.parseTransaction(originalText);
        
        // Should now return learned association immediately
        expect(secondResult.isSuccess, isTrue);
        expect(secondResult.transaction.type, equals(expectedType));
        expect(secondResult.transaction.categoryId, equals(expectedCategoryId));
        expect(secondResult.transaction.description, equals(originalText));
      });

      test('should learn from category finder service integration', () async {
        const text = 'starbucks coffee';
        const categoryId = 'food';

        // Step 1: Learn through category finder service
        await categoryService.learnCategory(text, categoryId);

        // Step 2: Verify the association was stored in unified service
        final association = await learnedService.getAssociation(text);
        expect(association?.categoryId, equals(categoryId));

        // Step 3: Verify category finder can retrieve it
        final foundCategory = await categoryService.findCategory(text, TransactionType.expense);
        expect(foundCategory, equals(categoryId));
      });

      test('should handle partial text matching in real parsing scenarios', () async {
        const fullText = 'payment to uber technologies inc for ride';
        const partialText = 'uber ride';
        const categoryId = 'transport';

        // Learn with full text
        await learnedService.learn(fullText, categoryId: categoryId);

        // Should match with partial text
        final result = await mlkitService.parseTransaction(partialText);
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals(categoryId));
      });

      test('should handle vendor name extraction in parsing pipeline', () async {
        const descriptions = [
          'Payment to McDonald\'s Restaurant #1234',
          'MCDONALD\'S #5678 PURCHASE',
          'McDonald\'s Drive-Thru Order'
        ];
        const categoryId = 'food';

        // Learn with first description
        await learnedService.learn(descriptions[0], categoryId: categoryId);

        // Should match other McDonald's variations
        for (int i = 1; i < descriptions.length; i++) {
          final result = await mlkitService.parseTransaction(descriptions[i]);
          expect(result.isSuccess, isTrue, 
              reason: 'Should match: ${descriptions[i]}');
          expect(result.transaction.categoryId, equals(categoryId),
              reason: 'Should have learned category for: ${descriptions[i]}');
        }
      });
    });

    group('Learning Confidence and Updates', () {
      test('should increase confidence with repeated learning', () async {
        const text = 'grocery shopping';
        const categoryId = 'food';

        // Learn multiple times
        for (int i = 0; i < 5; i++) {
          await learnedService.learn(text, categoryId: categoryId);
        }

        final association = await learnedService.getAssociation(text);
        expect(association?.confidence, equals(5));
      });

      test('should update associations when user changes their mind', () async {
        const text = 'amazon purchase';
        
        // First learn as shopping
        await learnedService.learn(text, categoryId: 'shopping');
        
        // User changes mind, now it's entertainment
        await learnedService.learn(text, categoryId: 'entertainment');
        
        // Should have updated category and increased confidence
        final association = await learnedService.getAssociation(text);
        expect(association?.categoryId, equals('entertainment'));
        expect(association?.confidence, equals(2));
      });

      test('should learn both type and category progressively', () async {
        const text = 'freelance payment';
        
        // First learn only type
        await learnedService.learn(text, type: TransactionType.income);
        
        // Then learn category as well
        await learnedService.learn(text, 
            type: TransactionType.income, 
            categoryId: 'work');
        
        final association = await learnedService.getAssociation(text);
        expect(association?.type, equals(TransactionType.income));
        expect(association?.categoryId, equals('work'));
        expect(association?.confidence, equals(2));
      });
    });

    group('Cross-Service Integration', () {
      test('should work seamlessly between all parser services', () async {
        const text = 'dinner at restaurant';
        const type = TransactionType.expense;
        const categoryId = 'food';

        // Learn through learned service
        await learnedService.learn(text, type: type, categoryId: categoryId);

        // Should be found by category finder
        final foundCategory = await categoryService.findCategory(text, TransactionType.expense);
        expect(foundCategory, equals(categoryId));

        // Should bypass ML Kit parsing
        final parseResult = await mlkitService.parseTransaction(text);
        expect(parseResult.isSuccess, isTrue);
        expect(parseResult.transaction.type, equals(type));
        expect(parseResult.transaction.categoryId, equals(categoryId));
      });

      test('should maintain backward compatibility with legacy storage', () async {
        // Setup legacy data
        await mockStorage.setString('learned_categories', '{"starbucks":"food"}');

        // Reset services to trigger migration
        LearnedAssociationService.resetInstance();
        TransactionParsingService.resetInstance();

        // Recreate services
        await LearnedAssociationService.getInstance(mockStorage);
        final newMlkitService = await TransactionParsingService.getInstance(mockStorage);
        final newCategoryService = CategoryFinderService(mockStorage);

        // Should find migrated data
        final category = await newCategoryService.findCategory('starbucks', TransactionType.expense);
        expect(category, equals('food'));

        // Should work in parsing pipeline
        final result = await newMlkitService.parseTransaction('starbucks coffee');
        expect(result.isSuccess, isTrue);
        expect(result.transaction.categoryId, equals('food'));
      });
    });

    group('Error Handling and Edge Cases', () {
      test('should handle learning failures gracefully in parsing pipeline', () async {
        const text = 'test transaction';
        
        // Simulate storage failure by corrupting data
        await mockStorage.setString('learned_associations', 'invalid json');
        
        // Parsing should still work even if learning lookup fails
        final result = await mlkitService.parseTransaction(text);
        expect(result, isNotNull);
        // Should fall back to normal ML Kit parsing
      });

      test('should handle empty and invalid inputs across services', () async {
        // Empty text should not break any service
        final emptyResult = await mlkitService.parseTransaction('');
        expect(emptyResult, isNotNull);
        
        final emptyCategory = await categoryService.findCategory('', TransactionType.expense);
        expect(emptyCategory, isNull);
        
        // Learning empty text should not crash
        await learnedService.learn('', categoryId: 'test');
        final emptyAssociation = await learnedService.getAssociation('');
        expect(emptyAssociation, isNull);
      });

      test('should handle concurrent learning operations', () async {
        const text = 'concurrent test';
        const categoryId = 'test';

        // Simulate concurrent learning
        final futures = List.generate(10, (index) => 
            learnedService.learn(text, categoryId: categoryId));
        
        await Future.wait(futures);
        
        // Should have learned successfully
        final association = await learnedService.getAssociation(text);
        expect(association?.categoryId, equals(categoryId));
        expect(association?.confidence, equals(10));
      });
    });

    group('Performance Integration', () {
      test('should be faster than full parsing for learned associations', () async {
        const text = 'performance test transaction';
        const categoryId = 'test';

        // Learn the association
        await learnedService.learn(text, 
            type: TransactionType.expense, 
            categoryId: categoryId);

        // Measure learned association lookup time
        final learnedStopwatch = Stopwatch()..start();
        final learnedResult = await mlkitService.parseTransaction(text);
        learnedStopwatch.stop();

        // Measure full parsing time with different text
        final fullParsingStopwatch = Stopwatch()..start();
        await mlkitService.parseTransaction('unknown transaction text');
        fullParsingStopwatch.stop();

        // Learned association should be successful and reasonably fast
        expect(learnedResult.isSuccess, isTrue);
        // In test environment, both operations are very fast, so just verify they complete
        expect(learnedStopwatch.elapsedMilliseconds,
            lessThanOrEqualTo(fullParsingStopwatch.elapsedMilliseconds + 1));
      });
    });

    group('Bug Fix Integration Tests', () {
      test('Bug 1: Should return needsCategory instead of random categories for unknown text', () async {
        const unknownText = 'spent 25.50 at qwerty zxcvbn';

        // Parse unknown text - should return needsCategory, not random category
        final result = await mlkitService.parseTransaction(unknownText);

        // Should either need category selection or succeed with 'unknown' placeholder
        if (result.needsCategorySelection) {
          // This is the expected behavior after our fix
          expect(result.status, equals(ParseStatus.needsCategory));
        } else if (result.isSuccess) {
          // If it succeeds, it should use 'unknown' placeholder, not 'other'
          expect(result.transaction.categoryId, equals('unknown'));
        } else {
          fail('Unexpected parse result: ${result.status}');
        }
      });

      test('Bug 2: Should learn from first manual edit without initialization race', () async {
        const transactionText = 'spent 42.50 at qwerty zxcvbn';
        const expectedCategory = 'shopping';

        // Parse transaction that needs category
        final parseResult = await mlkitService.parseTransaction(transactionText);

        // Create transaction with user-selected category
        final transaction = Transaction(
          id: parseResult.transaction.id,
          amount: parseResult.transaction.amount,
          type: parseResult.transaction.type,
          categoryId: expectedCategory,
          date: parseResult.transaction.date,
          description: parseResult.transaction.description,
          tags: parseResult.transaction.tags,
          currencyCode: parseResult.transaction.currencyCode,
        );

        // Simulate manual edit through TransactionProvider
        final provider = TransactionProvider(mockStorage);
        await provider.addTransaction(parseResult.transaction);
        await provider.updateTransaction(transaction);

        // Verify learning occurred
        final association = await learnedService.getAssociation(transactionText);
        expect(association, isNotNull, reason: 'Should learn from first edit');
        expect(association!.categoryId, equals(expectedCategory));
      });

      test('Bug 3: Should work consistently from first edit with vendor name extraction', () async {
        const firstText = 'spent 100 at Banana';
        const secondText = 'spent 50 at Banana';
        const expectedCategory = 'food';

        // Step 1: Parse first transaction (we don't need the result for this test)
        await mlkitService.parseTransaction(firstText);

        // Step 2: Simulate user correction
        await learnedService.learn(firstText, categoryId: expectedCategory);

        // Step 3: Parse similar transaction - should use learned association
        final secondResult = await mlkitService.parseTransaction(secondText);

        expect(secondResult.isSuccess, isTrue,
            reason: 'Should find learned association for similar vendor');
        expect(secondResult.transaction.categoryId, equals(expectedCategory),
            reason: 'Should use learned category for same vendor');
      });

      test('Integration: Complete workflow without timing issues', () async {
        const originalText = 'spent 15.75 at Coffee Bean';
        const expectedCategory = 'beverages';

        // Step 1: Parse unknown transaction
        final firstResult = await mlkitService.parseTransaction(originalText);

        // Step 2: Add transaction to provider
        final provider = TransactionProvider(mockStorage);
        await provider.addTransaction(firstResult.transaction);

        // Step 3: User edits category
        final editedTransaction = Transaction(
          id: firstResult.transaction.id,
          amount: firstResult.transaction.amount,
          type: firstResult.transaction.type,
          categoryId: expectedCategory,
          date: firstResult.transaction.date,
          description: firstResult.transaction.description,
          tags: firstResult.transaction.tags,
          currencyCode: firstResult.transaction.currencyCode,
        );

        // Step 4: Update transaction (triggers learning)
        await provider.updateTransaction(editedTransaction);

        // Step 5: Parse similar transaction
        final secondResult = await mlkitService.parseTransaction('spent 12.50 at Coffee Bean');

        // Should use learned association
        expect(secondResult.isSuccess, isTrue);
        expect(secondResult.transaction.categoryId, equals(expectedCategory));
      });
    });
  });
}
