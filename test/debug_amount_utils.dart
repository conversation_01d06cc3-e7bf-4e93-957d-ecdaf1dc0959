import 'package:flutter_test/flutter_test.dart';
import '../lib/utils/amount_utils.dart';

void main() {
  test('AmountUtils VND detection test', () {
    final result = AmountUtils.extractAmountFromText('Cafe50 100k vnd');
    print('Result: $result');
    
    expect(result, isNotNull);
    expect(result!['amount'], equals(100000.0));
    expect(result['currency'], equals('VND'));
  });
  
  test('AmountUtils VND detection test - simple', () {
    final result = AmountUtils.extractAmountFromText('100k vnd');
    print('Result: $result');
    
    expect(result, isNotNull);
    expect(result!['amount'], equals(100000.0));
    expect(result['currency'], equals('VND'));
  });
  
  test('AmountUtils VND detection test - uppercase', () {
    final result = AmountUtils.extractAmountFromText('100k VND');
    print('Result: $result');

    expect(result, isNotNull);
    expect(result!['amount'], equals(100000.0));
    expect(result['currency'], equals('VND'));
  });

  test('AmountUtils VND detection test - dinner at lux70 100k vnd', () {
    final result = AmountUtils.extractAmountFromText('dinner at lux70 100k vnd');
    print('Result: $result');

    expect(result, isNotNull);
    // This will show us what AmountUtils actually finds
  });
}
