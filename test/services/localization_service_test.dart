import 'dart:ui';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/services/localization_service.dart';
import 'package:dreamflow/models/localization_data.dart';

void main() {
  group('LocalizationService', () {
    late LocalizationService localizationService;

    setUp(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      LocalizationService.resetInstance();
      localizationService = LocalizationService.createTestInstance();
    });

    tearDown(() {
      localizationService.clearCache();
      LocalizationService.resetInstance();
    });

    group('singleton pattern', () {
      test('should return same instance', () {
        // Set the test instance as the singleton
        LocalizationService.resetInstance();
        final testInstance = LocalizationService.createTestInstance();
        LocalizationService.setTestInstance(testInstance);

        final instance1 = LocalizationService.instance;
        final instance2 = LocalizationService.instance;
        expect(identical(instance1, instance2), isTrue);
      });
    });

    group('getPatternsForLocale', () {
      test('should load English locale successfully', () async {
        // Mock the asset bundle
        const mockEnglishJson = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": ["spent", "paid", "bought"],
          "income_keywords": ["received", "earned", "income"],
          "loan_keywords": ["borrowed", "lent", "loan"],
          "currency_symbols": ["\$", "€", "£"],
          "special_patterns": {
            "for_keyword": "\\\\bfor\\\\b"
          }
        }
        ''';

        // Set up mock asset bundle
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'assets/l10n/en.json') {
              return mockEnglishJson;
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final locale = const Locale('en');
        final localizationData = await localizationService.getPatternsForLocale(locale);

        expect(localizationData.locale, equals('en-US'));
        expect(localizationData.decimalSeparator, equals('.'));
        expect(localizationData.thousandsSeparator, equals(','));
        expect(localizationData.expenseKeywords, contains('spent'));
        expect(localizationData.incomeKeywords, contains('received'));
        expect(localizationData.loanKeywords, contains('borrowed'));
        expect(localizationData.currencySymbols, contains('\$'));
      });

      test('should cache loaded locales', () async {
        const mockEnglishJson = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": ["spent"],
          "income_keywords": ["received"],
          "loan_keywords": ["borrowed"],
          "currency_symbols": ["\$"]
        }
        ''';

        int loadCallCount = 0;
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'assets/l10n/en.json') {
              loadCallCount++;
              return mockEnglishJson;
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final locale = const Locale('en');
        
        // First call should load from assets
        await localizationService.getPatternsForLocale(locale);
        expect(loadCallCount, equals(1));
        expect(localizationService.cacheSize, equals(1));
        expect(localizationService.isLocaleCached(locale), isTrue);

        // Second call should use cache
        await localizationService.getPatternsForLocale(locale);
        expect(loadCallCount, equals(1)); // Should not increase
        expect(localizationService.cacheSize, equals(1));
      });

      test('should fallback to English when requested locale is unavailable', () async {
        const mockEnglishJson = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": ["spent"],
          "income_keywords": ["received"],
          "loan_keywords": ["borrowed"],
          "currency_symbols": ["\$"]
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              if (methodCall.arguments == 'assets/l10n/en.json') {
                return mockEnglishJson;
              } else if (methodCall.arguments == 'assets/l10n/fr.json') {
                throw PlatformException(code: 'NOT_FOUND');
              }
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final frenchLocale = const Locale('fr');
        final localizationData = await localizationService.getPatternsForLocale(frenchLocale);

        // Should return English data as fallback
        expect(localizationData.locale, equals('en-US'));
        expect(localizationData.expenseKeywords, contains('spent'));
      });

      test('should throw exception when both requested locale and fallback fail', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final frenchLocale = const Locale('fr');
        
        expect(
          () => localizationService.getPatternsForLocale(frenchLocale),
          throwsA(isA<Exception>().having(
            (e) => e.toString(),
            'message',
            contains('Failed to load localization data'),
          )),
        );
      });

      test('should handle malformed JSON gracefully', () async {
        const malformedJson = '{ "locale": "en-US", invalid }';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'assets/l10n/en.json') {
              return malformedJson;
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final locale = const Locale('en');
        
        expect(
          () => localizationService.getPatternsForLocale(locale),
          throwsA(isA<Exception>().having(
            (e) => e.toString(),
            'message',
            contains('Failed to parse localization file'),
          )),
        );
      });
    });

    group('preloadLocale', () {
      test('should preload locale successfully', () async {
        const mockEnglishJson = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": ["spent"],
          "income_keywords": ["received"],
          "loan_keywords": ["borrowed"],
          "currency_symbols": ["\$"]
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'assets/l10n/en.json') {
              return mockEnglishJson;
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final locale = const Locale('en');
        expect(localizationService.isLocaleCached(locale), isFalse);

        await localizationService.preloadLocale(locale);
        
        expect(localizationService.isLocaleCached(locale), isTrue);
        expect(localizationService.cacheSize, equals(1));
      });

      test('should not preload if already cached', () async {
        const mockEnglishJson = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": ["spent"],
          "income_keywords": ["received"],
          "loan_keywords": ["borrowed"],
          "currency_symbols": ["\$"]
        }
        ''';

        int loadCallCount = 0;
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'assets/l10n/en.json') {
              loadCallCount++;
              return mockEnglishJson;
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final locale = const Locale('en');
        
        // First preload
        await localizationService.preloadLocale(locale);
        expect(loadCallCount, equals(1));

        // Second preload should not trigger loading
        await localizationService.preloadLocale(locale);
        expect(loadCallCount, equals(1));
      });

      test('should handle preload failures gracefully', () async {
        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final locale = const Locale('fr');
        
        // Should not throw exception, just print warning
        await localizationService.preloadLocale(locale);
        expect(localizationService.isLocaleCached(locale), isFalse);
      });
    });

    group('cache management', () {
      test('should clear cache', () async {
        const mockEnglishJson = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": ["spent"],
          "income_keywords": ["received"],
          "loan_keywords": ["borrowed"],
          "currency_symbols": ["\$"]
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString' &&
                methodCall.arguments == 'assets/l10n/en.json') {
              return mockEnglishJson;
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        final locale = const Locale('en');
        await localizationService.getPatternsForLocale(locale);
        
        expect(localizationService.cacheSize, equals(1));
        expect(localizationService.isLocaleCached(locale), isTrue);

        localizationService.clearCache();
        
        expect(localizationService.cacheSize, equals(0));
        expect(localizationService.isLocaleCached(locale), isFalse);
      });

      test('should track available locales', () async {
        const mockEnglishJson = '''
        {
          "locale": "en-US",
          "decimal_separator": ".",
          "thousands_separator": ",",
          "expense_keywords": ["spent"],
          "income_keywords": ["received"],
          "loan_keywords": ["borrowed"],
          "currency_symbols": ["\$"]
        }
        ''';

        const mockSpanishJson = '''
        {
          "locale": "es-ES",
          "decimal_separator": ",",
          "thousands_separator": ".",
          "expense_keywords": ["gasté"],
          "income_keywords": ["recibido"],
          "loan_keywords": ["prestado"],
          "currency_symbols": ["€"]
        }
        ''';

        TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
            .setMockMethodCallHandler(
          const MethodChannel('flutter/assets'),
          (MethodCall methodCall) async {
            if (methodCall.method == 'loadString') {
              if (methodCall.arguments == 'assets/l10n/en.json') {
                return mockEnglishJson;
              } else if (methodCall.arguments == 'assets/l10n/es.json') {
                return mockSpanishJson;
              }
            }
            throw PlatformException(code: 'NOT_FOUND');
          },
        );

        expect(localizationService.availableLocales, isEmpty);

        await localizationService.getPatternsForLocale(const Locale('en'));
        expect(localizationService.availableLocales, contains('en'));

        await localizationService.getPatternsForLocale(const Locale('es'));
        expect(localizationService.availableLocales, containsAll(['en', 'es']));
      });
    });

    group('validateLocalizationData', () {
      test('should validate correct data', () {
        const validData = LocalizationData(
          locale: 'en-US',
          decimalSeparator: '.',
          thousandsSeparator: ',',
          expenseKeywords: ['spent'],
          incomeKeywords: ['received'],
          loanKeywords: ['borrowed'],
          currencySymbols: ['\$'],
          specialPatterns: {},
        );

        expect(LocalizationService.validateLocalizationData(validData), isTrue);
      });

      test('should reject data with empty fields', () {
        const invalidData = LocalizationData(
          locale: '',
          decimalSeparator: '.',
          thousandsSeparator: ',',
          expenseKeywords: ['spent'],
          incomeKeywords: ['received'],
          loanKeywords: ['borrowed'],
          currencySymbols: ['\$'],
          specialPatterns: {},
        );

        expect(LocalizationService.validateLocalizationData(invalidData), isFalse);
      });

      test('should reject data with empty keyword arrays', () {
        const invalidData = LocalizationData(
          locale: 'en-US',
          decimalSeparator: '.',
          thousandsSeparator: ',',
          expenseKeywords: [],
          incomeKeywords: ['received'],
          loanKeywords: ['borrowed'],
          currencySymbols: ['\$'],
          specialPatterns: {},
        );

        expect(LocalizationService.validateLocalizationData(invalidData), isFalse);
      });
    });
  });
}
