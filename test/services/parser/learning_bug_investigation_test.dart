import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/learned_association_service.dart';
import '../../../lib/services/parser/transaction_parsing_service.dart';
import '../../../lib/services/parser/category_finder_service.dart';
import '../../../lib/models/transaction_model.dart';
import '../../../lib/models/parse_result.dart';
import '../../mocks/mock_storage_service.dart';
import '../../mocks/mock_entity_extractor.dart';
import '../../helpers/test_helpers.dart';

/// Focused unit tests to reproduce the three specific bugs reported by the user:
/// 1. Random categories instead of soft-fail
/// 2. First edit learning failure  
/// 3. Learning only works from 2nd edit
void main() {
  group('Learning Bug Investigation Tests', () {
    late MockStorageService mockStorage;
    late LearnedAssociationService learnedService;
    late TransactionParsingService mlkitService;
    late CategoryFinderService categoryFinder;
    late TransactionProvider transactionProvider;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();

      learnedService = await LearnedAssociationService.getInstance(mockStorage);
      // Use mock extractor for reliable bug investigation testing
      final mockExtractor = MockEntityExtractorFactory.createUSDScenario('\$25.50', 0, 6);
      mlkitService = await TransactionParsingService.getInstance(mockStorage, entityExtractor: mockExtractor);
      categoryFinder = CategoryFinderService(mockStorage);
      transactionProvider = TransactionProvider(mockStorage);
      await transactionProvider.waitForLearnedAssociationService();
    });

    tearDown(() async {
      await learnedService.clearAllData();
      LearnedAssociationService.resetInstance();
      TransactionParsingService.resetInstance();
    });

    group('Bug 1: Random Categories Instead of Soft-Fail', () {
      test('should return needsCategory when CategoryFinderService returns null', () async {
        const unknownText = 'spent 25.50 at qwerty zxcvbn';
        
        // Ensure no learned associations exist
        await learnedService.clearAllData();
        
        // Verify CategoryFinderService returns null for unknown text
        final foundCategory = await categoryFinder.findCategory(unknownText, TransactionType.expense);
        expect(foundCategory, isNull, reason: 'CategoryFinderService should return null for unknown text');
        
        // Parse the unknown transaction
        final parseResult = await mlkitService.parseTransaction(unknownText);
        
        // Should return needsCategory status, not success with 'other' category
        expect(parseResult.status, equals(ParseStatus.needsCategory), 
            reason: 'Parser should return needsCategory when no category can be determined');
        expect(parseResult.needsCategorySelection, isTrue,
            reason: 'Parser should indicate category selection is needed');
        expect(parseResult.isSuccess, isFalse,
            reason: 'Parser should not report success when category is unknown');
      });

      test('should not default to other category for unknown transactions', () async {
        const unknownText = 'spent 42.50 at qwerty zxcvbn';
        
        // Clear all learned data to ensure clean state
        await learnedService.clearAllData();
        
        // Parse unknown transaction
        final parseResult = await mlkitService.parseTransaction(unknownText);
        
        // Transaction should not have 'other' as categoryId when needsCategory is returned
        if (parseResult.status == ParseStatus.needsCategory) {
          // The transaction object may have null categoryId or 'other', but the status should indicate user input needed
          expect(parseResult.needsCategorySelection, isTrue);
        } else {
          fail('Expected needsCategory status for unknown transaction, got: ${parseResult.status}');
        }
      });

      test('should still succeed for learned associations', () async {
        const knownText = 'starbucks coffee';
        const expectedCategory = 'food';
        
        // Learn an association first
        await learnedService.learn(knownText, categoryId: expectedCategory);
        
        // Parse the known transaction
        final parseResult = await mlkitService.parseTransaction(knownText);
        
        // Should succeed with learned category
        expect(parseResult.isSuccess, isTrue,
            reason: 'Parser should succeed for learned associations');
        expect(parseResult.transaction.categoryId, equals(expectedCategory),
            reason: 'Parser should use learned category');
      });
    });

    group('Bug 2: First Edit Learning Failure', () {
      test('should learn from first manual edit even if service not pre-initialized', () async {
        // Create a fresh TransactionProvider to simulate app startup
        final freshProvider = TransactionProvider(mockStorage);
        await freshProvider.waitForLearnedAssociationService();
        
        // Create a transaction that needs categorization
        final originalTransaction = TestHelpers.createTestTransaction(
          description: 'banana smoothie',
          categoryId: 'other', // Initially uncategorized
        );
        
        await freshProvider.addTransaction(originalTransaction);
        
        // Immediately edit the transaction to change category (simulating first user edit)
        final editedTransaction = Transaction(
          id: originalTransaction.id,
          amount: originalTransaction.amount,
          type: originalTransaction.type,
          categoryId: 'food', // User selects food category
          date: originalTransaction.date,
          description: originalTransaction.description,
          tags: originalTransaction.tags,
          currencyCode: originalTransaction.currencyCode,
        );
        
        // This should trigger learning even on first edit
        await freshProvider.updateTransaction(editedTransaction);
        
        // Verify the association was learned
        final association = await learnedService.getAssociation('banana smoothie');
        expect(association, isNotNull, 
            reason: 'Learning should work on first edit');
        expect(association!.categoryId, equals('food'),
            reason: 'Learned association should have correct category');
      });

      test('should handle rapid edits without initialization race conditions', () async {
        final transactions = [
          TestHelpers.createTestTransaction(description: 'coffee shop', categoryId: 'other'),
          TestHelpers.createTestTransaction(description: 'gas station', categoryId: 'other'),
          TestHelpers.createTestTransaction(description: 'grocery store', categoryId: 'other'),
        ];
        
        // Add all transactions
        for (final transaction in transactions) {
          await transactionProvider.addTransaction(transaction);
        }
        
        // Rapidly edit all transactions (simulating quick user corrections)
        final edits = [
          (transactions[0], 'food'),
          (transactions[1], 'transport'),
          (transactions[2], 'shopping'),
        ];
        
        for (final (transaction, categoryId) in edits) {
          final edited = Transaction(
            id: transaction.id,
            amount: transaction.amount,
            type: transaction.type,
            categoryId: categoryId,
            date: transaction.date,
            description: transaction.description,
            tags: transaction.tags,
            currencyCode: transaction.currencyCode,
          );
          
          await transactionProvider.updateTransaction(edited);
        }
        
        // Verify all associations were learned
        final coffeeAssoc = await learnedService.getAssociation('coffee shop');
        final gasAssoc = await learnedService.getAssociation('gas station');
        final groceryAssoc = await learnedService.getAssociation('grocery store');
        
        expect(coffeeAssoc?.categoryId, equals('food'));
        expect(gasAssoc?.categoryId, equals('transport'));
        expect(groceryAssoc?.categoryId, equals('shopping'));
      });
    });

    group('Bug 3: Learning Only Works from 2nd Edit', () {
      test('should complete banana scenario: parse -> edit -> parse again', () async {
        const firstText = 'spent 100 at Banana';
        const secondText = 'spent 50 at Banana';
        const expectedCategory = 'food';
        
        // Step 1: Parse "Banana 100" - should need category
        final firstResult = await mlkitService.parseTransaction(firstText);
        
        // Should require user input (either needsCategory or success with unknown category)
        if (firstResult.isSuccess && firstResult.transaction.categoryId != expectedCategory) {
          // If it succeeds with a different category, we'll simulate user correction
        } else if (firstResult.needsCategorySelection) {
          // This is the expected behavior after our fix
        } else {
          fail('Unexpected parse result for first banana transaction: ${firstResult.status}');
        }
        
        // Step 2: User selects Food category (simulate manual edit)
        final editedTransaction = Transaction(
          id: firstResult.transaction.id,
          amount: firstResult.transaction.amount,
          type: firstResult.transaction.type,
          categoryId: expectedCategory, // User selects food
          date: firstResult.transaction.date,
          description: firstResult.transaction.description,
          tags: firstResult.transaction.tags,
          currencyCode: firstResult.transaction.currencyCode,
        );
        
        await transactionProvider.addTransaction(firstResult.transaction);
        await transactionProvider.updateTransaction(editedTransaction);
        
        // Step 3: Parse "Banana 50" - should auto-categorize as Food
        final secondResult = await mlkitService.parseTransaction(secondText);
        
        expect(secondResult.isSuccess, isTrue,
            reason: 'Second banana transaction should succeed with learned category');
        expect(secondResult.transaction.categoryId, equals(expectedCategory),
            reason: 'Second banana transaction should use learned food category');
      });

      test('should handle vendor name extraction with trailing numbers consistently', () async {
        const transactions = [
          'spent 25.50 at McDonald\'s',
          'spent 18.99 at McDonald\'s',
          'spent 32.10 at McDonald\'s Restaurant',
        ];
        const expectedCategory = 'food';
        
        // Learn from first transaction
        await learnedService.learn(transactions[0], categoryId: expectedCategory);
        
        // All variations should match the learned association
        for (int i = 1; i < transactions.length; i++) {
          final result = await mlkitService.parseTransaction(transactions[i]);
          expect(result.isSuccess, isTrue,
              reason: 'Should match learned association for: ${transactions[i]}');
          expect(result.transaction.categoryId, equals(expectedCategory),
              reason: 'Should use learned category for: ${transactions[i]}');
        }
      });

      test('should extract vendor names consistently for learning and lookup', () async {
        const learningText = 'spent 15.75 at Starbucks Coffee';
        const lookupTexts = [
          'spent 12.50 at Starbucks',
          'spent 18.25 at Starbucks Coffee Shop',
          'spent 20.00 at STARBUCKS COFFEE',
        ];
        const expectedCategory = 'beverages';
        
        // Learn from the first text
        await learnedService.learn(learningText, categoryId: expectedCategory);
        
        // All variations should find the learned association
        for (final text in lookupTexts) {
          final association = await learnedService.getAssociation(text);
          expect(association, isNotNull,
              reason: 'Should find association for: $text');
          expect(association!.categoryId, equals(expectedCategory),
              reason: 'Should have correct category for: $text');
        }
      });
    });

    group('Integration Validation', () {
      test('should handle complete workflow without timing issues', () async {
        const unknownText = 'New Vendor Payment 75.00';
        const selectedCategory = 'services';
        
        // Step 1: Parse unknown transaction
        final initialResult = await mlkitService.parseTransaction(unknownText);
        
        // Step 2: Add transaction and immediately edit (simulating user workflow)
        await transactionProvider.addTransaction(initialResult.transaction);
        
        final correctedTransaction = Transaction(
          id: initialResult.transaction.id,
          amount: initialResult.transaction.amount,
          type: initialResult.transaction.type,
          categoryId: selectedCategory,
          date: initialResult.transaction.date,
          description: initialResult.transaction.description,
          tags: initialResult.transaction.tags,
          currencyCode: initialResult.transaction.currencyCode,
        );
        
        await transactionProvider.updateTransaction(correctedTransaction);
        
        // Step 3: Parse similar transaction immediately after
        const similarText = 'New Vendor Service 45.00';
        final followupResult = await mlkitService.parseTransaction(similarText);
        
        expect(followupResult.isSuccess, isTrue,
            reason: 'Follow-up transaction should succeed with learned category');
        expect(followupResult.transaction.categoryId, equals(selectedCategory),
            reason: 'Follow-up transaction should use learned category');
      });
    });

    group('Currency Bug in Learned Associations', () {
      test('should use default currency instead of hardcoded USD in learned associations', () async {
        // Set default currency to VND
        await mockStorage.saveDefaultCurrency('VND');

        // Create a learned association without explicit currency
        const testText = '1000 lunch';
        const selectedCategory = 'food';

        // First transaction - should learn the association
        final firstResult = await mlkitService.parseTransaction(testText);
        expect(firstResult.needsCategorySelection, isTrue,
            reason: 'First transaction should need category selection');

        // Simulate user selecting category
        final firstTransaction = firstResult.transaction.copyWith(categoryId: selectedCategory);
        await transactionProvider.addTransaction(firstTransaction);

        // Wait for learning to complete
        await Future.delayed(const Duration(milliseconds: 100));

        // Second transaction - should use learned association
        final secondResult = await mlkitService.parseTransaction(testText);

        // Should use VND currency from default, not hardcoded USD
        expect(secondResult.transaction.currencyCode, equals('VND'),
            reason: 'Learned association should use default currency VND, not hardcoded USD');
        expect(secondResult.transaction.categoryId, equals(selectedCategory),
            reason: 'Should use learned category');
      });

      test('should respect explicit currency in learned associations', () async {
        // Set default currency to VND
        await mockStorage.saveDefaultCurrency('VND');

        // Create a learned association with explicit USD currency
        const testText = '\$1000 lunch';
        const selectedCategory = 'food';

        // First transaction - should learn the association
        final firstResult = await mlkitService.parseTransaction(testText);
        expect(firstResult.needsCategorySelection, isTrue,
            reason: 'First transaction should need category selection');

        // Simulate user selecting category
        final firstTransaction = firstResult.transaction.copyWith(categoryId: selectedCategory);
        await transactionProvider.addTransaction(firstTransaction);

        // Wait for learning to complete
        await Future.delayed(const Duration(milliseconds: 100));

        // Second transaction - should use learned association
        final secondResult = await mlkitService.parseTransaction(testText);

        // Should use explicit USD currency, not default VND
        expect(secondResult.transaction.currencyCode, equals('USD'),
            reason: 'Learned association should respect explicit USD currency');
        expect(secondResult.transaction.categoryId, equals(selectedCategory),
            reason: 'Should use learned category');
      });

      test('should handle currency changes in default settings', () async {
        // Start with USD as default
        await mockStorage.saveDefaultCurrency('USD');

        const testText = '1000 lunch';
        const selectedCategory = 'food';

        // First transaction - learn with USD
        final firstResult = await mlkitService.parseTransaction(testText);
        final firstTransaction = firstResult.transaction.copyWith(categoryId: selectedCategory);
        await transactionProvider.addTransaction(firstTransaction);
        await Future.delayed(const Duration(milliseconds: 100));

        // Change default currency to EUR
        await mockStorage.saveDefaultCurrency('EUR');

        // Second transaction - should use new default EUR
        final secondResult = await mlkitService.parseTransaction(testText);

        expect(secondResult.transaction.currencyCode, equals('EUR'),
            reason: 'Should use updated default currency EUR');
        expect(secondResult.transaction.categoryId, equals(selectedCategory),
            reason: 'Should still use learned category');
      });
    });

    group('Number Abbreviation Support in Learned Associations', () {
      test('should learn and recall abbreviation patterns correctly', () async {
        const testText = '100k lunch';
        const selectedCategory = 'food';
        const expectedAmount = 100000.0;

        // First transaction - should parse abbreviation and need category
        final firstResult = await mlkitService.parseTransaction(testText);
        expect(firstResult.transaction.amount, equals(expectedAmount),
            reason: 'Should parse 100k as 100000');
        expect(firstResult.needsCategorySelection, isTrue,
            reason: 'First transaction should need category selection');

        // Simulate user selecting category
        final firstTransaction = firstResult.transaction.copyWith(categoryId: selectedCategory);
        await transactionProvider.addTransaction(firstTransaction);

        // Wait for learning to complete
        await Future.delayed(const Duration(milliseconds: 100));

        // Second transaction - should use learned association
        final secondResult = await mlkitService.parseTransaction(testText);

        expect(secondResult.transaction.amount, equals(expectedAmount),
            reason: 'Learned association should preserve abbreviation parsing');
        expect(secondResult.transaction.categoryId, equals(selectedCategory),
            reason: 'Should use learned category');
        expect(secondResult.isSuccess, isTrue,
            reason: 'Should succeed with learned association');
      });

      test('should handle different abbreviation cases in learned associations', () async {
        const testCases = [
          {'text': '2.5M salary', 'amount': 2500000.0},
          {'text': '1.2B investment', 'amount': **********.0},
          {'text': '500k bonus', 'amount': 500000.0},
        ];

        for (final testCase in testCases) {
          final text = testCase['text'] as String;
          final expectedAmount = testCase['amount'] as double;
          const selectedCategory = 'income';

          // First transaction - learn the pattern
          final firstResult = await mlkitService.parseTransaction(text);
          expect(firstResult.transaction.amount, equals(expectedAmount),
              reason: 'Should parse abbreviation correctly for: $text');

          final firstTransaction = firstResult.transaction.copyWith(categoryId: selectedCategory);
          await transactionProvider.addTransaction(firstTransaction);
          await Future.delayed(const Duration(milliseconds: 100));

          // Second transaction - use learned association
          final secondResult = await mlkitService.parseTransaction(text);
          expect(secondResult.transaction.amount, equals(expectedAmount),
              reason: 'Learned association should preserve abbreviation for: $text');
          expect(secondResult.transaction.categoryId, equals(selectedCategory),
              reason: 'Should use learned category for: $text');
        }
      });

      test('should handle abbreviations with currency symbols in learned associations', () async {
        const testText = '\$100k shopping';
        const selectedCategory = 'shopping';
        const expectedAmount = 100000.0;
        const expectedCurrency = 'USD';

        // First transaction - learn the pattern
        final firstResult = await mlkitService.parseTransaction(testText);
        expect(firstResult.transaction.amount, equals(expectedAmount),
            reason: 'Should parse \$100k as 100000');
        expect(firstResult.transaction.currencyCode, equals(expectedCurrency),
            reason: 'Should detect USD currency');

        final firstTransaction = firstResult.transaction.copyWith(categoryId: selectedCategory);
        await transactionProvider.addTransaction(firstTransaction);
        await Future.delayed(const Duration(milliseconds: 100));

        // Second transaction - use learned association
        final secondResult = await mlkitService.parseTransaction(testText);
        expect(secondResult.transaction.amount, equals(expectedAmount),
            reason: 'Learned association should preserve abbreviation amount');
        expect(secondResult.transaction.currencyCode, equals(expectedCurrency),
            reason: 'Learned association should preserve currency');
        expect(secondResult.transaction.categoryId, equals(selectedCategory),
            reason: 'Should use learned category');
        expect(secondResult.isSuccess, isTrue,
            reason: 'Should succeed with learned association');
      });

      test('should handle mixed abbreviation and currency scenarios', () async {
        // Set default currency to EUR
        await mockStorage.saveDefaultCurrency('EUR');

        const testCases = [
          // Abbreviation with default currency
          {'text': '100k food', 'amount': 100000.0, 'currency': 'EUR'},
          // Abbreviation with explicit currency symbol
          {'text': '\$2.5M investment', 'amount': 2500000.0, 'currency': 'USD'},
          // Abbreviation with explicit currency code
          {'text': '1.5B JPY bonus', 'amount': 1500000000.0, 'currency': 'JPY'},
        ];

        for (final testCase in testCases) {
          final text = testCase['text'] as String;
          final expectedAmount = testCase['amount'] as double;
          final expectedCurrency = testCase['currency'] as String;
          const selectedCategory = 'income';

          // First transaction - learn the pattern
          final firstResult = await mlkitService.parseTransaction(text);
          expect(firstResult.transaction.amount, equals(expectedAmount),
              reason: 'Should parse amount correctly for: $text');
          expect(firstResult.transaction.currencyCode, equals(expectedCurrency),
              reason: 'Should detect currency correctly for: $text');

          final firstTransaction = firstResult.transaction.copyWith(categoryId: selectedCategory);
          await transactionProvider.addTransaction(firstTransaction);
          await Future.delayed(const Duration(milliseconds: 100));

          // Second transaction - use learned association
          final secondResult = await mlkitService.parseTransaction(text);
          expect(secondResult.transaction.amount, equals(expectedAmount),
              reason: 'Learned association should preserve amount for: $text');
          expect(secondResult.transaction.currencyCode, equals(expectedCurrency),
              reason: 'Learned association should preserve currency for: $text');
          expect(secondResult.transaction.categoryId, equals(selectedCategory),
              reason: 'Should use learned category for: $text');
        }
      });
    });
  });
}
