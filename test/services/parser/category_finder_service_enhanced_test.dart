import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/category_finder_service.dart';
import '../../../lib/models/category_suggestion.dart';
import '../../../lib/models/transaction_model.dart';
import '../../mocks/mock_storage_service.dart';

void main() {
  group('CategoryFinderService Enhanced Suggestions', () {
    late CategoryFinderService categoryFinder;
    late MockStorageService mockStorage;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      categoryFinder = CategoryFinderService(mockStorage);
    });

    tearDown(() async {
      await mockStorage.clear();
    });

    group('findCategorySuggestions', () {
      test('should return empty suggestions for empty text', () async {
        final result = await categoryFinder.findCategorySuggestions('', TransactionType.expense);
        
        expect(result.suggestions, isEmpty);
        expect(result.fallbackCategoryId, isNull);
      });

      test('should return keyword-based suggestions for known keywords', () async {
        final result = await categoryFinder.findCategorySuggestions(
          'coffee shop latte', 
          TransactionType.expense,
        );
        
        expect(result.suggestions, isNotEmpty);
        expect(result.suggestions.length, lessThanOrEqualTo(3));
        
        // Should contain food-related suggestions
        final categoryIds = result.suggestions.map((s) => s.categoryId).toList();
        expect(categoryIds, contains('food'));
        
        // Check confidence scoring
        for (final suggestion in result.suggestions) {
          expect(suggestion.confidence, greaterThan(0.0));
          expect(suggestion.confidence, lessThanOrEqualTo(1.0));
          expect(suggestion.source, equals(CategorySuggestionSource.keyword));
          expect(suggestion.matchReason, isNotEmpty);
        }
      });

      test('should prioritize learned associations over keyword matches', () async {
        const text = 'starbucks coffee';
        const learnedCategoryId = 'beverages';
        
        // Learn an association
        await categoryFinder.learnCategory(text, learnedCategoryId);
        
        final result = await categoryFinder.findCategorySuggestions(text, TransactionType.expense);
        
        expect(result.suggestions, isNotEmpty);
        expect(result.suggestions.first.categoryId, equals(learnedCategoryId));
        expect(result.suggestions.first.source, equals(CategorySuggestionSource.learned));
        expect(result.suggestions.first.confidence, greaterThan(0.8)); // High confidence for learned
      });

      test('should return multiple suggestions with different confidence levels', () async {
        final result = await categoryFinder.findCategorySuggestions(
          'restaurant dinner food', 
          TransactionType.expense,
        );
        
        expect(result.suggestions, isNotEmpty);
        
        // Should be sorted by confidence (highest first)
        for (int i = 0; i < result.suggestions.length - 1; i++) {
          expect(
            result.suggestions[i].confidence, 
            greaterThanOrEqualTo(result.suggestions[i + 1].confidence),
          );
        }
      });

      test('should limit suggestions to maximum of 3', () async {
        final result = await categoryFinder.findCategorySuggestions(
          'food restaurant dining eating meal lunch dinner breakfast', 
          TransactionType.expense,
        );
        
        expect(result.suggestions.length, lessThanOrEqualTo(3));
      });

      test('should set fallback category ID to highest confidence suggestion', () async {
        final result = await categoryFinder.findCategorySuggestions(
          'coffee shop', 
          TransactionType.expense,
        );
        
        if (result.suggestions.isNotEmpty) {
          expect(result.fallbackCategoryId, equals(result.suggestions.first.categoryId));
        }
      });

      test('should handle different transaction types correctly', () async {
        final expenseResult = await categoryFinder.findCategorySuggestions(
          'salary payment', 
          TransactionType.expense,
        );
        
        final incomeResult = await categoryFinder.findCategorySuggestions(
          'salary payment', 
          TransactionType.income,
        );
        
        // Results might be different based on transaction type context
        // At minimum, both should complete without error
        expect(expenseResult, isA<CategorySuggestionResult>());
        expect(incomeResult, isA<CategorySuggestionResult>());
      });

      test('should deduplicate suggestions from different sources', () async {
        const text = 'coffee shop';
        const categoryId = 'food';
        
        // Learn the same category that would also match by keywords
        await categoryFinder.learnCategory(text, categoryId);
        
        final result = await categoryFinder.findCategorySuggestions(text, TransactionType.expense);
        
        // Should not have duplicate category IDs
        final categoryIds = result.suggestions.map((s) => s.categoryId).toList();
        final uniqueCategoryIds = categoryIds.toSet().toList();
        expect(categoryIds.length, equals(uniqueCategoryIds.length));
      });

      test('should provide meaningful match reasons', () async {
        final result = await categoryFinder.findCategorySuggestions(
          'restaurant dinner', 
          TransactionType.expense,
        );
        
        for (final suggestion in result.suggestions) {
          expect(suggestion.matchReason, isNotEmpty);
          expect(suggestion.matchReason, isNot(equals('Unknown')));
        }
      });

      test('should handle special characters and case insensitivity', () async {
        final result1 = await categoryFinder.findCategorySuggestions(
          'COFFEE SHOP!!!', 
          TransactionType.expense,
        );
        
        final result2 = await categoryFinder.findCategorySuggestions(
          'coffee shop', 
          TransactionType.expense,
        );
        
        // Should produce similar results regardless of case and special characters
        expect(result1.suggestions, isNotEmpty);
        expect(result2.suggestions, isNotEmpty);
      });
    });

    group('Backward Compatibility', () {
      test('should maintain existing findCategory method behavior', () async {
        const text = 'coffee shop';
        
        final oldResult = await categoryFinder.findCategory(text, TransactionType.expense);
        final newResult = await categoryFinder.findCategorySuggestions(text, TransactionType.expense);
        
        if (oldResult != null && newResult.suggestions.isNotEmpty) {
          // The old single result should match the top suggestion
          expect(newResult.suggestions.first.categoryId, equals(oldResult));
        }
      });

      test('should handle learned categories in both old and new methods', () async {
        const text = 'custom vendor';
        const categoryId = 'custom-category';
        
        await categoryFinder.learnCategory(text, categoryId);
        
        final oldResult = await categoryFinder.findCategory(text, TransactionType.expense);
        final newResult = await categoryFinder.findCategorySuggestions(text, TransactionType.expense);
        
        expect(oldResult, equals(categoryId));
        expect(newResult.suggestions.first.categoryId, equals(categoryId));
      });
    });

    group('Performance', () {
      test('should complete suggestions within reasonable time', () async {
        final stopwatch = Stopwatch()..start();
        
        await categoryFinder.findCategorySuggestions(
          'restaurant food dining expensive meal', 
          TransactionType.expense,
        );
        
        stopwatch.stop();
        
        // Should complete within 100ms for typical usage
        expect(stopwatch.elapsedMilliseconds, lessThan(100));
      });

      test('should handle multiple concurrent requests', () async {
        final futures = List.generate(10, (index) => 
          categoryFinder.findCategorySuggestions(
            'test transaction $index', 
            TransactionType.expense,
          ),
        );
        
        final results = await Future.wait(futures);
        
        expect(results, hasLength(10));
        for (final result in results) {
          expect(result, isA<CategorySuggestionResult>());
        }
      });
    });
  });
}
