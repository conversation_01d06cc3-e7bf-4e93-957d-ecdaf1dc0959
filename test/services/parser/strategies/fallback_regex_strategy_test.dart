import 'package:flutter_test/flutter_test.dart';
import 'dart:ui';
import '../../../../lib/services/parser/strategies/fallback_regex_strategy.dart';
import '../../../../lib/services/parser/fallback_parser_service.dart';
import '../../../../lib/services/parser/parsing_context.dart';
import '../../../../lib/services/parser/parsing_config.dart';
import '../../../../lib/models/transaction_model.dart';
import '../../../../lib/models/parse_result.dart';
import '../../../../lib/models/localization_data.dart';
import '../../../helpers/test_helpers.dart';
import '../../../mocks/mock_storage_service.dart';
import '../../../mocks/mock_localization_service.dart';

void main() {
  group('FallbackRegexStrategy Tests', () {
    late FallbackRegexStrategy strategy;
    late FallbackParserService fallbackParser;
    late MockStorageService mockStorage;
    late MockLocalizationService mockLocalization;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      mockStorage.setString('default_currency', 'USD');
      
      mockLocalization = MockLocalizationService();
      // Set up default English localization data
      const englishData = LocalizationData(
        locale: 'en',
        decimalSeparator: '.',
        thousandsSeparator: ',',
        expenseKeywords: ['spent', 'paid', 'bought', 'purchase'],
        incomeKeywords: ['received', 'earned', 'income', 'salary'],
        loanKeywords: ['borrowed', 'lent', 'loan'],
        currencySymbols: ['\$', '€', '£', '¥'],
        specialPatterns: {},
      );
      mockLocalization.setMockData('en', englishData);
      
      fallbackParser = FallbackParserService(
        mockStorage,
        localizationService: mockLocalization,
      );
      
      strategy = FallbackRegexStrategy(fallbackParser, const ParsingConfig());
    });

    tearDown(() {
      mockLocalization.reset();
    });

    group('Strategy Interface Compliance', () {
      test('should return correct strategy name', () {
        expect(strategy.name, equals('FallbackRegexStrategy'));
      });

      test('should return ParseResult and never null', () async {
        final context = ParsingContext(text: 'Coffee \$5.50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result, isA<ParseResult>());
      });

      test('should handle empty text gracefully', () async {
        final context = ParsingContext(text: '');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should return failed result for empty text
        expect(result!.status, equals(ParseStatus.failed));
      });

      test('should handle whitespace-only text', () async {
        final context = ParsingContext(text: '   \n\t  ');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should return failed result for whitespace-only text
        expect(result!.status, equals(ParseStatus.failed));
      });
    });

    group('Delegation Behavior', () {
      test('should delegate to FallbackParserService correctly', () async {
        final context = ParsingContext(text: 'Lunch \$12.50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(12.50));
        expect(result!.transaction.description, equals('Lunch \$12.50'));
      });

      test('should return identical results to direct FallbackParserService calls', () async {
        const testText = 'Coffee shop \$4.75';
        const testLocale = Locale('en');

        // Call strategy
        final strategyContext = ParsingContext(text: testText, locale: testLocale);
        final strategyResult = await strategy.execute(strategyContext);

        // Call fallback parser directly
        final directResult = await fallbackParser.parseTransaction(testText, locale: testLocale);

        expect(strategyResult, isNotNull);
        expect(strategyResult!.status, equals(directResult.status));
        expect(strategyResult.transaction.amount, equals(directResult.transaction.amount));
        expect(strategyResult.transaction.type, equals(directResult.transaction.type));
        expect(strategyResult.transaction.description, equals(directResult.transaction.description));
        expect(strategyResult.transaction.currencyCode, equals(directResult.transaction.currencyCode));
      });

      test('should handle complex transactions like direct parser', () async {
        const testText = 'Grocery shopping \$85.25 #food #weekly';

        final strategyContext = ParsingContext(text: testText);
        final strategyResult = await strategy.execute(strategyContext);

        final directResult = await fallbackParser.parseTransaction(testText);

        expect(strategyResult, isNotNull);
        expect(strategyResult!.transaction.amount, equals(directResult.transaction.amount));
        expect(strategyResult.transaction.tags, equals(directResult.transaction.tags));
      });
    });

    group('Locale Passing', () {
      test('should pass locale from context to FallbackParserService', () async {
        // Set up Spanish localization data
        const spanishData = LocalizationData(
          locale: 'es',
          decimalSeparator: ',',
          thousandsSeparator: '.',
          expenseKeywords: ['gasté', 'pagué', 'compré'],
          incomeKeywords: ['recibí', 'gané', 'ingreso'],
          loanKeywords: ['presté', 'pedí prestado'],
          currencySymbols: ['€', '\$'],
          specialPatterns: {},
        );
        mockLocalization.setMockData('es', spanishData);

        const spanishLocale = Locale('es');
        final context = ParsingContext(text: 'Café €3,50', locale: spanishLocale);
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should parse with Spanish locale (comma as decimal separator)
        expect(result!.transaction.amount, equals(3.50));
        expect(result!.transaction.currencyCode, equals('EUR'));
      });

      test('should handle null locale gracefully', () async {
        final context = ParsingContext(text: 'Tea \$2.25', locale: null);
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(2.25));
        // Should default to English locale behavior
      });

      test('should work with different locales', () async {
        // Test with English locale
        final enContext = ParsingContext(text: 'Lunch \$15.00', locale: const Locale('en'));
        final enResult = await strategy.execute(enContext);

        expect(enResult, isNotNull);
        expect(enResult!.transaction.amount, equals(15.00));
        expect(enResult.transaction.currencyCode, equals('USD'));

        // Test with Spanish locale (if Spanish data is available)
        if (mockLocalization.hasMockDataFor('es')) {
          final esContext = ParsingContext(text: 'Almuerzo €15,00', locale: const Locale('es'));
          final esResult = await strategy.execute(esContext);

          expect(esResult, isNotNull);
          expect(esResult!.transaction.amount, equals(15.00));
          expect(esResult.transaction.currencyCode, equals('EUR'));
        }
      });
    });

    group('Various Input Types', () {
      test('should handle expense transactions', () async {
        final context = ParsingContext(text: 'Spent \$25 on dinner');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(25.0));
        expect(result!.transaction.type, equals(TransactionType.expense));
      });

      test('should handle income transactions', () async {
        final context = ParsingContext(text: 'Received salary \$3000');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(3000.0));
        expect(result!.transaction.type, equals(TransactionType.income));
      });

      test('should handle loan transactions', () async {
        final context = ParsingContext(text: 'Borrowed \$500 from bank');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(500.0));
        expect(result!.transaction.type, equals(TransactionType.loan));
      });

      test('should handle transactions with hashtags', () async {
        final context = ParsingContext(text: 'Movie tickets \$20 #entertainment #weekend');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(20.0));
        expect(result!.transaction.tags, containsAll(['entertainment', 'weekend']));
      });

      test('should handle different currencies', () async {
        final testCases = [
          {'text': 'Coffee €4.50', 'expectedCurrency': 'EUR'},
          {'text': 'Tea £3.25', 'expectedCurrency': 'GBP'},
          {'text': 'Sushi ¥1200', 'expectedCurrency': 'JPY'},
        ];

        for (final testCase in testCases) {
          final context = ParsingContext(text: testCase['text'] as String);
          final result = await strategy.execute(context);

          expect(result, isNotNull);
          expect(result!.transaction.currencyCode, equals(testCase['expectedCurrency']));
        }
      });
    });

    group('Error Cases', () {
      test('should handle malformed input gracefully', () async {
        final context = ParsingContext(text: 'Invalid transaction text without amount');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should return failed result for malformed input
        expect(result!.status, equals(ParseStatus.failed));
      });

      test('should propagate errors from FallbackParserService', () async {
        // Make localization service throw error
        mockLocalization.setShouldThrowError(true);

        final context = ParsingContext(text: 'Coffee \$5.50');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should still return a result even on error (fallback behavior)
        expect(result!.status, equals(ParseStatus.failed));
      });

      test('should handle very long input text', () async {
        final longText = 'Coffee ' * 1000 + '\$5.50';
        final context = ParsingContext(text: longText);
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(5.50));
      });

      test('should handle special characters in text', () async {
        final context = ParsingContext(text: 'Café & Restaurant \$25.50 #special-chars');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(25.50));
      });
    });

    group('Localization Support', () {
      test('should work with English locale', () async {
        final context = ParsingContext(
          text: 'Breakfast \$8.75',
          locale: const Locale('en'),
        );
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(8.75));
        expect(result!.transaction.currencyCode, equals('USD'));
      });

      test('should handle unsupported locales gracefully', () async {
        final context = ParsingContext(
          text: 'Dinner \$30.00',
          locale: const Locale('fr'), // French not set up in mock
        );
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        // Should fall back to default behavior
        expect(result!.transaction.amount, equals(30.00));
      });

      test('should maintain locale context throughout parsing', () async {
        const locale = Locale('en');
        final context = ParsingContext(text: 'Lunch \$15.50', locale: locale);
        
        // Verify that the same locale is used consistently
        final result = await strategy.execute(context);
        expect(result, isNotNull);
        
        // The result should be consistent with English locale parsing
        expect(result!.transaction.amount, equals(15.50));
        expect(result!.transaction.currencyCode, equals('USD'));
      });
    });
  });
}
