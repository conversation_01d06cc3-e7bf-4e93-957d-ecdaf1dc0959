import 'package:flutter_test/flutter_test.dart';
import 'package:uuid/uuid.dart';
import '../../../../lib/services/parser/strategies/learned_association_strategy.dart';
import '../../../../lib/services/parser/parsing_config.dart';
import '../../../../lib/services/parser/parsing_context.dart';
import '../../../../lib/services/parser/learned_association_service.dart';
import '../../../../lib/models/transaction_model.dart';
import '../../../mocks/mock_storage_service.dart';

void main() {
  group('LearnedAssociationStrategy Configuration Tests', () {
    late LearnedAssociationService learnedService;
    late MockStorageService mockStorage;
    late Uuid uuid;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      uuid = const Uuid();

      // Use real service with mock storage
      learnedService = await LearnedAssociationService.getInstance(mockStorage);
    });

    tearDown(() async {
      await learnedService.clearAllData();
      LearnedAssociationService.resetInstance();
      mockStorage.reset();
    });

    group('Default Currency Tests', () {
      test('should use custom defaultCurrency when association has no currency', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'EUR');

        final strategy = LearnedAssociationStrategy(
          learnedService,
          mockStorage,
          uuid,
          customConfig,
        );

        // Learn an association without explicit currency
        await learnedService.learn(
          'coffee shop',
          confirmedAmount: 5.50,
          type: TransactionType.expense,
          categoryId: 'food',
        );

        await mockStorage.saveDefaultCurrency('EUR');

        final context = ParsingContext(text: 'coffee shop');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.isSuccess, isTrue);
        expect(result.transaction.currencyCode, equals('EUR'));
        expect(result.transaction.amount, equals(5.50));
      });

      test('should use USD with default config', () async {
        const defaultConfig = ParsingConfig.defaults;

        final strategy = LearnedAssociationStrategy(
          learnedService,
          mockStorage,
          uuid,
          defaultConfig,
        );

        await learnedService.learn(
          'restaurant',
          confirmedAmount: 25.00,
          type: TransactionType.expense,
          categoryId: 'food',
        );

        await mockStorage.saveDefaultCurrency('USD');

        final context = ParsingContext(text: 'restaurant');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.isSuccess, isTrue);
        expect(result.transaction.currencyCode, equals('USD'));
        expect(result.transaction.amount, equals(25.00));
      });

      test('should fallback to config default when association has no currency', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'EUR');

        final strategy = LearnedAssociationStrategy(
          learnedService,
          mockStorage,
          uuid,
          customConfig,
        );

        // Learn association without currency (LearnedAssociation doesn't store currency)
        await learnedService.learn(
          'gas station',
          confirmedAmount: 45.00,
          type: TransactionType.expense,
          categoryId: 'transport',
        );

        await mockStorage.saveDefaultCurrency('EUR');

        final context = ParsingContext(text: 'gas station');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.isSuccess, isTrue);
        // Since LearnedAssociation doesn't store currency, should use config default
        expect(result.transaction.currencyCode, equals('EUR'));
        expect(result.transaction.amount, equals(45.00));
      });
    });

    group('Configuration Consistency Tests', () {
      test('should properly store and access configuration', () async {
        const customConfig = ParsingConfig(
          defaultCurrency: 'CAD',
          strictEmbeddedLetterThreshold: 2,
          abbreviationPattern: '[kKmM]',
        );

        final strategy = LearnedAssociationStrategy(
          learnedService,
          mockStorage,
          uuid,
          customConfig,
        );

        await learnedService.learn(
          'grocery store',
          confirmedAmount: 75.50,
          type: TransactionType.expense,
          categoryId: 'food',
        );

        await mockStorage.saveDefaultCurrency('CAD');

        final context = ParsingContext(text: 'grocery store');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.isSuccess, isTrue);
        expect(result.transaction.currencyCode, equals('CAD'));
      });

      test('should not interfere with learned association logic', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'JPY');

        final strategy = LearnedAssociationStrategy(
          learnedService,
          mockStorage,
          uuid,
          customConfig,
        );

        await learnedService.learn(
          'taxi ride',
          confirmedAmount: 15.75,
          type: TransactionType.expense,
          categoryId: 'transport',
        );

        await mockStorage.saveDefaultCurrency('JPY');

        final context = ParsingContext(text: 'taxi ride');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.isSuccess, isTrue);
        // Configuration should not interfere with association data
        expect(result.transaction.amount, equals(15.75));
        expect(result.transaction.type, equals(TransactionType.expense));
        expect(result.transaction.categoryId, equals('transport'));
        expect(result.transaction.currencyCode, equals('JPY')); // Uses config default
      });
    });

    group('Integration Tests', () {
      test('should handle multiple operations with consistent configuration', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'AUD');

        final strategy = LearnedAssociationStrategy(
          learnedService,
          mockStorage,
          uuid,
          customConfig,
        );

        // Learn multiple associations
        await learnedService.learn(
          'morning coffee',
          confirmedAmount: 4.50,
          type: TransactionType.expense,
          categoryId: 'food',
        );

        await learnedService.learn(
          'lunch special',
          confirmedAmount: 12.00,
          type: TransactionType.expense,
          categoryId: 'food',
        );

        await mockStorage.saveDefaultCurrency('AUD');

        final testCases = [
          {'text': 'morning coffee', 'amount': 4.50},
          {'text': 'lunch special', 'amount': 12.00},
        ];

        for (final testCase in testCases) {
          final text = testCase['text'] as String;
          final amount = testCase['amount'] as double;

          final context = ParsingContext(text: text);
          final result = await strategy.execute(context);

          expect(result, isNotNull, reason: 'Failed for: $text');
          expect(result!.isSuccess, isTrue, reason: 'Should succeed for: $text');
          expect(result.transaction.currencyCode, equals('AUD'), reason: 'Wrong currency for: $text');
          expect(result.transaction.amount, equals(amount), reason: 'Wrong amount for: $text');
        }
      });

      test('should return null when no association found', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'NZD');

        final strategy = LearnedAssociationStrategy(
          learnedService,
          mockStorage,
          uuid,
          customConfig,
        );

        // No association learned for this text
        final context = ParsingContext(text: 'unknown text');
        final result = await strategy.execute(context);

        // Should return null when no association found
        expect(result, isNull);
      });

      test('should handle fallback to configuration when association data is incomplete', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'CHF');

        final strategy = LearnedAssociationStrategy(
          learnedService,
          mockStorage,
          uuid,
          customConfig,
        );

        // Learn association with minimal data (only category, no amount or type)
        await learnedService.learn('parking meter', categoryId: 'transport');

        await mockStorage.saveDefaultCurrency('CHF');

        final context = ParsingContext(text: 'parking meter');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.isSuccess, isTrue);
        // Should use configuration defaults for missing data
        expect(result.transaction.currencyCode, equals('CHF'));
        expect(result.transaction.type, equals(TransactionType.expense)); // Default type
        expect(result.transaction.categoryId, equals('transport')); // From association
        expect(result.transaction.amount, equals(0.0)); // Default amount when not provided
      });
    });
  });
}
