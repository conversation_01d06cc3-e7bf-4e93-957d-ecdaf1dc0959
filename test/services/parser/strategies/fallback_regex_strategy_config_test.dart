import 'package:flutter_test/flutter_test.dart';
import '../../../../lib/services/parser/strategies/fallback_regex_strategy.dart';
import '../../../../lib/services/parser/parsing_config.dart';
import '../../../../lib/services/parser/parsing_context.dart';
import '../../../../lib/models/parse_result.dart';
import '../../../mocks/mock_storage_service.dart';
import '../../../mocks/mock_localization_service.dart';
import '../../../mocks/mock_fallback_parser_service.dart';

void main() {
  group('FallbackRegexStrategy Configuration Tests', () {
    late MockStorageService mockStorage;
    late MockLocalizationService mockLocalization;
    late MockFallbackParserService mockFallbackParser;

    setUp(() {
      mockStorage = MockStorageService();
      mockLocalization = MockLocalizationService();
      mockFallbackParser = MockFallbackParserService(
        mockStorage,
        localizationService: mockLocalization,
      );
    });

    tearDown(() {
      mockStorage.reset();
      mockLocalization.reset();
      mockFallbackParser.reset();
    });

    group('Default Currency Tests', () {
      test('should use custom defaultCurrency in error fallback scenarios', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'EUR');

        final strategy = FallbackRegexStrategy(mockFallbackParser, customConfig);

        // Configure mock to throw an error to trigger _createBasicTransaction
        mockFallbackParser.setShouldThrowError(true, 'Mock parsing error');

        final context = ParsingContext(text: 'invalid input that causes error');
        final result = await strategy.execute(context);

        // Should return a failed result with the custom default currency
        expect(result, isNotNull);
        expect(result!.status, equals(ParseStatus.failed));
        expect(result.transaction.currencyCode, equals('EUR'));
        expect(result.transaction.amount, equals(0.0));
        expect(result.transaction.description, equals('invalid input that causes error'));
      });

      test('should use USD with default config in error scenarios', () async {
        const defaultConfig = ParsingConfig.defaults;

        final strategy = FallbackRegexStrategy(mockFallbackParser, defaultConfig);

        // Configure mock to throw an error to trigger _createBasicTransaction
        mockFallbackParser.setShouldThrowError(true, 'Mock parsing error');

        final context = ParsingContext(text: '\$15.75 lunch');
        final result = await strategy.execute(context);

        // Should return a failed result with the default currency (USD)
        expect(result, isNotNull);
        expect(result!.status, equals(ParseStatus.failed));
        expect(result.transaction.currencyCode, equals('USD'));
        expect(result.transaction.amount, equals(0.0));
      });
    });

    group('Configuration Consistency Tests', () {
      test('should properly store configuration without interfering with delegation', () async {
        const customConfig = ParsingConfig(
          defaultCurrency: 'GBP',
          strictEmbeddedLetterThreshold: 2,
          abbreviationPattern: '[kKmM]',
        );

        final strategy = FallbackRegexStrategy(mockFallbackParser, customConfig);

        await mockStorage.saveDefaultCurrency('GBP');

        // Configure mock to return a successful result
        final mockResult = mockFallbackParser.createMockSuccessResult(
          '£30.00 dinner',
          amount: 30.0,
          currencyCode: 'GBP',
        );
        mockFallbackParser.setMockResult(mockResult);

        // Test that normal delegation to FallbackParserService works
        final context = ParsingContext(text: '£30.00 dinner');
        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(result!.isSuccess, isTrue);
        expect(result.transaction.amount, equals(30.0));
        expect(result.transaction.currencyCode, equals('GBP'));
      });

      test('should not interfere with FallbackParserService logic', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'CAD');

        final strategy = FallbackRegexStrategy(mockFallbackParser, customConfig);

        await mockStorage.saveDefaultCurrency('CAD');

        // Test various inputs that FallbackParserService should handle
        final testCases = [
          {'text': '\$50.00 groceries', 'amount': 50.0},
          {'text': '25.75 gas', 'amount': 25.75},
          {'text': 'spent 15.50 on coffee', 'amount': 15.50},
          {'text': 'received 100.00 bonus', 'amount': 100.0},
        ];

        for (final testCase in testCases) {
          final text = testCase['text'] as String;
          final amount = testCase['amount'] as double;

          // Configure mock to return successful result for each test
          final mockResult = mockFallbackParser.createMockSuccessResult(
            text,
            amount: amount,
            currencyCode: 'CAD',
          );
          mockFallbackParser.setMockResult(mockResult);

          final context = ParsingContext(text: text);
          final result = await strategy.execute(context);

          expect(result, isNotNull, reason: 'Failed for: $text');
          expect(result!.isSuccess, isTrue, reason: 'Should succeed for: $text');
          expect(result.transaction.amount, equals(amount), reason: 'Wrong amount for: $text');
        }
      });
    });

    group('Error Handling Tests', () {
      test('should use configured default currency during error scenarios', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'JPY');

        final strategy = FallbackRegexStrategy(mockFallbackParser, customConfig);

        // Configure mock to throw an error to trigger _createBasicTransaction
        mockFallbackParser.setShouldThrowError(true, 'Mock parsing error');

        final context = ParsingContext(text: '¥1000 sushi');
        final result = await strategy.execute(context);

        // Should return a failed result with the configured default currency
        expect(result, isNotNull);
        expect(result!.status, equals(ParseStatus.failed));
        expect(result.transaction.currencyCode, equals('JPY'));
        expect(result.transaction.amount, equals(0.0));
        expect(result.transaction.description, equals('¥1000 sushi'));
      });

      test('should provide consistent behavior across error cases', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'AUD');

        final strategy = FallbackRegexStrategy(mockFallbackParser, customConfig);

        // Configure mock to throw an error for all operations
        mockFallbackParser.setShouldThrowError(true, 'Mock parsing error');

        // Test multiple operations to ensure consistency
        final testTexts = [
          'A\$20.00 coffee',
          'A\$35.50 lunch',
          'A\$8.75 snack',
        ];

        for (final text in testTexts) {
          final context = ParsingContext(text: text);
          final result = await strategy.execute(context);

          expect(result, isNotNull, reason: 'Failed for: $text');
          expect(result!.status, equals(ParseStatus.failed), reason: 'Should fail for: $text');
          expect(result.transaction.currencyCode, equals('AUD'), reason: 'Wrong currency for: $text');
          expect(result.transaction.amount, equals(0.0), reason: 'Wrong amount for: $text');
          expect(result.transaction.description, equals(text), reason: 'Wrong description for: $text');
        }
      });
    });

    group('Integration Tests', () {
      test('should work correctly with various configuration values', () async {
        final testConfigs = [
          const ParsingConfig(defaultCurrency: 'USD'),
          const ParsingConfig(defaultCurrency: 'EUR'),
          const ParsingConfig(defaultCurrency: 'GBP'),
          const ParsingConfig(defaultCurrency: 'JPY'),
        ];

        for (final config in testConfigs) {
          final strategy = FallbackRegexStrategy(mockFallbackParser, config);

          // Configure mock to return successful result
          final mockResult = mockFallbackParser.createMockSuccessResult(
            '25.00 test transaction',
            amount: 25.0,
            currencyCode: config.defaultCurrency,
          );
          mockFallbackParser.setMockResult(mockResult);

          final context = ParsingContext(text: '25.00 test transaction');
          final result = await strategy.execute(context);

          expect(result, isNotNull,
              reason: 'Failed with currency: ${config.defaultCurrency}');
          expect(result!.isSuccess, isTrue,
              reason: 'Should succeed with currency: ${config.defaultCurrency}');
          expect(result.transaction.currencyCode, equals(config.defaultCurrency),
              reason: 'Wrong currency: ${config.defaultCurrency}');
        }
      });

      test('should maintain configuration across multiple parsing operations', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'CHF');

        final strategy = FallbackRegexStrategy(mockFallbackParser, customConfig);

        // Perform multiple parsing operations
        final testCases = [
          {'text': 'CHF 45.00 restaurant', 'amount': 45.0},
          {'text': 'CHF 12.50 coffee', 'amount': 12.50},
          {'text': 'CHF 75.25 shopping', 'amount': 75.25},
          {'text': 'CHF 30.00 transport', 'amount': 30.0},
        ];

        for (final testCase in testCases) {
          final text = testCase['text'] as String;
          final amount = testCase['amount'] as double;

          // Configure mock to return successful result for each test
          final mockResult = mockFallbackParser.createMockSuccessResult(
            text,
            amount: amount,
            currencyCode: 'CHF',
          );
          mockFallbackParser.setMockResult(mockResult);

          final context = ParsingContext(text: text);
          final result = await strategy.execute(context);

          expect(result, isNotNull, reason: 'Failed for: $text');
          expect(result!.isSuccess, isTrue, reason: 'Should succeed for: $text');
          expect(result.transaction.amount, equals(amount), reason: 'Wrong amount for: $text');
          expect(result.transaction.currencyCode, equals('CHF'), reason: 'Wrong currency for: $text');
        }
      });

      test('should handle edge cases with configuration', () async {
        const customConfig = ParsingConfig(
          defaultCurrency: 'NZD',
          strictEmbeddedLetterThreshold: 1,
          embeddedLetterThreshold: 1,
          abbreviationPattern: '[kK]',
        );

        final strategy = FallbackRegexStrategy(mockFallbackParser, customConfig);

        // Test edge cases - configure mock to throw errors for all edge cases
        mockFallbackParser.setShouldThrowError(true, 'Mock parsing error');

        final edgeCases = [
          '', // Empty string
          '   ', // Whitespace only
          'no numbers here', // No amounts
          'NZ\$0.00', // Zero amount
          'NZ\$999999.99', // Large amount
        ];

        for (final text in edgeCases) {
          final context = ParsingContext(text: text);
          final result = await strategy.execute(context);

          expect(result, isNotNull, reason: 'Failed for edge case: "$text"');
          expect(result!.status, equals(ParseStatus.failed), reason: 'Should fail for edge case: "$text"');
          expect(result.transaction.currencyCode, equals('NZD'), reason: 'Wrong currency for edge case: "$text"');
        }
      });
    });

    group('Configuration Isolation Tests', () {
      test('should not affect other strategy instances', () async {
        const config1 = ParsingConfig(defaultCurrency: 'USD');
        const config2 = ParsingConfig(defaultCurrency: 'EUR');

        final strategy1 = FallbackRegexStrategy(mockFallbackParser, config1);
        final strategy2 = FallbackRegexStrategy(mockFallbackParser, config2);

        // Both strategies should work independently
        expect(strategy1, isNotNull);
        expect(strategy2, isNotNull);

        // They should be different instances
        expect(identical(strategy1, strategy2), isFalse);
      });

      test('should maintain configuration integrity', () async {
        const originalConfig = ParsingConfig(defaultCurrency: 'GBP');
        const modifiedConfig = ParsingConfig(defaultCurrency: 'EUR');

        final strategy1 = FallbackRegexStrategy(mockFallbackParser, originalConfig);
        final strategy2 = FallbackRegexStrategy(mockFallbackParser, modifiedConfig);

        // Each strategy should maintain its own configuration
        expect(strategy1, isNotNull);
        expect(strategy2, isNotNull);

        // Configuration should not be shared between instances
        expect(identical(strategy1, strategy2), isFalse);
      });
    });
  });
}
