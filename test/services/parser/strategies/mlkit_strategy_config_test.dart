import 'package:flutter_test/flutter_test.dart';
import 'package:uuid/uuid.dart';
import '../../../../lib/services/parser/strategies/mlkit_strategy.dart';
import '../../../../lib/services/parser/parsing_config.dart';
import '../../../../lib/services/parser/parsing_context.dart';
import '../../../../lib/models/amount_candidate.dart';
import '../../../mocks/mock_storage_service.dart';
import '../../../mocks/mock_category_finder_service.dart';
import '../../../mocks/mock_entity_extractor.dart';

void main() {
  group('MlKitStrategy Configuration Tests', () {
    late MockStorageService mockStorage;
    late MockCategoryFinderService mockCategoryFinder;
    late MockEntityExtractor mockEntityExtractor;
    late Uuid uuid;

    setUp(() {
      mockStorage = MockStorageService();
      mockCategoryFinder = MockCategoryFinderService();
      mockEntityExtractor = MockEntityExtractor();
      uuid = const Uuid();
    });

    tearDown(() {
      mockStorage.reset();
      mockCategoryFinder.reset();
      mockEntityExtractor.reset();
    });

    group('Embedded Letter Threshold Tests', () {
      test('should use custom strictEmbeddedLetterThreshold for embedded detection', () async {
        // Create custom config with lower threshold
        const customConfig = ParsingConfig(strictEmbeddedLetterThreshold: 1);
        
        final strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
          customConfig,
        );

        // Test with text that has 1 letter on each side of number
        // With threshold=1, "A2B" should be considered embedded
        // With default threshold=3, "A2B" would not be embedded
        
        // Create a candidate that represents "2" in "A2B"
        final candidate = AmountCandidate.fromRawNumberFinder(
          amount: 2.0,
          currency: null,
          start: 1, // Position of "2" in "A2B"
          end: 2,
          sourceText: '2',
        );

        // Use reflection or create a test method to access _isEmbeddedInVendorName
        // For now, we'll test the behavior indirectly through parsing
        
        // Mock entity extractor to return no entities (force raw number finder usage)
        mockEntityExtractor.setMockEntities([]);
        
        // Mock storage for default currency
        mockStorage.setMockDefaultCurrency('USD');
        
        final context = ParsingContext(text: 'A2B coffee shop');
        final result = await strategy.execute(context);
        
        // With custom threshold=1, the "2" should be filtered out as embedded
        // So we expect either null result or a fallback transaction
        expect(result, isNotNull);
        if (result!.isSuccess || result.needsAmountConfirmation) {
          // If parsing succeeded, the amount should not be 2.0 (filtered out)
          expect(result.transaction.amount, isNot(equals(2.0)));
        }
      });

      test('should use default strictEmbeddedLetterThreshold with default config', () async {
        const defaultConfig = ParsingConfig.defaults;
        
        final strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
          defaultConfig,
        );

        // Test with text that has 1 letter on each side of number
        // With default threshold=3, "A2B" should NOT be considered embedded
        
        mockEntityExtractor.setMockEntities([]);
        mockStorage.setMockDefaultCurrency('USD');
        
        final context = ParsingContext(text: 'A2B coffee shop');
        final result = await strategy.execute(context);
        
        // With default threshold=3, the "2" should not be filtered out
        expect(result, isNotNull);
      });

      test('should handle embeddedLetterThreshold for basic detection', () async {
        const customConfig = ParsingConfig(embeddedLetterThreshold: 1);
        
        final strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
          customConfig,
        );

        mockEntityExtractor.setMockEntities([]);
        mockStorage.setMockDefaultCurrency('USD');
        
        final context = ParsingContext(text: 'AB2CD restaurant');
        final result = await strategy.execute(context);
        
        expect(result, isNotNull);
        // The behavior should be consistent with the configured threshold
      });
    });

    group('Default Currency Tests', () {
      test('should use custom defaultCurrency when no currency detected', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'EUR');
        
        final strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
          customConfig,
        );

        mockEntityExtractor.setMockEntities([]);
        mockStorage.setMockDefaultCurrency('EUR'); // Storage should return EUR
        mockCategoryFinder.setMockCategory('food');
        
        final context = ParsingContext(text: '25.50 coffee'); // No currency symbol
        final result = await strategy.execute(context);
        
        expect(result, isNotNull);
        expect(result!.transaction.currencyCode, equals('EUR'));
      });

      test('should use USD with default config', () async {
        const defaultConfig = ParsingConfig.defaults;
        
        final strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
          defaultConfig,
        );

        mockEntityExtractor.setMockEntities([]);
        mockStorage.setMockDefaultCurrency('USD'); // Storage returns USD
        mockCategoryFinder.setMockCategory('food');
        
        final context = ParsingContext(text: '25.50 coffee'); // No currency symbol
        final result = await strategy.execute(context);
        
        expect(result, isNotNull);
        expect(result!.transaction.currencyCode, equals('USD'));
      });
    });

    group('Abbreviation Pattern Tests', () {
      test('should use custom abbreviationPattern for detection', () async {
        // Custom pattern that only includes 'k' and 'K' (no m, M, b, B)
        const customConfig = ParsingConfig(abbreviationPattern: '[kK]');
        
        final strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
          customConfig,
        );

        mockEntityExtractor.setMockEntities([]);
        mockStorage.setMockDefaultCurrency('USD');
        mockCategoryFinder.setMockCategory('food');
        
        // Test with 'k' abbreviation (should be recognized)
        final contextK = ParsingContext(text: '100k salary');
        final resultK = await strategy.execute(contextK);
        
        expect(resultK, isNotNull);
        expect(resultK!.transaction.amount, equals(100000.0));
        
        // Test with 'm' abbreviation (should not be recognized with custom pattern)
        final contextM = ParsingContext(text: '2m bonus');
        final resultM = await strategy.execute(contextM);

        expect(resultM, isNotNull);
        // With custom pattern, 'm' should not be recognized as abbreviation
        // So amount should be 2.0, not 2000000.0
        // Note: This test may need adjustment based on actual AmountUtils behavior
        expect(resultM!.transaction.amount, isNot(equals(2000000.0)));
      });

      test('should use default abbreviationPattern with default config', () async {
        const defaultConfig = ParsingConfig.defaults;
        
        final strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
          defaultConfig,
        );

        mockEntityExtractor.setMockEntities([]);
        mockStorage.setMockDefaultCurrency('USD');
        mockCategoryFinder.setMockCategory('food');
        
        // Test with various abbreviations (all should be recognized)
        final testCases = [
          {'text': '100k salary', 'expectedAmount': 100000.0},
          {'text': '2M bonus', 'expectedAmount': 2000000.0},
          {'text': '1b investment', 'expectedAmount': 1000000000.0},
        ];

        for (final testCase in testCases) {
          final context = ParsingContext(text: testCase['text'] as String);
          final result = await strategy.execute(context);
          
          expect(result, isNotNull);
          expect(result!.transaction.amount, equals(testCase['expectedAmount']),
              reason: 'Failed for: ${testCase['text']}');
        }
      });
    });

    group('Integration Tests', () {
      test('should properly store and use configuration', () async {
        const customConfig = ParsingConfig(
          strictEmbeddedLetterThreshold: 2,
          defaultCurrency: 'GBP',
          abbreviationPattern: '[kKmM]',
        );
        
        final strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
          customConfig,
        );

        // Verify configuration is accessible (indirectly through behavior)
        mockEntityExtractor.setMockEntities([]);
        mockStorage.setMockDefaultCurrency('GBP');
        mockCategoryFinder.setMockCategory('food');
        
        final context = ParsingContext(text: '100k lunch');
        final result = await strategy.execute(context);
        
        expect(result, isNotNull);
        expect(result!.transaction.amount, equals(100000.0)); // 'k' should work
        expect(result.transaction.currencyCode, equals('GBP')); // Custom currency
      });

      test('should handle configuration consistency across multiple operations', () async {
        const customConfig = ParsingConfig(defaultCurrency: 'CAD');
        
        final strategy = MlKitStrategy(
          mockEntityExtractor,
          mockStorage,
          mockCategoryFinder,
          uuid,
          true,
          customConfig,
        );

        mockEntityExtractor.setMockEntities([]);
        mockStorage.setMockDefaultCurrency('CAD');
        mockCategoryFinder.setMockCategory('food');
        
        // Multiple parsing operations should use same configuration
        final contexts = [
          ParsingContext(text: '10.50 coffee'),
          ParsingContext(text: '25.00 lunch'),
          ParsingContext(text: '5.75 snack'),
        ];

        for (final context in contexts) {
          final result = await strategy.execute(context);
          expect(result, isNotNull);
          expect(result!.transaction.currencyCode, equals('CAD'));
        }
      });
    });
  });
}
