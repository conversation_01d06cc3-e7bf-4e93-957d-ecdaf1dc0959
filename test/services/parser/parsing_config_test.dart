import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/parsing_config.dart';

void main() {
  group('ParsingConfig', () {
    group('Defaults', () {
      test('should have correct default values', () {
        const config = ParsingConfig.defaults;

        expect(config.embeddedLetterThreshold, equals(2));
        expect(config.strictEmbeddedLetterThreshold, equals(3));
        expect(config.thousandCutoff, equals(1000.0));
        expect(config.millionCutoff, equals(1000000.0));
        expect(config.billionCutoff, equals(1000000000.0));
        expect(config.defaultCurrency, equals('USD'));
        expect(config.abbreviationPattern, equals('[kKmMbB]'));
      });

      test('should create default instance with const constructor', () {
        const config = ParsingConfig();

        expect(config.embeddedLetterThreshold, equals(2));
        expect(config.strictEmbeddedLetterThreshold, equals(3));
        expect(config.thousandCutoff, equals(1000.0));
        expect(config.millionCutoff, equals(1000000.0));
        expect(config.billionCutoff, equals(1000000000.0));
        expect(config.defaultCurrency, equals('USD'));
        expect(config.abbreviationPattern, equals('[kKmMbB]'));
      });
    });

    group('Constructor', () {
      test('should create config with custom values', () {
        const config = ParsingConfig(
          embeddedLetterThreshold: 1,
          strictEmbeddedLetterThreshold: 2,
          thousandCutoff: 500.0,
          millionCutoff: 500000.0,
          billionCutoff: 500000000.0,
          defaultCurrency: 'EUR',
          abbreviationPattern: '[kKmM]',
        );

        expect(config.embeddedLetterThreshold, equals(1));
        expect(config.strictEmbeddedLetterThreshold, equals(2));
        expect(config.thousandCutoff, equals(500.0));
        expect(config.millionCutoff, equals(500000.0));
        expect(config.billionCutoff, equals(500000000.0));
        expect(config.defaultCurrency, equals('EUR'));
        expect(config.abbreviationPattern, equals('[kKmM]'));
      });

      test('should create config with partial custom values', () {
        const config = ParsingConfig(
          defaultCurrency: 'GBP',
          strictEmbeddedLetterThreshold: 1,
        );

        // Custom values
        expect(config.defaultCurrency, equals('GBP'));
        expect(config.strictEmbeddedLetterThreshold, equals(1));

        // Default values for unspecified fields
        expect(config.embeddedLetterThreshold, equals(2));
        expect(config.thousandCutoff, equals(1000.0));
        expect(config.millionCutoff, equals(1000000.0));
        expect(config.billionCutoff, equals(1000000000.0));
        expect(config.abbreviationPattern, equals('[kKmMbB]'));
      });
    });

    group('CopyWith', () {
      test('should override only specified fields', () {
        const original = ParsingConfig.defaults;
        final modified = original.copyWith(
          defaultCurrency: 'EUR',
          strictEmbeddedLetterThreshold: 2,
        );

        // Modified fields
        expect(modified.defaultCurrency, equals('EUR'));
        expect(modified.strictEmbeddedLetterThreshold, equals(2));

        // Unchanged fields
        expect(modified.embeddedLetterThreshold, equals(2));
        expect(modified.thousandCutoff, equals(1000.0));
        expect(modified.millionCutoff, equals(1000000.0));
        expect(modified.billionCutoff, equals(1000000000.0));
        expect(modified.abbreviationPattern, equals('[kKmMbB]'));
      });

      test('should handle single field changes', () {
        const original = ParsingConfig.defaults;
        
        final modifiedCurrency = original.copyWith(defaultCurrency: 'JPY');
        expect(modifiedCurrency.defaultCurrency, equals('JPY'));
        expect(modifiedCurrency.strictEmbeddedLetterThreshold, equals(3));

        final modifiedThreshold = original.copyWith(embeddedLetterThreshold: 5);
        expect(modifiedThreshold.embeddedLetterThreshold, equals(5));
        expect(modifiedThreshold.defaultCurrency, equals('USD'));
      });

      test('should handle multiple field changes', () {
        const original = ParsingConfig.defaults;
        final modified = original.copyWith(
          embeddedLetterThreshold: 1,
          strictEmbeddedLetterThreshold: 1,
          thousandCutoff: 2000.0,
          millionCutoff: 2000000.0,
          billionCutoff: 2000000000.0,
          defaultCurrency: 'CAD',
          abbreviationPattern: '[kKmMbBtT]',
        );

        expect(modified.embeddedLetterThreshold, equals(1));
        expect(modified.strictEmbeddedLetterThreshold, equals(1));
        expect(modified.thousandCutoff, equals(2000.0));
        expect(modified.millionCutoff, equals(2000000.0));
        expect(modified.billionCutoff, equals(2000000000.0));
        expect(modified.defaultCurrency, equals('CAD'));
        expect(modified.abbreviationPattern, equals('[kKmMbBtT]'));
      });

      test('should return new instance (immutability)', () {
        const original = ParsingConfig.defaults;
        final modified = original.copyWith(defaultCurrency: 'EUR');

        expect(identical(original, modified), isFalse);
        expect(original.defaultCurrency, equals('USD'));
        expect(modified.defaultCurrency, equals('EUR'));
      });
    });

    group('Equality', () {
      test('should be equal when all fields match', () {
        const config1 = ParsingConfig(
          embeddedLetterThreshold: 1,
          strictEmbeddedLetterThreshold: 2,
          thousandCutoff: 500.0,
          millionCutoff: 500000.0,
          billionCutoff: 500000000.0,
          defaultCurrency: 'EUR',
          abbreviationPattern: '[kKmM]',
        );

        const config2 = ParsingConfig(
          embeddedLetterThreshold: 1,
          strictEmbeddedLetterThreshold: 2,
          thousandCutoff: 500.0,
          millionCutoff: 500000.0,
          billionCutoff: 500000000.0,
          defaultCurrency: 'EUR',
          abbreviationPattern: '[kKmM]',
        );

        expect(config1, equals(config2));
        expect(config1.hashCode, equals(config2.hashCode));
      });

      test('should not be equal when fields differ', () {
        const config1 = ParsingConfig.defaults;
        const config2 = ParsingConfig(defaultCurrency: 'EUR');

        expect(config1, isNot(equals(config2)));
        expect(config1.hashCode, isNot(equals(config2.hashCode)));
      });

      test('should handle various field differences', () {
        const base = ParsingConfig.defaults;

        final testCases = [
          base.copyWith(embeddedLetterThreshold: 1),
          base.copyWith(strictEmbeddedLetterThreshold: 2),
          base.copyWith(thousandCutoff: 2000.0),
          base.copyWith(millionCutoff: 2000000.0),
          base.copyWith(billionCutoff: 2000000000.0),
          base.copyWith(defaultCurrency: 'EUR'),
          base.copyWith(abbreviationPattern: '[kKmM]'),
        ];

        for (final testCase in testCases) {
          expect(base, isNot(equals(testCase)));
          expect(base.hashCode, isNot(equals(testCase.hashCode)));
        }
      });

      test('should be equal to itself', () {
        const config = ParsingConfig.defaults;
        expect(config, equals(config));
        expect(identical(config, config), isTrue);
      });
    });

    group('Immutability', () {
      test('should be properly immutable', () {
        const config = ParsingConfig.defaults;
        
        // All fields should be final (compile-time check)
        // This test verifies that the class structure supports immutability
        expect(config.embeddedLetterThreshold, isA<int>());
        expect(config.strictEmbeddedLetterThreshold, isA<int>());
        expect(config.thousandCutoff, isA<double>());
        expect(config.millionCutoff, isA<double>());
        expect(config.billionCutoff, isA<double>());
        expect(config.defaultCurrency, isA<String>());
        expect(config.abbreviationPattern, isA<String>());
      });

      test('should maintain immutability through copyWith', () {
        const original = ParsingConfig.defaults;
        final modified1 = original.copyWith(defaultCurrency: 'EUR');
        final modified2 = modified1.copyWith(embeddedLetterThreshold: 1);

        // Original should remain unchanged
        expect(original.defaultCurrency, equals('USD'));
        expect(original.embeddedLetterThreshold, equals(2));

        // First modification should remain unchanged
        expect(modified1.defaultCurrency, equals('EUR'));
        expect(modified1.embeddedLetterThreshold, equals(2));

        // Second modification should have both changes
        expect(modified2.defaultCurrency, equals('EUR'));
        expect(modified2.embeddedLetterThreshold, equals(1));
      });
    });

    group('toString', () {
      test('should provide readable string representation', () {
        const config = ParsingConfig.defaults;
        final str = config.toString();

        expect(str, contains('ParsingConfig'));
        expect(str, contains('embeddedLetterThreshold: 2'));
        expect(str, contains('strictEmbeddedLetterThreshold: 3'));
        expect(str, contains('thousandCutoff: 1000.0'));
        expect(str, contains('millionCutoff: 1000000.0'));
        expect(str, contains('billionCutoff: 1000000000.0'));
        expect(str, contains('defaultCurrency: USD'));
        expect(str, contains('abbreviationPattern: [kKmMbB]'));
      });
    });
  });
}
