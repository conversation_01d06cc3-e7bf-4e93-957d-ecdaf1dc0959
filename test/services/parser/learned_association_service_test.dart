import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/learned_association_service.dart';
import '../../../lib/models/transaction_model.dart';
import '../../mocks/mock_storage_service.dart';

void main() {
  group('LearnedAssociationService Tests', () {
    late LearnedAssociationService service;
    late MockStorageService mockStorage;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      service = await LearnedAssociationService.getInstance(mockStorage);
    });

    tearDown(() async {
      await service.clearAllData();
      // Reset singleton for next test
      LearnedAssociationService.resetInstance();
    });

    group('Singleton Pattern', () {
      test('should return same instance for multiple calls', () async {
        final instance1 = await LearnedAssociationService.getInstance(mockStorage);
        final instance2 = await LearnedAssociationService.getInstance(mockStorage);
        
        expect(identical(instance1, instance2), isTrue);
      });

      test('should initialize properly', () async {
        final instance = await LearnedAssociationService.getInstance(mockStorage);
        expect(instance, isNotNull);
      });
    });

    group('Basic Learning Functionality', () {
      test('should learn and retrieve category association', () async {
        const text = 'starbucks coffee';
        const categoryId = 'food';

        await service.learn(text, categoryId: categoryId);
        final association = await service.getAssociation(text);

        expect(association, isNotNull);
        expect(association!.categoryId, equals(categoryId));
        expect(association.type, isNull);
        expect(association.confidence, equals(1));
      });

      test('should learn and retrieve type association', () async {
        const text = 'salary payment';
        const type = TransactionType.income;

        await service.learn(text, type: type);
        final association = await service.getAssociation(text);

        expect(association, isNotNull);
        expect(association!.type, equals(type));
        expect(association.categoryId, isNull);
        expect(association.confidence, equals(1));
      });

      test('should learn and retrieve both type and category', () async {
        const text = 'grocery shopping';
        const type = TransactionType.expense;
        const categoryId = 'food';

        await service.learn(text, type: type, categoryId: categoryId);
        final association = await service.getAssociation(text);

        expect(association, isNotNull);
        expect(association!.type, equals(type));
        expect(association.categoryId, equals(categoryId));
        expect(association.confidence, equals(1));
      });

      test('should return null when no association exists', () async {
        final association = await service.getAssociation('unknown text');
        expect(association, isNull);
      });
    });

    group('Text Normalization', () {
      test('should handle case insensitive lookup', () async {
        const categoryId = 'food';

        await service.learn('Starbucks Coffee', categoryId: categoryId);
        
        final result1 = await service.getAssociation('starbucks coffee');
        final result2 = await service.getAssociation('STARBUCKS COFFEE');
        
        expect(result1?.categoryId, equals(categoryId));
        expect(result2?.categoryId, equals(categoryId));
      });

      test('should normalize text with special characters', () async {
        const text = 'Café! @#\$ Coffee & More...';
        const categoryId = 'food';

        await service.learn(text, categoryId: categoryId);
        
        final result = await service.getAssociation('cafe coffee more');
        expect(result?.categoryId, equals(categoryId));
      });

      test('should handle vendor name extraction', () async {
        const categoryId = 'food';
        
        // Learn with full transaction description
        await service.learn('Payment to McDonald\'s Restaurant #1234', categoryId: categoryId);
        
        // Should match with just vendor name
        final result = await service.getAssociation('mcdonalds');
        expect(result?.categoryId, equals(categoryId));
      });
    });

    group('Confidence and Updates', () {
      test('should increment confidence on repeated learning', () async {
        const text = 'starbucks coffee';
        const categoryId = 'food';

        // Learn multiple times
        await service.learn(text, categoryId: categoryId);
        await service.learn(text, categoryId: categoryId);
        await service.learn(text, categoryId: categoryId);

        final association = await service.getAssociation(text);
        expect(association!.confidence, equals(3));
      });

      test('should update lastUpdated timestamp', () async {
        const text = 'starbucks coffee';
        const categoryId = 'food';

        await service.learn(text, categoryId: categoryId);
        final firstAssociation = await service.getAssociation(text);
        
        // Wait a bit and learn again
        await Future.delayed(Duration(milliseconds: 10));
        await service.learn(text, categoryId: categoryId);
        
        final secondAssociation = await service.getAssociation(text);
        expect(secondAssociation!.lastUpdated.isAfter(firstAssociation!.lastUpdated), isTrue);
      });

      test('should update existing association with new data', () async {
        const text = 'grocery shopping';
        
        // First learn only category
        await service.learn(text, categoryId: 'food');
        
        // Then learn with type as well
        await service.learn(text, type: TransactionType.expense, categoryId: 'food');
        
        final association = await service.getAssociation(text);
        expect(association!.type, equals(TransactionType.expense));
        expect(association.categoryId, equals('food'));
        expect(association.confidence, equals(2));
      });
    });

    group('Partial Text Matching', () {
      test('should match partial text patterns', () async {
        const categoryId = 'transport';
        
        await service.learn('uber ride to downtown', categoryId: categoryId);
        
        final result = await service.getAssociation('uber ride');
        expect(result?.categoryId, equals(categoryId));
      });

      test('should match vendor names in different contexts', () async {
        const categoryId = 'food';
        
        await service.learn('dinner at olive garden restaurant', categoryId: categoryId);
        
        final result = await service.getAssociation('lunch olive garden');
        expect(result?.categoryId, equals(categoryId));
      });
    });

    group('Data Management', () {
      test('should retrieve all associations', () async {
        await service.learn('starbucks', categoryId: 'food');
        await service.learn('uber', categoryId: 'transport');
        await service.learn('salary', type: TransactionType.income);

        final allAssociations = await service.getAllAssociations();
        expect(allAssociations.length, equals(3));
      });

      test('should clear all data', () async {
        await service.learn('starbucks', categoryId: 'food');
        await service.learn('uber', categoryId: 'transport');

        await service.clearAllData();
        
        final allAssociations = await service.getAllAssociations();
        expect(allAssociations.length, equals(0));
        
        final association = await service.getAssociation('starbucks');
        expect(association, isNull);
      });
    });

    group('Error Handling', () {
      test('should handle empty text gracefully', () async {
        await service.learn('', categoryId: 'food');
        final association = await service.getAssociation('');
        expect(association, isNull);
      });

      test('should handle null parameters gracefully', () async {
        // Should not throw when both type and categoryId are null
        await service.learn('some text');
        final association = await service.getAssociation('some text');
        expect(association, isNull);
      });

      test('should handle corrupted storage data gracefully', () async {
        // Manually corrupt the storage
        await mockStorage.setString('learned_associations', 'invalid json');
        
        // Should not throw and should return null
        final association = await service.getAssociation('any text');
        expect(association, isNull);
        
        // Should be able to learn new data after corruption
        await service.learn('new text', categoryId: 'food');
        final newAssociation = await service.getAssociation('new text');
        expect(newAssociation?.categoryId, equals('food'));
      });
    });

    group('Data Migration', () {
      test('should migrate data from legacy LearnedCategoryStorage', () async {
        // Setup legacy data in storage
        await mockStorage.setString('learned_categories',
            '{"starbucks":"food","uber":"transport","walmart":"shopping"}');

        // Reset and create new instance to trigger migration
        LearnedAssociationService.resetInstance();
        final newService = await LearnedAssociationService.getInstance(mockStorage);

        // Check that legacy data was migrated
        final starbucksAssociation = await newService.getAssociation('starbucks');
        final uberAssociation = await newService.getAssociation('uber');
        final walmartAssociation = await newService.getAssociation('walmart');

        expect(starbucksAssociation?.categoryId, equals('food'));
        expect(uberAssociation?.categoryId, equals('transport'));
        expect(walmartAssociation?.categoryId, equals('shopping'));

        // All should have default type as null and confidence as 1
        expect(starbucksAssociation?.type, isNull);
        expect(starbucksAssociation?.confidence, equals(1));
      });

      test('should handle empty legacy data gracefully', () async {
        // Setup empty legacy data
        await mockStorage.setString('learned_categories', '{}');

        // Reset and create new instance
        LearnedAssociationService.resetInstance();
        final newService = await LearnedAssociationService.getInstance(mockStorage);

        // Should work normally
        await newService.learn('test', categoryId: 'food');
        final association = await newService.getAssociation('test');
        expect(association?.categoryId, equals('food'));
      });

      test('should handle corrupted legacy data gracefully', () async {
        // Setup corrupted legacy data
        await mockStorage.setString('learned_categories', 'invalid json');

        // Reset and create new instance
        LearnedAssociationService.resetInstance();
        final newService = await LearnedAssociationService.getInstance(mockStorage);

        // Should work normally despite corrupted legacy data
        await newService.learn('test', categoryId: 'food');
        final association = await newService.getAssociation('test');
        expect(association?.categoryId, equals('food'));
      });

      test('should not migrate data multiple times', () async {
        // Setup legacy data
        await mockStorage.setString('learned_categories', '{"starbucks":"food"}');

        // First migration
        LearnedAssociationService.resetInstance();
        final service1 = await LearnedAssociationService.getInstance(mockStorage);

        // Learn additional data
        await service1.learn('starbucks', categoryId: 'food'); // This should increment confidence

        // Second instance creation should not re-migrate
        LearnedAssociationService.resetInstance();
        final service2 = await LearnedAssociationService.getInstance(mockStorage);

        final association = await service2.getAssociation('starbucks');
        // Confidence should be 2 (1 from migration + 1 from manual learning)
        expect(association?.confidence, equals(2));
      });
    });

    group('Edge Cases', () {
      test('should handle very long text inputs', () async {
        final longText = 'a' * 1000; // 1000 character string
        const categoryId = 'food';

        await service.learn(longText, categoryId: categoryId);
        final association = await service.getAssociation(longText);
        expect(association?.categoryId, equals(categoryId));
      });

      test('should handle special unicode characters', () async {
        const text = '🍔 McDonald\'s 🍟 with émojis and àccénts';
        const categoryId = 'food';

        await service.learn(text, categoryId: categoryId);
        final association = await service.getAssociation(text);
        expect(association?.categoryId, equals(categoryId));
      });

      test('should handle numbers and mixed content', () async {
        const text = 'Transaction #12345 at Store-123 for \$45.67';
        const categoryId = 'shopping';

        await service.learn(text, categoryId: categoryId);
        final association = await service.getAssociation(text);
        expect(association?.categoryId, equals(categoryId));
      });
    });
  });
}
