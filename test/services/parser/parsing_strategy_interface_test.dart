import 'dart:ui';
import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/parsing_strategy.dart';
import '../../../lib/services/parser/parsing_context.dart';
import '../../../lib/models/parse_result.dart';
import '../../helpers/test_helpers.dart';

/// A concrete implementation of ParsingStrategy for testing purposes
class DummyStrategy extends ParsingStrategy {
  final String strategyName;
  final ParseResult? _returnResult;
  final bool _shouldReturnNull;

  // Track the contexts passed to execute for testing
  final List<ParsingContext> receivedContexts = [];

  DummyStrategy({
    this.strategyName = 'DummyStrategy',
    ParseResult? returnResult,
    bool shouldReturnNull = false,
  }) : _returnResult = returnResult,
       _shouldReturnNull = shouldReturnNull;

  @override
  String get name => strategyName;

  @override
  Future<ParseResult?> execute(ParsingContext context) async {
    // Store the context for testing verification
    receivedContexts.add(context);

    if (_shouldReturnNull) {
      return null;
    }

    return _returnResult ?? TestHelpers.createSuccessParseResult();
  }
}

void main() {
  group('ParsingStrategy Interface Tests', () {
    
    group('Interface Instantiation Safety', () {
      test('ParsingStrategy is abstract and cannot be instantiated directly', () {
        // This test verifies that ParsingStrategy is properly abstract
        // The abstract nature is enforced at compile time, so we test
        // that concrete implementations work correctly
        
        final strategy = DummyStrategy();
        expect(strategy, isA<ParsingStrategy>());
        expect(strategy.name, equals('DummyStrategy'));
      });
    });

    group('Dummy Implementation Behavior', () {
      test('execute method receives the same context reference', () async {
        final strategy = DummyStrategy();
        final context = ParsingContext(
          text: 'Test input text',
          locale: const Locale('en'),
        );

        await strategy.execute(context);

        expect(strategy.receivedContexts.length, equals(1));
        expect(identical(strategy.receivedContexts.first, context), isTrue);
      });

      test('returned ParseResult bubbles back correctly', () async {
        final testTransaction = TestHelpers.createTestTransaction(
          description: 'Test transaction for strategy',
          amount: 42.50,
        );
        final expectedResult = TestHelpers.createSuccessParseResult(
          transaction: testTransaction,
        );

        final strategy = DummyStrategy(returnResult: expectedResult);
        final context = ParsingContext(text: 'Test input');

        final result = await strategy.execute(context);

        expect(result, isNotNull);
        expect(identical(result, expectedResult), isTrue);
        expect(result!.transaction.description, equals('Test transaction for strategy'));
        expect(result.transaction.amount, equals(42.50));
      });

      test('name getter returns expected value', () {
        final strategy1 = DummyStrategy(strategyName: 'TestStrategy1');
        final strategy2 = DummyStrategy(strategyName: 'TestStrategy2');

        expect(strategy1.name, equals('TestStrategy1'));
        expect(strategy2.name, equals('TestStrategy2'));
      });

      test('strategy can return different types of ParseResult objects', () async {
        final testTransaction = TestHelpers.createTestTransaction();
        
        // Test success result
        final successStrategy = DummyStrategy(
          returnResult: TestHelpers.createSuccessParseResult(transaction: testTransaction),
        );
        final successResult = await successStrategy.execute(ParsingContext(text: 'test'));
        expect(successResult!.isSuccess, isTrue);

        // Test needs category result
        final needsCategoryStrategy = DummyStrategy(
          returnResult: TestHelpers.createNeedsCategoryParseResult(transaction: testTransaction),
        );
        final needsCategoryResult = await needsCategoryStrategy.execute(ParsingContext(text: 'test'));
        expect(needsCategoryResult!.needsCategorySelection, isTrue);

        // Test needs type result
        final needsTypeStrategy = DummyStrategy(
          returnResult: TestHelpers.createNeedsTypeParseResult(transaction: testTransaction),
        );
        final needsTypeResult = await needsTypeStrategy.execute(ParsingContext(text: 'test'));
        expect(needsTypeResult!.needsTypeSelection, isTrue);

        // Test failed result
        final failedStrategy = DummyStrategy(
          returnResult: TestHelpers.createFailedParseResult(
            transaction: testTransaction,
            error: 'Test error message',
          ),
        );
        final failedResult = await failedStrategy.execute(ParsingContext(text: 'test'));
        expect(failedResult!.hasError, isTrue);
        expect(failedResult.error, equals('Test error message'));
      });
    });

    group('ParsingContext Mutability', () {
      test('adding entries to extras map and reading them back', () {
        final context = ParsingContext(text: 'Test input');
        
        // Initially empty
        expect(context.extras.isEmpty, isTrue);

        // Add entries
        context.extras['testKey'] = 'testValue';
        context.extras['numericKey'] = 42;
        context.extras['boolKey'] = true;

        // Verify entries can be read back
        expect(context.extras['testKey'], equals('testValue'));
        expect(context.extras['numericKey'], equals(42));
        expect(context.extras['boolKey'], isTrue);
        expect(context.extras.length, equals(3));
      });

      test('setting interimResult field and confirming change is observable', () {
        final context = ParsingContext(text: 'Test input');
        final testResult = TestHelpers.createSuccessParseResult();

        // Initially null
        expect(context.interimResult, isNull);

        // Set the interim result
        context.interimResult = testResult;

        // Verify change is observable
        expect(context.interimResult, isNotNull);
        expect(identical(context.interimResult, testResult), isTrue);
      });

      test('modifying context and verifying changes persist', () async {
        final strategy = DummyStrategy();
        final context = ParsingContext(
          text: 'Test input',
          locale: const Locale('en'),
        );

        // Add data to context before passing to strategy
        context.extras['beforeStrategy'] = 'initialValue';
        final beforeResult = TestHelpers.createNeedsCategoryParseResult();
        context.interimResult = beforeResult;

        await strategy.execute(context);

        // Verify the context received by strategy has our modifications
        final receivedContext = strategy.receivedContexts.first;
        expect(receivedContext.extras['beforeStrategy'], equals('initialValue'));
        expect(identical(receivedContext.interimResult, beforeResult), isTrue);

        // Modify the context after strategy execution
        context.extras['afterStrategy'] = 'modifiedValue';
        final afterResult = TestHelpers.createSuccessParseResult();
        context.interimResult = afterResult;

        // Verify modifications persist
        expect(context.extras['beforeStrategy'], equals('initialValue'));
        expect(context.extras['afterStrategy'], equals('modifiedValue'));
        expect(identical(context.interimResult, afterResult), isTrue);
      });
    });

    group('Null Result Convention', () {
      test('strategy returning null is accepted without throwing exceptions', () async {
        final strategy = DummyStrategy(shouldReturnNull: true);
        final context = ParsingContext(text: 'Test input');

        // Should not throw an exception
        final result = await strategy.execute(context);

        expect(result, isNull);
        expect(strategy.receivedContexts.length, equals(1));
      });

      test('multiple strategies can return null in chain pattern', () async {
        final strategies = [
          DummyStrategy(strategyName: 'Strategy1', shouldReturnNull: true),
          DummyStrategy(strategyName: 'Strategy2', shouldReturnNull: true),
          DummyStrategy(strategyName: 'Strategy3', shouldReturnNull: true),
        ];

        final context = ParsingContext(text: 'Test input');

        for (final strategy in strategies) {
          final result = await strategy.execute(context);
          expect(result, isNull);
        }

        // Verify all strategies were called
        for (final strategy in strategies) {
          expect(strategy.receivedContexts.length, equals(1));
        }
      });
    });

    group('ParsingContext Constructor and Properties', () {
      test('creates context with required text parameter', () {
        const testText = 'Test transaction input';
        final context = ParsingContext(text: testText);

        expect(context.text, equals(testText));
        expect(context.locale, isNull);
        expect(context.extras, isNotNull);
        expect(context.extras.isEmpty, isTrue);
        expect(context.interimResult, isNull);
      });

      test('creates context with all parameters', () {
        const testText = 'Test transaction input';
        const testLocale = Locale('es', 'ES');
        final testExtras = {'key1': 'value1', 'key2': 42};

        final context = ParsingContext(
          text: testText,
          locale: testLocale,
          extras: testExtras,
        );

        expect(context.text, equals(testText));
        expect(context.locale, equals(testLocale));
        expect(context.extras, equals(testExtras));
        expect(context.interimResult, isNull);
      });

      test('extras map defaults to empty map when not provided', () {
        final context = ParsingContext(text: 'Test');

        expect(context.extras, isNotNull);
        expect(context.extras.isEmpty, isTrue);
        
        // Should be able to add to the map
        context.extras['newKey'] = 'newValue';
        expect(context.extras['newKey'], equals('newValue'));
      });
    });
  });
}
