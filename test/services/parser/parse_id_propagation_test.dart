import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import '../../../lib/services/parser/transaction_parsing_service.dart';
import '../../../lib/services/parser/parse_logger.dart';
import '../../../lib/models/parse_result.dart';
import '../../mocks/mock_storage_service.dart';
import '../../mocks/mock_entity_extractor.dart';

/// Memory-based log output for testing
class MemoryLogOutput extends LogOutput {
  final List<OutputEvent> events = [];

  @override
  void output(OutputEvent event) {
    events.add(event);
  }

  void clear() {
    events.clear();
  }

  List<String> get messages => events.map((e) => e.lines.join('\n')).toList();
  
  /// Get all messages containing a specific correlation ID
  List<String> getMessagesForId(String parseId) {
    return messages.where((msg) => msg.contains('[parse:$parseId]')).toList();
  }
  
  /// Extract correlation ID from a START message
  String? extractIdFromStartMessage() {
    for (final message in messages) {
      if (message.contains('START:')) {
        final match = RegExp(r'\[parse:([a-f0-9-]{8})\]').firstMatch(message);
        return match?.group(1);
      }
    }
    return null;
  }
}

void main() {
  group('Parse ID Propagation Integration Tests', () {
    late MockStorageService mockStorage;
    late MockEntityExtractor mockExtractor;
    late MemoryLogOutput memoryOutput;

    setUp(() async {
      // Set up mocks
      mockStorage = MockStorageService();
      mockExtractor = MockEntityExtractor();

      // Set up memory log output
      memoryOutput = MemoryLogOutput();
      ParseLogger.setLogOutput(memoryOutput);

      // Configure mock storage defaults
      mockStorage.setString('default_currency', 'USD');
      mockStorage.setString('categories', '[]');
    });

    tearDown(() {
      memoryOutput.clear();
    });

    group('Parse ID Generation and Propagation', () {
      test('generates correlation ID and propagates through parsing pipeline', () async {
        // Configure mock extractor to return empty entities (will use fallback)
        mockExtractor.setMockResults([]);
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );
        
        // Parse sample text
        final result = await service.parseTransaction('buy coffee 5.50');
        
        // Verify parsing completed (any valid status is acceptable)
        expect(result.status, isIn([ParseStatus.success, ParseStatus.needsCategory, ParseStatus.needsAmountConfirmation, ParseStatus.needsType]));
        
        // Extract correlation ID from START message
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);
        expect(parseId!.length, equals(8));
        
        // Verify START message exists
        final startMessages = memoryOutput.messages.where((msg) => 
          msg.contains('[parse:$parseId] START:')).toList();
        expect(startMessages, hasLength(1));
        expect(startMessages.first, contains('buy coffee 5.50'));
        
        // Verify strategy messages use same correlation ID
        final strategyMessages = memoryOutput.getMessagesForId(parseId);
        expect(strategyMessages.length, greaterThan(1));
        
        // Should have messages about trying strategies
        final tryingMessages = strategyMessages.where((msg) => 
          msg.contains('Trying strategy:')).toList();
        expect(tryingMessages, isNotEmpty);
        
        // Should have messages about strategy results
        final resultMessages = strategyMessages.where((msg) => 
          msg.contains('handled the input') || msg.contains('declined to handle')).toList();
        expect(resultMessages, isNotEmpty);
      });

      test('correlation ID is consistent across multiple log messages', () async {
        mockExtractor.setMockResults([]);
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );
        
        await service.parseTransaction('test transaction 10.00');
        
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);
        
        // Get all messages for this parse ID
        final correlatedMessages = memoryOutput.getMessagesForId(parseId!);
        
        // Should have multiple messages all with same correlation ID
        expect(correlatedMessages.length, greaterThan(3));
        
        // Verify all messages contain the same parse ID
        for (final message in correlatedMessages) {
          expect(message, contains('[parse:$parseId]'));
        }
      });
    });

    group('Strategy Chain Correlation', () {
      test('all strategies receive same parseId through ParsingContext', () async {
        // Configure mock to trigger multiple strategies
        mockExtractor.setMockResults([]);
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );
        
        await service.parseTransaction('complex transaction 25.99');
        
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);
        
        final correlatedMessages = memoryOutput.getMessagesForId(parseId!);
        
        // Should see messages from different strategies
        final strategyNames = ['LearnedAssociationStrategy', 'MlKitStrategy', 'FallbackRegexStrategy'];
        
        for (final strategyName in strategyNames) {
          final strategyMessages = correlatedMessages.where((msg) => 
            msg.contains(strategyName)).toList();
          
          if (strategyMessages.isNotEmpty) {
            // If strategy was used, all its messages should have same correlation ID
            for (final message in strategyMessages) {
              expect(message, contains('[parse:$parseId]'));
            }
          }
        }
      });

      test('strategy execution order is logged with correlation', () async {
        mockExtractor.setMockResults([]);
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );
        
        await service.parseTransaction('ordered execution test 15.75');
        
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);
        
        final correlatedMessages = memoryOutput.getMessagesForId(parseId!);
        
        // Should see "Trying strategy:" messages in order
        final tryingMessages = correlatedMessages.where((msg) => 
          msg.contains('Trying strategy:')).toList();
        
        expect(tryingMessages, isNotEmpty);
        
        // Each trying message should be followed by a result message
        final resultMessages = correlatedMessages.where((msg) => 
          msg.contains('handled the input') || msg.contains('declined to handle')).toList();
        
        expect(resultMessages, isNotEmpty);
      });
    });

    group('Context Flow Verification', () {
      test('parseId flows correctly through ParsingContext', () async {
        mockExtractor.setMockResults([]);
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );
        
        await service.parseTransaction('context flow test 8.25');
        
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);
        
        // Verify that strategies can access and use the parseId
        final correlatedMessages = memoryOutput.getMessagesForId(parseId!);
        
        // Should have messages from strategy execution
        expect(correlatedMessages.length, greaterThan(2));
        
        // All messages should be properly correlated
        for (final message in correlatedMessages) {
          expect(message, contains('[parse:$parseId]'));
        }
      });

      test('parseId is accessible in strategy error handling', () async {
        // Configure mock to potentially trigger errors
        mockExtractor.setMockResults([]);
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );
        
        // Use input that might trigger edge cases
        await service.parseTransaction('error test scenario');
        
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);
        
        // Even if errors occur, they should be correlated
        final correlatedMessages = memoryOutput.getMessagesForId(parseId!);
        expect(correlatedMessages, isNotEmpty);
        
        // All messages should maintain correlation
        for (final message in correlatedMessages) {
          expect(message, contains('[parse:$parseId]'));
        }
      });
    });

    group('Backward Compatibility', () {
      test('parsing works when parseId is null or not provided', () async {
        // This test ensures existing code continues to work
        mockExtractor.setMockResults([]);
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );
        
        // Normal parsing should still work
        final result = await service.parseTransaction('backward compatibility test 12.34');
        
        // Should complete successfully (any valid status is acceptable)
        expect(result.status, isIn([ParseStatus.success, ParseStatus.needsCategory, ParseStatus.needsAmountConfirmation, ParseStatus.needsType]));
        
        // Should have generated a correlation ID
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);
      });

      test('strategies handle missing parseId gracefully', () async {
        mockExtractor.setMockResults([]);
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );
        
        await service.parseTransaction('graceful handling test 7.89');
        
        // Should not throw exceptions and should complete
        final parseId = memoryOutput.extractIdFromStartMessage();
        expect(parseId, isNotNull);
        
        // Should have correlated messages
        final correlatedMessages = memoryOutput.getMessagesForId(parseId!);
        expect(correlatedMessages, isNotEmpty);
      });
    });

    group('Multiple Parse Operations', () {
      test('different parse operations get different correlation IDs', () async {
        mockExtractor.setMockResults([]);
        
        final service = await TransactionParsingService.getInstance(
          mockStorage,
          entityExtractor: mockExtractor,
        );
        
        // Perform multiple parse operations
        await service.parseTransaction('first transaction 10.00');
        await service.parseTransaction('second transaction 20.00');
        await service.parseTransaction('third transaction 30.00');
        
        // Extract all START messages
        final startMessages = memoryOutput.messages.where((msg) => 
          msg.contains('START:')).toList();
        
        expect(startMessages, hasLength(3));
        
        // Extract correlation IDs
        final parseIds = <String>[];
        for (final message in startMessages) {
          final match = RegExp(r'\[parse:([a-f0-9-]{8})\]').firstMatch(message);
          if (match != null) {
            parseIds.add(match.group(1)!);
          }
        }
        
        expect(parseIds, hasLength(3));
        
        // All IDs should be unique
        expect(parseIds.toSet(), hasLength(3));
        
        // Each ID should have its own set of correlated messages
        for (final parseId in parseIds) {
          final correlatedMessages = memoryOutput.getMessagesForId(parseId);
          expect(correlatedMessages, isNotEmpty);
        }
      });
    });
  });
}
