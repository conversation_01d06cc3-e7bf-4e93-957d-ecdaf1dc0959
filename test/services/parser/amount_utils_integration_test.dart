import 'package:flutter_test/flutter_test.dart';
import '../../../lib/utils/amount_utils.dart';

void main() {
  group('AmountUtils Direct Tests', () {
    test('should parse basic amounts correctly', () {
      final result = AmountUtils.extractAmountFromText('\$100.50 food');

      expect(result, isNotNull);
      expect(result!['amount'], equals(100.50));
      expect(result['currency'], equals('USD'));
    });

    test('should parse abbreviated amounts correctly', () {
      final testCases = [
        {'text': '100k food', 'expectedAmount': 100000.0},
        {'text': '2.5M salary', 'expectedAmount': 2500000.0},
        {'text': '1.2B investment', 'expectedAmount': 1200000000.0},
      ];

      for (final testCase in testCases) {
        final result = AmountUtils.extractAmountFromText(testCase['text'] as String);

        expect(result, isNotNull, reason: 'Should find amount in: ${testCase['text']}');
        expect(result!['amount'], equals(testCase['expectedAmount']),
            reason: 'Amount parsing failed for: ${testCase['text']}');
      }
    });

    test('should handle explicit currency with abbreviations', () {
      final result = AmountUtils.extractAmountFromText('\$100k shopping');

      expect(result, isNotNull);
      expect(result!['amount'], equals(100000.0));
      expect(result['currency'], equals('USD'));
    });

    test('should handle case variations in abbreviations', () {
      final testCases = [
        {'text': '100K food', 'expectedAmount': 100000.0},
        {'text': '2m salary', 'expectedAmount': 2000000.0},
        {'text': '1.5b investment', 'expectedAmount': 1500000000.0},
      ];

      for (final testCase in testCases) {
        final result = AmountUtils.extractAmountFromText(testCase['text'] as String);

        expect(result, isNotNull, reason: 'Should find amount in: ${testCase['text']}');
        expect(result!['amount'], equals(testCase['expectedAmount']),
            reason: 'Case-insensitive parsing failed for: ${testCase['text']}');
      }
    });

    test('should handle thousands separators with abbreviations', () {
      final testCases = [
        {'text': '1,500k shopping', 'expectedAmount': 1500000.0},
        {'text': '2,500.50M investment', 'expectedAmount': 2500500000.0},
      ];

      for (final testCase in testCases) {
        final result = AmountUtils.extractAmountFromText(testCase['text'] as String);

        expect(result, isNotNull, reason: 'Should find amount in: ${testCase['text']}');
        expect(result!['amount'], equals(testCase['expectedAmount']),
            reason: 'Thousands separator parsing failed for: ${testCase['text']}');
      }
    });

    test('should maintain backward compatibility with non-abbreviated amounts', () {
      final testCases = [
        {'text': '\$100.50 food', 'expectedAmount': 100.50},
        {'text': '1,500 euros shopping', 'expectedAmount': 1500.0},
        {'text': '250.75 transport', 'expectedAmount': 250.75},
      ];

      for (final testCase in testCases) {
        final result = AmountUtils.extractAmountFromText(testCase['text'] as String);

        expect(result, isNotNull, reason: 'Should find amount in: ${testCase['text']}');
        expect(result!['amount'], equals(testCase['expectedAmount']),
            reason: 'Backward compatibility failed for: ${testCase['text']}');
      }
    });
  });
}
