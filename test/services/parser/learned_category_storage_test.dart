import 'package:flutter_test/flutter_test.dart';
import '../../../lib/services/parser/learned_category_storage.dart';
import '../../mocks/mock_storage_service.dart';

void main() {
  group('LearnedCategoryStorage Tests', () {
    late LearnedCategoryStorage learnedStorage;
    late MockStorageService mockStorage;

    setUp(() {
      mockStorage = MockStorageService();
      learnedStorage = LearnedCategoryStorage(mockStorage);
    });

    group('Basic Functionality', () {
      test('should return null when no data stored', () async {
        final result = await learnedStorage.getLearnedCategory('starbucks coffee');
        expect(result, isNull);
      });

      test('should save and retrieve learned category', () async {
        const text = 'starbucks coffee';
        const categoryId = 'food';

        await learnedStorage.saveLearnedCategory(text, categoryId);
        final result = await learnedStorage.getLearnedCategory(text);

        expect(result, equals(categoryId));
      });

      test('should handle case insensitive lookup', () async {
        const text = 'Starbucks Coffee';
        const categoryId = 'food';

        await learnedStorage.saveLearnedCategory(text, categoryId);
        
        // Should find with different case
        final result1 = await learnedStorage.getLearnedCategory('starbucks coffee');
        final result2 = await learnedStorage.getLearnedCategory('STARBUCKS COFFEE');
        
        expect(result1, equals(categoryId));
        expect(result2, equals(categoryId));
      });

      test('should normalize text with special characters', () async {
        const text = 'Café! @#\$ Coffee & More...';
        const categoryId = 'food';

        await learnedStorage.saveLearnedCategory(text, categoryId);
        
        // Should find with normalized version
        final result = await learnedStorage.getLearnedCategory('cafe coffee more');
        expect(result, equals(categoryId));
      });
    });

    group('Vendor Name Extraction', () {
      test('should extract vendor names from transaction descriptions', () async {
        const descriptions = [
          'spent at starbucks',
          'paid starbucks for coffee',
          'bought coffee from starbucks',
          'purchase at starbucks coffee',
        ];
        const categoryId = 'food';

        for (final desc in descriptions) {
          await learnedStorage.saveLearnedCategory(desc, categoryId);
        }

        // Should match by extracted vendor name
        final result1 = await learnedStorage.getLearnedCategory('coffee at starbucks');
        final result2 = await learnedStorage.getLearnedCategory('starbucks breakfast');
        
        expect(result1, equals(categoryId));
        expect(result2, equals(categoryId));
      });

      test('should handle business names with multiple words', () async {
        const text = 'dinner at olive garden restaurant';
        const categoryId = 'food';

        await learnedStorage.saveLearnedCategory(text, categoryId);
        
        final result = await learnedStorage.getLearnedCategory('lunch olive garden');
        expect(result, equals(categoryId));
      });
    });

    group('Partial Matching', () {
      test('should find partial matches', () async {
        const text = 'target store';
        const categoryId = 'shopping';

        await learnedStorage.saveLearnedCategory(text, categoryId);
        
        // Should match if either contains the other
        final result1 = await learnedStorage.getLearnedCategory('target');
        final result2 = await learnedStorage.getLearnedCategory('shopping at target store downtown');
        
        expect(result1, equals(categoryId));
        expect(result2, equals(categoryId));
      });

      test('should prioritize exact matches over partial', () async {
        const exactText = 'coffee';
        const partialText = 'starbucks coffee shop';
        const exactCategoryId = 'beverage';
        const partialCategoryId = 'food';

        await learnedStorage.saveLearnedCategory(exactText, exactCategoryId);
        await learnedStorage.saveLearnedCategory(partialText, partialCategoryId);
        
        // Should return exact match
        final result = await learnedStorage.getLearnedCategory('coffee');
        expect(result, equals(exactCategoryId));
      });
    });

    group('Multiple Categories', () {
      test('should handle multiple learned categories', () async {
        const categories = {
          'starbucks': 'food',
          'shell gas station': 'transport',
          'target': 'shopping',
          'movie theater': 'entertainment',
        };

        for (final entry in categories.entries) {
          await learnedStorage.saveLearnedCategory(entry.key, entry.value);
        }

        // Verify all categories can be retrieved
        for (final entry in categories.entries) {
          final result = await learnedStorage.getLearnedCategory(entry.key);
          expect(result, equals(entry.value));
        }
      });

      test('should update existing category associations', () async {
        const text = 'amazon';
        const oldCategoryId = 'shopping';
        const newCategoryId = 'online';

        await learnedStorage.saveLearnedCategory(text, oldCategoryId);
        final result1 = await learnedStorage.getLearnedCategory(text);
        expect(result1, equals(oldCategoryId));

        // Update with new category
        await learnedStorage.saveLearnedCategory(text, newCategoryId);
        final result2 = await learnedStorage.getLearnedCategory(text);
        expect(result2, equals(newCategoryId));
      });
    });

    group('Data Management', () {
      test('should get all learned categories', () async {
        const categories = {
          'starbucks': 'food',
          'shell': 'transport',
          'target': 'shopping',
        };

        for (final entry in categories.entries) {
          await learnedStorage.saveLearnedCategory(entry.key, entry.value);
        }

        final allCategories = await learnedStorage.getAllLearnedCategories();
        expect(allCategories, isA<Map<String, String>>());
        expect(allCategories.length, greaterThanOrEqualTo(categories.length));
      });

      test('should clear all learned data', () async {
        await learnedStorage.saveLearnedCategory('test', 'category');
        
        final beforeClear = await learnedStorage.getLearnedCategory('test');
        expect(beforeClear, equals('category'));

        await learnedStorage.clearLearnedData();
        
        final afterClear = await learnedStorage.getLearnedCategory('test');
        expect(afterClear, isNull);
      });

      test('should export learned data as JSON', () async {
        const categories = {
          'starbucks': 'food',
          'shell': 'transport',
        };

        for (final entry in categories.entries) {
          await learnedStorage.saveLearnedCategory(entry.key, entry.value);
        }

        final exportedData = await learnedStorage.exportLearnedData();
        expect(exportedData, isNotNull);
        expect(exportedData, isA<String>());
        expect(exportedData, contains('starbucks'));
        expect(exportedData, contains('food'));
      });

      test('should handle empty data gracefully', () async {
        final allCategories = await learnedStorage.getAllLearnedCategories();
        expect(allCategories, isEmpty);

        final exportedData = await learnedStorage.exportLearnedData();
        expect(exportedData, isNull);
      });
    });

    group('Error Handling', () {
      test('should handle corrupted storage data gracefully', () async {
        // Simulate corrupted JSON data
        await mockStorage.setString('learned_categories', 'invalid json {[}');
        
        final result = await learnedStorage.getLearnedCategory('test');
        expect(result, isNull);

        final allCategories = await learnedStorage.getAllLearnedCategories();
        expect(allCategories, isEmpty);
      });

      test('should handle storage errors gracefully', () async {
        // This test depends on the mock being able to simulate errors
        // For now, we test that the method doesn't throw
        
        expect(() async {
          await learnedStorage.getLearnedCategory('test');
        }, returnsNormally);

        expect(() async {
          await learnedStorage.getAllLearnedCategories();
        }, returnsNormally);
      });

      test('should handle empty and null inputs', () async {
        // Empty text
        await learnedStorage.saveLearnedCategory('', 'category');
        final result1 = await learnedStorage.getLearnedCategory('');
        expect(result1, equals('category'));

        // Whitespace only
        await learnedStorage.saveLearnedCategory('   ', 'whitespace');
        final result2 = await learnedStorage.getLearnedCategory('   ');
        expect(result2, equals('whitespace'));
      });
    });

    group('Text Normalization', () {
      test('should normalize special characters consistently', () async {
        const variations = [
          'café & restaurant!',
          'cafe and restaurant',
          'Café & Restaurant!',
          'CAFE & RESTAURANT',
        ];
        const categoryId = 'food';

        await learnedStorage.saveLearnedCategory(variations[0], categoryId);

        // All variations should match
        for (final variation in variations) {
          final result = await learnedStorage.getLearnedCategory(variation);
          expect(result, equals(categoryId), reason: 'Failed for: $variation');
        }
      });

      test('should handle unicode characters', () async {
        const text = 'sushi 🍣 restaurant';
        const categoryId = 'food';

        await learnedStorage.saveLearnedCategory(text, categoryId);
        
        final result1 = await learnedStorage.getLearnedCategory('sushi restaurant');
        final result2 = await learnedStorage.getLearnedCategory('sushi 🍣 restaurant');
        
        expect(result1, equals(categoryId));
        expect(result2, equals(categoryId));
      });

      test('should handle excessive whitespace', () async {
        const text = '   starbucks    coffee   shop   ';
        const categoryId = 'food';

        await learnedStorage.saveLearnedCategory(text, categoryId);
        
        final result = await learnedStorage.getLearnedCategory('starbucks coffee shop');
        expect(result, equals(categoryId));
      });
    });

    group('Vendor Name Extraction Logic', () {
      test('should extract meaningful vendor names', () async {
        const testCases = {
          'spent at walmart': 'walmart',
          'paid starbucks for coffee': 'starbucks',
          'bought groceries from trader joes': 'trader joes',
          'purchase at best buy': 'best buy',
        };

        for (final entry in testCases.entries) {
          await learnedStorage.saveLearnedCategory(entry.key, 'test-category');
        }

        // Should be able to find by extracted vendor names
        for (final vendorName in testCases.values) {
          final result = await learnedStorage.getLearnedCategory(vendorName);
          expect(result, equals('test-category'), reason: 'Failed for vendor: $vendorName');
        }
      });

      test('should not extract overly long vendor names', () async {
        const text = 'bought this really long description that should not be extracted as a vendor name';
        const categoryId = 'shopping';

        await learnedStorage.saveLearnedCategory(text, categoryId);
        
        // Should still find by full text match
        final result = await learnedStorage.getLearnedCategory(text);
        expect(result, equals(categoryId));
      });

      test('should handle single word vendor names', () async {
        const text = 'walmart';
        const categoryId = 'shopping';

        await learnedStorage.saveLearnedCategory(text, categoryId);
        
        final result = await learnedStorage.getLearnedCategory('walmart');
        expect(result, equals(categoryId));
      });
    });

    group('Performance and Storage Limits', () {
      test('should handle large number of learned categories', () async {
        const numCategories = 100;
        
        // Add many categories
        for (int i = 0; i < numCategories; i++) {
          await learnedStorage.saveLearnedCategory('vendor$i', 'category$i');
        }

        // Verify they can all be retrieved
        for (int i = 0; i < numCategories; i++) {
          final result = await learnedStorage.getLearnedCategory('vendor$i');
          expect(result, equals('category$i'));
        }

        final allCategories = await learnedStorage.getAllLearnedCategories();
        expect(allCategories.length, equals(numCategories));
      });

      test('should perform lookups efficiently', () async {
        // Add some test data
        for (int i = 0; i < 50; i++) {
          await learnedStorage.saveLearnedCategory('vendor$i', 'category$i');
        }

        final stopwatch = Stopwatch()..start();
        
        // Perform multiple lookups
        for (int i = 0; i < 100; i++) {
          await learnedStorage.getLearnedCategory('vendor${i % 50}');
        }
        
        stopwatch.stop();
        
        // Should complete quickly (this is a rough performance test)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });
    });
  });
}
