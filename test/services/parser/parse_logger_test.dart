import 'package:flutter_test/flutter_test.dart';
import 'package:logger/logger.dart';
import '../../../lib/services/parser/parse_logger.dart';

/// Memory-based log output for testing
class MemoryLogOutput extends LogOutput {
  final List<OutputEvent> events = [];

  @override
  void output(OutputEvent event) {
    events.add(event);
  }

  void clear() {
    events.clear();
  }

  List<String> get messages => events.map((e) => e.lines.join('\n')).toList();
}

void main() {
  group('ParseLogger', () {
    late MemoryLogOutput memoryOutput;

    setUp(() {
      memoryOutput = MemoryLogOutput();
      ParseLogger.setLogOutput(memoryOutput);
    });

    tearDown(() {
      memoryOutput.clear();
    });

    group('ID Generation Tests', () {
      test('start() returns non-null 8-character correlation ID', () {
        final id = ParseLogger.start('test input');
        
        expect(id, isNotNull);
        expect(id.length, equals(8));
        expect(id, matches(RegExp(r'^[a-f0-9]{8}$')));
      });

      test('consecutive calls produce unique IDs', () {
        final id1 = ParseLogger.start('test input 1');
        final id2 = ParseLogger.start('test input 2');
        final id3 = ParseLogger.start('test input 3');
        
        expect(id1, isNot(equals(id2)));
        expect(id2, isNot(equals(id3)));
        expect(id1, isNot(equals(id3)));
      });

      test('start() logs START message with correct format', () {
        final id = ParseLogger.start('buy coffee 5\$');

        expect(memoryOutput.messages, hasLength(1));
        expect(memoryOutput.messages.first, contains('[parse:$id] START: "buy coffee 5\$"'));
      });
    });

    group('Logging Method Tests', () {
      const testId = 'test1234';
      const testMessage = 'Test message';

      test('d() logs debug message with correct format', () {
        ParseLogger.d(testId, testMessage);
        
        expect(memoryOutput.messages, hasLength(1));
        expect(memoryOutput.messages.first, contains('[parse:$testId] $testMessage'));
      });

      test('i() logs info message with correct format', () {
        ParseLogger.i(testId, testMessage);
        
        expect(memoryOutput.messages, hasLength(1));
        expect(memoryOutput.messages.first, contains('[parse:$testId] $testMessage'));
      });

      test('w() logs warning message with correct format', () {
        ParseLogger.w(testId, testMessage);
        
        expect(memoryOutput.messages, hasLength(1));
        expect(memoryOutput.messages.first, contains('[parse:$testId] $testMessage'));
      });

      test('e() logs error message with correct format', () {
        ParseLogger.e(testId, testMessage);
        
        expect(memoryOutput.messages, hasLength(1));
        expect(memoryOutput.messages.first, contains('[parse:$testId] $testMessage'));
      });

      test('all methods prefix messages with [parse:<id>] format', () {
        const id = 'abc12345';
        const message = 'Test correlation';
        
        ParseLogger.d(id, message);
        ParseLogger.i(id, message);
        ParseLogger.w(id, message);
        ParseLogger.e(id, message);
        
        expect(memoryOutput.messages, hasLength(4));
        for (final logMessage in memoryOutput.messages) {
          expect(logMessage, contains('[parse:$id] $message'));
        }
      });
    });

    group('Error Handling Tests', () {
      const testId = 'err12345';
      const testMessage = 'Error occurred';

      test('w() handles optional error object', () {
        final error = Exception('Test exception');
        ParseLogger.w(testId, testMessage, error);
        
        expect(memoryOutput.messages, hasLength(1));
        expect(memoryOutput.messages.first, contains('[parse:$testId] $testMessage'));
      });

      test('e() handles optional error object and stack trace', () {
        final error = Exception('Test exception');
        final stackTrace = StackTrace.current;
        ParseLogger.e(testId, testMessage, error, stackTrace);
        
        expect(memoryOutput.messages, hasLength(1));
        expect(memoryOutput.messages.first, contains('[parse:$testId] $testMessage'));
      });

      test('w() works without error object', () {
        ParseLogger.w(testId, testMessage);
        
        expect(memoryOutput.messages, hasLength(1));
        expect(memoryOutput.messages.first, contains('[parse:$testId] $testMessage'));
      });

      test('e() works without error object or stack trace', () {
        ParseLogger.e(testId, testMessage);
        
        expect(memoryOutput.messages, hasLength(1));
        expect(memoryOutput.messages.first, contains('[parse:$testId] $testMessage'));
      });
    });

    group('Message Formatting Tests', () {
      test('correlation IDs are properly formatted in log messages', () {
        const ids = ['a1b2c3d4', '12345678', 'xyz98765'];
        const message = 'Formatting test';
        
        for (final id in ids) {
          ParseLogger.i(id, message);
        }
        
        expect(memoryOutput.messages, hasLength(3));
        for (int i = 0; i < ids.length; i++) {
          expect(memoryOutput.messages[i], contains('[parse:${ids[i]}] $message'));
        }
      });

      test('message content is preserved correctly', () {
        const id = 'preserve1';
        const messages = [
          'Simple message',
          'Message with "quotes"',
          'Message with special chars: !@#\\\$%^&*()',
          'Multi-word message with spaces',
          'Message with numbers: 123.45',
        ];
        
        for (final message in messages) {
          ParseLogger.i(id, message);
        }
        
        expect(memoryOutput.messages, hasLength(5));
        for (int i = 0; i < messages.length; i++) {
          expect(memoryOutput.messages[i], contains('[parse:$id] ${messages[i]}'));
        }
      });

      test('empty and whitespace messages are handled', () {
        const id = 'empty123';

        ParseLogger.i(id, '');
        ParseLogger.i(id, '   ');
        ParseLogger.i(id, 'tab_newline');

        expect(memoryOutput.messages, hasLength(3));
        expect(memoryOutput.messages[0], contains('[parse:$id] '));
        expect(memoryOutput.messages[1], contains('[parse:$id]    '));
        expect(memoryOutput.messages[2], contains('[parse:$id] tab_newline'));
      });
    });

    group('LogOutput Injection Tests', () {
      test('setLogOutput allows injection of custom LogOutput', () {
        final customOutput = MemoryLogOutput();
        ParseLogger.setLogOutput(customOutput);
        
        ParseLogger.i('test123', 'Custom output test');
        
        // Should appear in custom output
        expect(customOutput.messages, hasLength(1));
        expect(customOutput.messages.first, contains('[parse:test123] Custom output test'));
      });

      test('setLogOutput replaces current output', () {
        final output1 = MemoryLogOutput();
        final output2 = MemoryLogOutput();

        ParseLogger.setLogOutput(output1);
        ParseLogger.i('first123', 'First output test');

        ParseLogger.setLogOutput(output2);
        ParseLogger.i('second123', 'Second output test');

        // First output should only have first message
        expect(output1.messages, hasLength(1));
        expect(output1.messages.first, contains('[parse:first123] First output test'));

        // Second output should only have second message
        expect(output2.messages, hasLength(1));
        expect(output2.messages.first, contains('[parse:second123] Second output test'));
      });
    });
  });
}
