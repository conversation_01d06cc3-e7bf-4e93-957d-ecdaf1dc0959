import 'package:flutter_test/flutter_test.dart';
import 'package:dreamflow/utils/startup_timer.dart';
import 'mocks/mock_storage_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('Startup Performance Tests', () {
    late StartupTimer timer;
    
    setUp(() {
      timer = StartupTimer.instance;
      StartupTimer.reset();
    });

    test('StartupTimer measures performance correctly', () async {
      // Test the StartupTimer utility itself
      timer.mark('phase-1');
      
      // Simulate some work
      await Future.delayed(const Duration(milliseconds: 10));
      
      timer.mark('phase-2');
      
      // Simulate more work
      await Future.delayed(const Duration(milliseconds: 5));
      
      timer.mark('phase-3');
      
      // Verify measurements
      final duration1to2 = timer.measure('phase-1', 'phase-2');
      final duration2to3 = timer.measure('phase-2', 'phase-3');
      final totalDuration = timer.durationSince('phase-1');
      
      expect(duration1to2, isNotNull);
      expect(duration2to3, isNotNull);
      expect(totalDuration, isNotNull);
      expect(duration1to2!.inMicroseconds, greaterThan(0));
      expect(duration2to3!.inMicroseconds, greaterThan(0));
      expect(totalDuration!.inMicroseconds, greaterThan(0));
      expect(totalDuration.inMicroseconds, greaterThan(duration1to2.inMicroseconds));
      expect(totalDuration.inMicroseconds, greaterThan(duration2to3.inMicroseconds));
    });

    test('Storage service initialization is fast', () async {
      timer.mark('storage-init-start');
      
      final storageService = MockStorageService();
      await storageService.init();
      
      timer.mark('storage-init-complete');
      final duration = timer.measure('storage-init-start', 'storage-init-complete');
      
      // Storage initialization should be very fast (under 100ms)
      expect(duration, isNotNull);
      expect(duration!.inMilliseconds, lessThan(100),
          reason: 'Storage service initialization should be fast');
      
      print('Storage initialization took: ${duration.inMilliseconds}ms');
    });

    test('Background initialization dispatch is non-blocking', () async {
      timer.mark('background-init-start');
      
      // Simulate dispatching background initialization
      final initFuture = Future.delayed(const Duration(milliseconds: 100), () {
        return 'Background task completed';
      });
      
      timer.mark('background-init-dispatched');
      
      // Verify that dispatching the background initialization is fast
      final dispatchDuration = timer.measure('background-init-start', 'background-init-dispatched');
      expect(dispatchDuration, isNotNull);
      expect(dispatchDuration!.inMilliseconds, lessThan(50), 
          reason: 'Background initialization dispatch should be very fast');
      
      // Wait for actual initialization to complete
      final result = await initFuture;
      timer.mark('background-init-complete');
      
      expect(result, isNotNull);
      
      // Print timing information for analysis
      final totalDuration = timer.measure('background-init-start', 'background-init-complete');
      print('Background initialization took: ${totalDuration?.inMilliseconds ?? 0}ms');
    });

    test('Performance regression detection baseline', () async {
      // This test serves as a baseline for detecting performance regressions
      final storageService = MockStorageService();
      
      timer.mark('full-startup-simulation-start');
      
      // Simulate storage initialization
      timer.mark('storage-init-start');
      await storageService.init();
      timer.mark('storage-ready');
      
      // Simulate UI ready
      timer.mark('ui-ready');
      
      // Simulate background service initialization
      timer.mark('background-init-start');
      await Future.delayed(const Duration(milliseconds: 50)); // Simulate background work
      timer.mark('background-init-complete');
      
      // Calculate performance metrics
      final storageTime = timer.measure('storage-init-start', 'storage-ready');
      final uiTime = timer.measure('storage-ready', 'ui-ready');
      final backgroundTime = timer.measure('background-init-start', 'background-init-complete');
      final totalTime = timer.measure('full-startup-simulation-start', 'background-init-complete');
      
      // Print performance metrics
      print('=== Startup Performance Metrics ===');
      print('Storage initialization: ${storageTime?.inMilliseconds ?? 0}ms');
      print('UI ready time: ${uiTime?.inMilliseconds ?? 0}ms');
      print('Background init: ${backgroundTime?.inMilliseconds ?? 0}ms');
      print('Total startup time: ${totalTime?.inMilliseconds ?? 0}ms');
      
      // Performance expectations (these may need adjustment based on actual performance)
      expect(storageTime, isNotNull);
      expect(uiTime, isNotNull);
      expect(totalTime, isNotNull);
      expect(storageTime!.inMilliseconds, lessThan(100), 
          reason: 'Storage init should be under 100ms');
      expect(uiTime!.inMilliseconds, lessThan(10), 
          reason: 'UI should be ready very quickly');
      // Background can take longer but should complete eventually
      expect(totalTime!.inMilliseconds, lessThan(1000), 
          reason: 'Total startup should complete within 1 second for test');
    });
  });
}
