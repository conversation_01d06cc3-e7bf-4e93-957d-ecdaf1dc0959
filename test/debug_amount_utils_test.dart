import 'package:flutter_test/flutter_test.dart';
import '../lib/utils/amount_utils.dart';

void main() {
  group('Debug AmountUtils', () {
    test('should extract amount from simple text', () {
      final result = AmountUtils.extractAmountFromText('Spent \$25.50 on coffee');
      print('Result: $result');
      
      expect(result, isNotNull);
      expect(result!['amount'], equals(25.50));
    });

    test('should extract amount from text without currency symbol', () {
      final result = AmountUtils.extractAmountFromText('Spent 25.50 on coffee');
      print('Result: $result');
      
      expect(result, isNotNull);
      expect(result!['amount'], equals(25.50));
    });
  });
}
