import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/parser/transaction_parsing_service.dart';
import '../../lib/services/parser/entity_extractor_base.dart';
import '../../lib/models/parse_result.dart';
import '../mocks/mock_entity_extractor.dart';
import '../mocks/mock_storage_service.dart';
import '../mocks/mock_localization_service.dart';

/// Comprehensive debug test for multiple number detection flow
/// This test validates the entire PRD v1.1.4 "Trust but Verify" approach
/// end-to-end with detailed logging for debugging purposes
void main() {
  group('Multiple Number Detection Debug Tests', () {
    late TransactionParsingService parserService;
    late MockEntityExtractor mockEntityExtractor;
    late MockStorageService mockStorageService;
    late MockLocalizationService mockLocalizationService;

    setUp(() async {
      print('DEBUG: Setting up multiple number debug test');

      mockEntityExtractor = MockEntityExtractor();
      mockStorageService = MockStorageService();
      await mockStorageService.init();

      mockLocalizationService = MockLocalizationService();
      mockLocalizationService.setupDefaultEnglishData();

      // Create parser service instance using factory method
      parserService = await TransactionParsingService.getInstance(
        mockStorageService,
        entityExtractor: mockEntityExtractor,
      );

      print('DEBUG: Test setup completed');
    });

    tearDown(() {
      mockEntityExtractor.reset();
      print('DEBUG: Test teardown completed');
    });

    group('PRD v1.1.4 Scenarios', () {
      test('Scenario 1: "lux69 100" should trigger amount confirmation with candidates [69, 100]', () async {
        print('\n=== DEBUG TEST: lux69 100 scenario ===');
        
        final testText = 'lux69 100';
        print('DEBUG: Testing text: "$testText"');
        
        // Configure ML Kit to find "69" embedded in "lux69"
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '69',
            start: 3, // Position of "69" in "lux69"
            end: 5,
            entityType: EntityType.money,
          ),
        ]);
        
        print('DEBUG: ML Kit configured to return: [69] at position 3-5');
        
        final stopwatch = Stopwatch()..start();
        final result = await parserService.parseTransaction(testText);
        stopwatch.stop();
        
        print('DEBUG: Parsing completed in ${stopwatch.elapsedMilliseconds}ms');
        print('DEBUG: Result status: ${result.status}');
        print('DEBUG: Candidate amounts: ${result.candidateAmounts}');
        print('DEBUG: Candidate texts: ${result.candidateTexts}');
        
        // Verify the result
        expect(result.status, ParseStatus.needsAmountConfirmation,
            reason: 'Should trigger amount confirmation for multiple candidates');
        expect(result.candidateAmounts, isNotNull,
            reason: 'Should have candidate amounts');
        expect(result.candidateTexts, isNotNull,
            reason: 'Should have candidate texts');
        
        // Verify both ML Kit (69) and raw finder (100) candidates are present
        expect(result.candidateAmounts!.contains(69.0), true,
            reason: 'Should include ML Kit candidate 69');
        expect(result.candidateAmounts!.contains(100.0), true,
            reason: 'Should include raw finder candidate 100');
        
        print('DEBUG: ✅ Test passed - amount confirmation triggered correctly');
      });

      test('Scenario 2: "restaurant45 mall 200" should detect both embedded and standalone numbers', () async {
        print('\n=== DEBUG TEST: restaurant45 mall 200 scenario ===');
        
        final testText = 'restaurant45 mall 200';
        print('DEBUG: Testing text: "$testText"');
        
        // Configure ML Kit to find "45" embedded in "restaurant45"
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '45',
            start: 10, // Position of "45" in "restaurant45"
            end: 12,
            entityType: EntityType.money,
          ),
        ]);
        
        print('DEBUG: ML Kit configured to return: [45] at position 10-12');
        
        final stopwatch = Stopwatch()..start();
        final result = await parserService.parseTransaction(testText);
        stopwatch.stop();
        
        print('DEBUG: Parsing completed in ${stopwatch.elapsedMilliseconds}ms');
        print('DEBUG: Result status: ${result.status}');
        print('DEBUG: Candidate amounts: ${result.candidateAmounts}');
        print('DEBUG: Candidate texts: ${result.candidateTexts}');
        
        // Verify the result
        expect(result.status, ParseStatus.needsAmountConfirmation,
            reason: 'Should trigger amount confirmation for embedded vs standalone numbers');
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateTexts, isNotNull);
        
        // Should include both embedded (45) and standalone (200) numbers
        expect(result.candidateAmounts!.contains(45.0), true,
            reason: 'Should include embedded number 45');
        expect(result.candidateAmounts!.contains(200.0), true,
            reason: 'Should include standalone number 200');
        
        print('DEBUG: ✅ Test passed - both embedded and standalone numbers detected');
      });

      test('Scenario 3: "cafe88 bill 150" should consolidate ML Kit and raw finder results', () async {
        print('\n=== DEBUG TEST: cafe88 bill 150 scenario ===');
        
        final testText = 'cafe88 bill 150';
        print('DEBUG: Testing text: "$testText"');
        
        // Configure ML Kit to find "88" embedded in "cafe88"
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '88',
            start: 4, // Position of "88" in "cafe88"
            end: 6,
            entityType: EntityType.money,
          ),
        ]);
        
        print('DEBUG: ML Kit configured to return: [88] at position 4-6');
        
        final stopwatch = Stopwatch()..start();
        final result = await parserService.parseTransaction(testText);
        stopwatch.stop();
        
        print('DEBUG: Parsing completed in ${stopwatch.elapsedMilliseconds}ms');
        print('DEBUG: Result status: ${result.status}');
        print('DEBUG: Candidate amounts: ${result.candidateAmounts}');
        print('DEBUG: Candidate texts: ${result.candidateTexts}');
        
        // Verify consolidation worked correctly
        expect(result.status, ParseStatus.needsAmountConfirmation,
            reason: 'Should trigger amount confirmation for consolidated candidates');
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateTexts, isNotNull);
        
        // Should consolidate ML Kit (88) and raw finder (150) results
        expect(result.candidateAmounts!.contains(88.0), true,
            reason: 'Should include ML Kit candidate 88');
        expect(result.candidateAmounts!.contains(150.0), true,
            reason: 'Should include raw finder candidate 150');
        
        print('DEBUG: ✅ Test passed - ML Kit and raw finder results consolidated');
      });
    });

    group('Edge Cases', () {
      test('ML Kit finds nothing but raw finder finds numbers', () async {
        print('\n=== DEBUG TEST: ML Kit empty, raw finder finds numbers ===');
        
        final testText = 'spent 50 and 75 today';
        print('DEBUG: Testing text: "$testText"');
        
        // Configure ML Kit to return empty results
        mockEntityExtractor.setMockResults([]);
        print('DEBUG: ML Kit configured to return: []');
        
        final stopwatch = Stopwatch()..start();
        final result = await parserService.parseTransaction(testText);
        stopwatch.stop();
        
        print('DEBUG: Parsing completed in ${stopwatch.elapsedMilliseconds}ms');
        print('DEBUG: Result status: ${result.status}');
        print('DEBUG: Candidate amounts: ${result.candidateAmounts}');
        print('DEBUG: Candidate texts: ${result.candidateTexts}');
        
        // Should still trigger amount confirmation from raw finder results
        expect(result.status, ParseStatus.needsAmountConfirmation,
            reason: 'Should trigger amount confirmation from raw finder alone');
        expect(result.candidateAmounts!.contains(50.0), true);
        expect(result.candidateAmounts!.contains(75.0), true);
        
        print('DEBUG: ✅ Test passed - raw finder works when ML Kit finds nothing');
      });

      test('Both find same numbers (deduplication)', () async {
        print('\n=== DEBUG TEST: Deduplication scenario ===');
        
        final testText = 'paid 100 dollars';
        print('DEBUG: Testing text: "$testText"');
        
        // Configure ML Kit to find "100" at the same position raw finder would find it
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '100',
            start: 5, // Position of "100" in "paid 100 dollars"
            end: 8,
            entityType: EntityType.money,
          ),
        ]);
        
        print('DEBUG: ML Kit configured to return: [100] at position 5-8');
        
        final stopwatch = Stopwatch()..start();
        final result = await parserService.parseTransaction(testText);
        stopwatch.stop();
        
        print('DEBUG: Parsing completed in ${stopwatch.elapsedMilliseconds}ms');
        print('DEBUG: Result status: ${result.status}');
        print('DEBUG: Candidate amounts: ${result.candidateAmounts}');
        print('DEBUG: Candidate texts: ${result.candidateTexts}');
        
        // Should not trigger amount confirmation for single deduplicated candidate
        expect(result.status, isNot(ParseStatus.needsAmountConfirmation),
            reason: 'Should not trigger confirmation for single deduplicated candidate');
        
        print('DEBUG: ✅ Test passed - deduplication prevents false ambiguity');
      });

      test('Both find different numbers (true consolidation)', () async {
        print('\n=== DEBUG TEST: True consolidation scenario ===');
        
        final testText = 'shop123 total 500';
        print('DEBUG: Testing text: "$testText"');
        
        // Configure ML Kit to find "123" embedded in "shop123"
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '123',
            start: 4, // Position of "123" in "shop123"
            end: 7,
            entityType: EntityType.money,
          ),
        ]);
        
        print('DEBUG: ML Kit configured to return: [123] at position 4-7');
        
        final stopwatch = Stopwatch()..start();
        final result = await parserService.parseTransaction(testText);
        stopwatch.stop();
        
        print('DEBUG: Parsing completed in ${stopwatch.elapsedMilliseconds}ms');
        print('DEBUG: Result status: ${result.status}');
        print('DEBUG: Candidate amounts: ${result.candidateAmounts}');
        print('DEBUG: Candidate texts: ${result.candidateTexts}');
        
        // Should trigger amount confirmation for true consolidation
        expect(result.status, ParseStatus.needsAmountConfirmation,
            reason: 'Should trigger confirmation for different numbers from both sources');
        expect(result.candidateAmounts!.contains(123.0), true,
            reason: 'Should include ML Kit candidate 123');
        expect(result.candidateAmounts!.contains(500.0), true,
            reason: 'Should include raw finder candidate 500');
        
        print('DEBUG: ✅ Test passed - true consolidation works correctly');
      });
    });

    group('Performance Tests', () {
      test('Multiple number detection does not impact parsing speed significantly', () async {
        print('\n=== DEBUG TEST: Performance impact ===');
        
        final testTexts = [
          'simple 100',
          'complex shop123 mall456 total 789',
          'very complex restaurant99 bill 250 tip 50 tax 25',
        ];
        
        for (final testText in testTexts) {
          print('DEBUG: Testing performance for: "$testText"');
          
          // Configure ML Kit for complex scenario
          mockEntityExtractor.setMockResults([
            MockEntityAnnotation(
              text: '99',
              start: testText.indexOf('99'),
              end: testText.indexOf('99') + 2,
              entityType: EntityType.money,
            ),
          ]);
          
          final stopwatch = Stopwatch()..start();
          final result = await parserService.parseTransaction(testText);
          stopwatch.stop();
          
          final elapsedMs = stopwatch.elapsedMilliseconds;
          print('DEBUG: Parsing completed in ${elapsedMs}ms');
          
          // Performance should be reasonable (under 100ms for these simple cases)
          expect(elapsedMs, lessThan(100),
              reason: 'Parsing should complete within reasonable time');
          
          print('DEBUG: Result status: ${result.status}');
        }
        
        print('DEBUG: ✅ Performance test passed - parsing remains fast');
      });
    });

    group('ParseResult Validation', () {
      test('ParseResult.needsAmountConfirmation has correct structure', () async {
        print('\n=== DEBUG TEST: ParseResult structure validation ===');
        
        final testText = 'lux69 100';
        print('DEBUG: Testing ParseResult structure for: "$testText"');
        
        mockEntityExtractor.setMockResults([
          MockEntityAnnotation(
            text: '69',
            start: 3,
            end: 5,
            entityType: EntityType.money,
          ),
        ]);
        
        final result = await parserService.parseTransaction(testText);
        
        print('DEBUG: Validating ParseResult structure...');
        
        // Validate ParseResult structure
        expect(result.status, ParseStatus.needsAmountConfirmation);
        expect(result.candidateAmounts, isNotNull);
        expect(result.candidateTexts, isNotNull);
        expect(result.transaction, isNotNull);
        
        // Validate arrays have same length
        expect(result.candidateAmounts!.length, result.candidateTexts!.length,
            reason: 'candidateAmounts and candidateTexts should have same length');
        
        // Validate transaction has reasonable defaults
        expect(result.transaction.description, testText.trim());
        expect(result.transaction.currencyCode, isNotEmpty);
        expect(result.transaction.type, isNotNull);
        
        print('DEBUG: candidateAmounts length: ${result.candidateAmounts!.length}');
        print('DEBUG: candidateTexts length: ${result.candidateTexts!.length}');
        print('DEBUG: Transaction currency: ${result.transaction.currencyCode}');
        print('DEBUG: Transaction type: ${result.transaction.type}');
        
        print('DEBUG: ✅ ParseResult structure validation passed');
      });
    });
  });
}
