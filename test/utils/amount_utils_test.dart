import 'package:flutter_test/flutter_test.dart';
import '../../lib/utils/amount_utils.dart';

void main() {
  group('AmountUtils Tests', () {
    
    group('parseAbbreviatedNumber', () {
      test('should parse basic abbreviations correctly', () {
        expect(AmountUtils.parseAbbreviatedNumber('100k'), equals(100000.0));
        expect(AmountUtils.parseAbbreviatedNumber('2M'), equals(2000000.0));
        expect(AmountUtils.parseAbbreviatedNumber('1.5B'), equals(1500000000.0));
      });

      test('should handle case insensitivity', () {
        expect(AmountUtils.parseAbbreviatedNumber('100K'), equals(100000.0));
        expect(AmountUtils.parseAbbreviatedNumber('2m'), equals(2000000.0));
        expect(AmountUtils.parseAbbreviatedNumber('1.5b'), equals(1500000000.0));
      });

      test('should handle decimal values', () {
        expect(AmountUtils.parseAbbreviatedNumber('1.25k'), equals(1250.0));
        expect(AmountUtils.parseAbbreviatedNumber('2.5M'), equals(2500000.0));
        expect(AmountUtils.parseAbbreviatedNumber('0.5B'), equals(500000000.0));
      });

      test('should handle edge cases', () {
        expect(AmountUtils.parseAbbreviatedNumber('0k'), equals(0.0));
        expect(AmountUtils.parseAbbreviatedNumber('0M'), equals(0.0));
        expect(AmountUtils.parseAbbreviatedNumber('0B'), equals(0.0));
      });

      test('should handle numbers without abbreviations', () {
        expect(AmountUtils.parseAbbreviatedNumber('100'), equals(100.0));
        expect(AmountUtils.parseAbbreviatedNumber('1500.50'), equals(1500.50));
        expect(AmountUtils.parseAbbreviatedNumber('0'), equals(0.0));
      });

      test('should return null for invalid input', () {
        expect(AmountUtils.parseAbbreviatedNumber(''), isNull);
        expect(AmountUtils.parseAbbreviatedNumber('   '), isNull);
        expect(AmountUtils.parseAbbreviatedNumber('invalid'), isNull);
        expect(AmountUtils.parseAbbreviatedNumber('k'), isNull);
        expect(AmountUtils.parseAbbreviatedNumber('M'), isNull);
        expect(AmountUtils.parseAbbreviatedNumber('abc123k'), isNull);
        expect(AmountUtils.parseAbbreviatedNumber('1.2.3k'), isNull);
      });

      test('should handle boundary values', () {
        expect(AmountUtils.parseAbbreviatedNumber('999.999k'), equals(999999.0));
        expect(AmountUtils.parseAbbreviatedNumber('1000k'), equals(1000000.0));
        expect(AmountUtils.parseAbbreviatedNumber('999.999M'), equals(999999000.0));
        expect(AmountUtils.parseAbbreviatedNumber('1000M'), equals(1000000000.0));
      });

      test('should handle whitespace', () {
        expect(AmountUtils.parseAbbreviatedNumber(' 100k '), equals(100000.0));
        expect(AmountUtils.parseAbbreviatedNumber('\t2M\n'), equals(2000000.0));
      });
    });

    group('extractAmountFromText', () {
      test('should extract basic amounts with abbreviations', () {
        final result1 = AmountUtils.extractAmountFromText('100k VND');
        expect(result1?['amount'], equals(100000.0));
        expect(result1?['currency'], equals('VND'));

        final result2 = AmountUtils.extractAmountFromText('\$2.5M');
        expect(result2?['amount'], equals(2500000.0));
        expect(result2?['currency'], equals('USD'));

        final result3 = AmountUtils.extractAmountFromText('€1.2k');
        expect(result3?['amount'], equals(1200.0));
        expect(result3?['currency'], equals('EUR'));
      });

      test('should handle currency symbols with abbreviations', () {
        final result1 = AmountUtils.extractAmountFromText('\$100k shopping');
        expect(result1?['amount'], equals(100000.0));
        expect(result1?['currency'], equals('USD'));

        final result2 = AmountUtils.extractAmountFromText('€2.5M bonus');
        expect(result2?['amount'], equals(2500000.0));
        expect(result2?['currency'], equals('EUR'));

        final result3 = AmountUtils.extractAmountFromText('₫1.5k transport');
        expect(result3?['amount'], equals(1500.0));
        expect(result3?['currency'], equals('VND'));
      });

      test('should handle case variations', () {
        final result1 = AmountUtils.extractAmountFromText('100K food');
        expect(result1?['amount'], equals(100000.0));

        final result2 = AmountUtils.extractAmountFromText('2m salary');
        expect(result2?['amount'], equals(2000000.0));

        final result3 = AmountUtils.extractAmountFromText('1.5B investment');
        expect(result3?['amount'], equals(1500000000.0));
      });

      test('should handle thousands separators with abbreviations', () {
        final result1 = AmountUtils.extractAmountFromText('1,500k');
        expect(result1?['amount'], equals(1500000.0));

        final result2 = AmountUtils.extractAmountFromText('2,500.50M dollars');
        expect(result2?['amount'], equals(2500500000.0));
        expect(result2?['currency'], equals('USD'));
      });

      test('should handle custom separators', () {
        final result1 = AmountUtils.extractAmountFromText(
          '1.500k euros', 
          thousandsSeparator: '.', 
          decimalSeparator: ','
        );
        expect(result1?['amount'], equals(1500000.0));
        expect(result1?['currency'], equals('EUR'));

        final result2 = AmountUtils.extractAmountFromText(
          '2.500,50M', 
          thousandsSeparator: '.', 
          decimalSeparator: ','
        );
        expect(result2?['amount'], equals(2500500000.0));
      });

      test('should handle mixed formats', () {
        final result1 = AmountUtils.extractAmountFromText('spent 1,500k on shopping');
        expect(result1?['amount'], equals(1500000.0));

        final result2 = AmountUtils.extractAmountFromText('received 2.5M dollars bonus');
        expect(result2?['amount'], equals(2500000.0));
        expect(result2?['currency'], equals('USD'));
      });

      test('should handle currency codes with abbreviations', () {
        final result1 = AmountUtils.extractAmountFromText('100k USD');
        expect(result1?['amount'], equals(100000.0));
        expect(result1?['currency'], equals('USD'));

        final result2 = AmountUtils.extractAmountFromText('2.5M EUR');
        expect(result2?['amount'], equals(2500000.0));
        expect(result2?['currency'], equals('EUR'));
      });

      test('should handle currency names with abbreviations', () {
        final result1 = AmountUtils.extractAmountFromText('100k dollars');
        expect(result1?['amount'], equals(100000.0));
        expect(result1?['currency'], equals('USD'));

        final result2 = AmountUtils.extractAmountFromText('2.5M euros');
        expect(result2?['amount'], equals(2500000.0));
        expect(result2?['currency'], equals('EUR'));
      });

      test('should return null for invalid input', () {
        expect(AmountUtils.extractAmountFromText(''), isNull);
        expect(AmountUtils.extractAmountFromText('no amount here'), isNull);
        expect(AmountUtils.extractAmountFromText('just text'), isNull);
        expect(AmountUtils.extractAmountFromText('k without number'), isNull);
      });

      test('should handle amounts without abbreviations', () {
        final result1 = AmountUtils.extractAmountFromText('\$100.50');
        expect(result1?['amount'], equals(100.50));
        expect(result1?['currency'], equals('USD'));

        final result2 = AmountUtils.extractAmountFromText('1,500 euros');
        expect(result2?['amount'], equals(1500.0));
        expect(result2?['currency'], equals('EUR'));
      });

      test('should handle amounts without currency', () {
        final result1 = AmountUtils.extractAmountFromText('100k for food');
        expect(result1?['amount'], equals(100000.0));
        expect(result1?['currency'], isNull);

        final result2 = AmountUtils.extractAmountFromText('spent 2.5M');
        expect(result2?['amount'], equals(2500000.0));
        expect(result2?['currency'], isNull);
      });
    });

    group('regression tests', () {
      test('should maintain backward compatibility with existing patterns', () {
        // Test existing amount patterns still work
        final result1 = AmountUtils.extractAmountFromText('\$100.50');
        expect(result1?['amount'], equals(100.50));
        expect(result1?['currency'], equals('USD'));

        final result2 = AmountUtils.extractAmountFromText('€250.75');
        expect(result2?['amount'], equals(250.75));
        expect(result2?['currency'], equals('EUR'));

        final result3 = AmountUtils.extractAmountFromText('1,500 dollars');
        expect(result3?['amount'], equals(1500.0));
        expect(result3?['currency'], equals('USD'));
      });

      test('should handle real transaction text examples', () {
        final result1 = AmountUtils.extractAmountFromText('100k starbucks coffee');
        expect(result1?['amount'], equals(100000.0));
        expect(result1?['currency'], isNull);

        final result2 = AmountUtils.extractAmountFromText('spent \$2.5M on house');
        expect(result2?['amount'], equals(2500000.0));
        expect(result2?['currency'], equals('USD'));

        final result3 = AmountUtils.extractAmountFromText('received 1.2B VND salary');
        expect(result3?['amount'], equals(1200000000.0));
        expect(result3?['currency'], equals('VND'));
      });
    });
  });
}
