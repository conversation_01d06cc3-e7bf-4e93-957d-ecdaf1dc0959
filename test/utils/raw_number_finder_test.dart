import 'package:flutter_test/flutter_test.dart';
import '../../lib/utils/raw_number_finder.dart';
import '../../lib/models/amount_candidate.dart';

void main() {
  group('RawNumberFinder Tests', () {
    group('Basic Numbers', () {
      test('should find simple integer', () {
        final candidates = RawNumberFinder.findAllNumbers('I spent 123 today');
        
        expect(candidates, hasLength(1));
        expect(candidates[0].amount, equals(123.0));
        expect(candidates[0].start, equals(8));
        expect(candidates[0].end, equals(11));
        expect(candidates[0].sourceText, equals('123'));
        expect(candidates[0].source, equals(AmountSource.rawNumberFinder));
      });

      test('should find decimal number', () {
        final candidates = RawNumberFinder.findAllNumbers('Cost was 45.67 dollars');

        expect(candidates, hasLength(1));
        expect(candidates[0].amount, equals(45.67));
        expect(candidates[0].start, equals(9));
        expect(candidates[0].end, equals(22)); // Includes "dollars"
        expect(candidates[0].sourceText, equals('45.67 dollars'));
      });

      test('should find number with thousands separator', () {
        final candidates = RawNumberFinder.findAllNumbers('Price: 1,500 euros');

        expect(candidates, hasLength(1));
        expect(candidates[0].amount, equals(1500.0));
        expect(candidates[0].sourceText, equals('1,500 eur')); // Includes currency name
        expect(candidates[0].currency, equals('EUR'));
      });
    });

    group('Abbreviated Numbers', () {
      test('should find numbers with k suffix', () {
        final testCases = [
          {'text': 'Earned 100k this year', 'expected': 100000.0},
          {'text': 'Spent 2.5K on vacation', 'expected': 2500.0},
          {'text': 'Budget: 50k', 'expected': 50000.0},
        ];

        for (final testCase in testCases) {
          final candidates = RawNumberFinder.findAllNumbers(testCase['text'] as String);
          
          expect(candidates, hasLength(1), 
              reason: 'Should find one candidate in: ${testCase['text']}');
          expect(candidates[0].amount, equals(testCase['expected']),
              reason: 'Amount parsing failed for: ${testCase['text']}');
        }
      });

      test('should find numbers with M suffix', () {
        final testCases = [
          {'text': 'Revenue: 2.5M dollars', 'expected': 2500000.0},
          {'text': 'Investment of 1m', 'expected': 1000000.0},
          {'text': 'Worth 10M', 'expected': 10000000.0},
        ];

        for (final testCase in testCases) {
          final candidates = RawNumberFinder.findAllNumbers(testCase['text'] as String);
          
          expect(candidates, hasLength(1),
              reason: 'Should find one candidate in: ${testCase['text']}');
          expect(candidates[0].amount, equals(testCase['expected']),
              reason: 'Amount parsing failed for: ${testCase['text']}');
        }
      });

      test('should find numbers with B suffix', () {
        final testCases = [
          {'text': 'Company valued at 1.2B', 'expected': 1200000000.0},
          {'text': 'GDP: 5b dollars', 'expected': 5000000000.0},
          {'text': 'Market cap 10B', 'expected': 10000000000.0},
        ];

        for (final testCase in testCases) {
          final candidates = RawNumberFinder.findAllNumbers(testCase['text'] as String);
          
          expect(candidates, hasLength(1),
              reason: 'Should find one candidate in: ${testCase['text']}');
          expect(candidates[0].amount, equals(testCase['expected']),
              reason: 'Amount parsing failed for: ${testCase['text']}');
        }
      });
    });

    group('Currency Symbols', () {
      test('should find numbers with dollar sign', () {
        final candidates = RawNumberFinder.findAllNumbers('Paid \$100.50 for dinner');
        
        expect(candidates, hasLength(1));
        expect(candidates[0].amount, equals(100.50));
        expect(candidates[0].currency, equals('USD'));
        expect(candidates[0].sourceText, equals('\$100.50'));
      });

      test('should find numbers with euro symbol', () {
        final candidates = RawNumberFinder.findAllNumbers('Cost €250 for the trip');
        
        expect(candidates, hasLength(1));
        expect(candidates[0].amount, equals(250.0));
        expect(candidates[0].currency, equals('EUR'));
        expect(candidates[0].sourceText, equals('€250'));
      });

      test('should find numbers with various currency symbols', () {
        final testCases = [
          {'text': '£50 for books', 'expectedCurrency': 'GBP'},
          {'text': '¥1000 for lunch', 'expectedCurrency': 'JPY'},
          {'text': '₹500 for groceries', 'expectedCurrency': 'INR'},
        ];

        for (final testCase in testCases) {
          final candidates = RawNumberFinder.findAllNumbers(testCase['text'] as String);
          
          expect(candidates, hasLength(1),
              reason: 'Should find one candidate in: ${testCase['text']}');
          expect(candidates[0].currency, equals(testCase['expectedCurrency']),
              reason: 'Currency detection failed for: ${testCase['text']}');
        }
      });
    });

    group('Currency Names and Codes', () {
      test('should detect currency from names', () {
        final testCases = [
          {'text': '100 dollars for food', 'expectedCurrency': 'USD'},
          {'text': '50 euros shopping', 'expectedCurrency': 'EUR'},
          {'text': '1000 yen for transport', 'expectedCurrency': 'JPY'},
          {'text': '500 dong for coffee', 'expectedCurrency': 'VND'},
        ];

        for (final testCase in testCases) {
          final candidates = RawNumberFinder.findAllNumbers(testCase['text'] as String);
          
          expect(candidates, hasLength(1),
              reason: 'Should find one candidate in: ${testCase['text']}');
          expect(candidates[0].currency, equals(testCase['expectedCurrency']),
              reason: 'Currency name detection failed for: ${testCase['text']}');
        }
      });

      test('should detect currency from codes', () {
        final testCases = [
          {'text': '100 USD for dinner', 'expectedCurrency': 'USD'},
          {'text': '50 EUR for shopping', 'expectedCurrency': 'EUR'},
          {'text': '1000 VND for coffee', 'expectedCurrency': 'VND'},
          {'text': '200 GBP for books', 'expectedCurrency': 'GBP'},
        ];

        for (final testCase in testCases) {
          final candidates = RawNumberFinder.findAllNumbers(testCase['text'] as String);
          
          expect(candidates, hasLength(1),
              reason: 'Should find one candidate in: ${testCase['text']}');
          expect(candidates[0].currency, equals(testCase['expectedCurrency']),
              reason: 'Currency code detection failed for: ${testCase['text']}');
        }
      });
    });

    group('Multiple Numbers', () {
      test('should find all numbers in text with multiple amounts', () {
        final candidates = RawNumberFinder.findAllNumbers('Spent \$25 on coffee and €50 on lunch');
        
        expect(candidates, hasLength(2));
        
        // First candidate (dollar amount)
        expect(candidates[0].amount, equals(25.0));
        expect(candidates[0].currency, equals('USD'));
        expect(candidates[0].sourceText, equals('\$25'));
        
        // Second candidate (euro amount)
        expect(candidates[1].amount, equals(50.0));
        expect(candidates[1].currency, equals('EUR'));
        expect(candidates[1].sourceText, equals('€50'));
      });

      test('should find numbers in complex text', () {
        final candidates = RawNumberFinder.findAllNumbers('Budget: 1000k for project, 500 for supplies, 2.5M total');
        
        expect(candidates, hasLength(3));
        expect(candidates[0].amount, equals(1000000.0)); // 1000k
        expect(candidates[1].amount, equals(500.0)); // 500
        expect(candidates[2].amount, equals(2500000.0)); // 2.5M
      });
    });

    group('Embedded Numbers (Should NOT be filtered)', () {
      test('should find embedded numbers in vendor names', () {
        final candidates = RawNumberFinder.findAllNumbers('dinner at lux69 100k vnd');
        
        expect(candidates, hasLength(2));
        
        // Should find both the embedded number and the standalone amount
        final amounts = candidates.map((c) => c.amount).toList();
        expect(amounts, containsAll([69.0, 100000.0]));
      });

      test('should find all numbers including embedded ones', () {
        final candidates = RawNumberFinder.findAllNumbers('restaurant45 mall 200');
        
        expect(candidates, hasLength(2));
        
        final amounts = candidates.map((c) => c.amount).toList();
        expect(amounts, containsAll([45.0, 200.0]));
      });

      test('should find embedded numbers in complex vendor names', () {
        final candidates = RawNumberFinder.findAllNumbers('cafe88 bill 150 total');
        
        expect(candidates, hasLength(2));
        
        final amounts = candidates.map((c) => c.amount).toList();
        expect(amounts, containsAll([88.0, 150.0]));
      });
    });

    group('Edge Cases', () {
      test('should return empty list for empty text', () {
        final candidates = RawNumberFinder.findAllNumbers('');
        expect(candidates, isEmpty);
      });

      test('should return empty list for text with no numbers', () {
        final candidates = RawNumberFinder.findAllNumbers('Just some text without any numbers');
        expect(candidates, isEmpty);
      });

      test('should handle text with only currency symbols', () {
        final candidates = RawNumberFinder.findAllNumbers('Just \$ and € symbols');
        expect(candidates, isEmpty);
      });

      test('should handle very large numbers', () {
        final candidates = RawNumberFinder.findAllNumbers('Amount: 999999999');
        
        expect(candidates, hasLength(1));
        expect(candidates[0].amount, equals(999999999.0));
      });

      test('should handle zero amounts', () {
        final candidates = RawNumberFinder.findAllNumbers('Balance: \$0.00');
        
        expect(candidates, hasLength(1));
        expect(candidates[0].amount, equals(0.0));
        expect(candidates[0].currency, equals('USD'));
      });

      test('should handle decimal-only numbers', () {
        final candidates = RawNumberFinder.findAllNumbers('Cost: 0.99 cents');
        
        expect(candidates, hasLength(1));
        expect(candidates[0].amount, equals(0.99));
      });
    });

    group('Real-world Examples', () {
      test('should handle complex real-world transaction text', () {
        final candidates = RawNumberFinder.findAllNumbers('dinner at lux69 100k vnd');
        
        expect(candidates, hasLength(2));
        
        // Should find both embedded number and main amount
        final amounts = candidates.map((c) => c.amount).toList();
        expect(amounts, containsAll([69.0, 100000.0]));
        
        // Check that VND currency is detected for the 100k amount
        final vndCandidate = candidates.firstWhere((c) => c.amount == 100000.0);
        expect(vndCandidate.currency, equals('VND'));
      });

      test('should handle shopping scenario', () {
        final candidates = RawNumberFinder.findAllNumbers('shop123 total 500k for electronics');
        
        expect(candidates, hasLength(2));
        
        final amounts = candidates.map((c) => c.amount).toList();
        expect(amounts, containsAll([123.0, 500000.0]));
      });

      test('should handle restaurant scenario', () {
        final candidates = RawNumberFinder.findAllNumbers('restaurant45 mall 200 USD');
        
        expect(candidates, hasLength(2));
        
        final amounts = candidates.map((c) => c.amount).toList();
        expect(amounts, containsAll([45.0, 200.0]));
        
        // Check USD currency detection for the 200 amount
        final usdCandidate = candidates.firstWhere((c) => c.amount == 200.0);
        expect(usdCandidate.currency, equals('USD'));
      });
    });

    group('Position Accuracy', () {
      test('should provide accurate start and end positions', () {
        final text = 'I spent \$100.50 on coffee';
        final candidates = RawNumberFinder.findAllNumbers(text);
        
        expect(candidates, hasLength(1));
        
        final candidate = candidates[0];
        final extractedText = text.substring(candidate.start, candidate.end);
        expect(extractedText, equals('\$100.50'));
      });

      test('should provide accurate positions for multiple numbers', () {
        final text = 'Budget: 1000k project, 500 supplies';
        final candidates = RawNumberFinder.findAllNumbers(text);
        
        expect(candidates, hasLength(2));
        
        for (final candidate in candidates) {
          final extractedText = text.substring(candidate.start, candidate.end);
          expect(extractedText, equals(candidate.sourceText));
        }
      });
    });

    group('Sorting and Ordering', () {
      test('should return candidates sorted by position', () {
        final candidates = RawNumberFinder.findAllNumbers('First 100, then 200, finally 300');
        
        expect(candidates, hasLength(3));
        expect(candidates[0].amount, equals(100.0));
        expect(candidates[1].amount, equals(200.0));
        expect(candidates[2].amount, equals(300.0));
        
        // Verify positions are in ascending order
        for (int i = 1; i < candidates.length; i++) {
          expect(candidates[i].start, greaterThan(candidates[i-1].start));
        }
      });
    });

    group('Currency Context Detection', () {
      test('should detect currency from nearby context', () {
        final candidates = RawNumberFinder.findAllNumbers('Paid 100 dollars for the meal');
        
        expect(candidates, hasLength(1));
        expect(candidates[0].amount, equals(100.0));
        expect(candidates[0].currency, equals('USD'));
      });

      test('should detect currency symbol in context', () {
        final candidates = RawNumberFinder.findAllNumbers('Total: 150 \$ for shopping');
        
        expect(candidates, hasLength(1));
        expect(candidates[0].amount, equals(150.0));
        expect(candidates[0].currency, equals('USD'));
      });

      test('should handle no currency context', () {
        final candidates = RawNumberFinder.findAllNumbers('Just a number 42 here');
        
        expect(candidates, hasLength(1));
        expect(candidates[0].amount, equals(42.0));
        expect(candidates[0].currency, isNull);
      });
    });
  });
}
