# Contributing to <PERSON> Lover Chat

Thank you for your interest in contributing to Money Lover Chat! This document provides guidelines and instructions for contributing to the project.

## Table of Contents

1. [Code of Conduct](#code-of-conduct)
2. [Getting Started](#getting-started)
3. [Development Workflow](#development-workflow)
4. [Pull Request Process](#pull-request-process)
5. [Coding Standards](#coding-standards)
6. [Documentation](#documentation)
7. [Testing](#testing)
8. [Issue Reporting](#issue-reporting)

## Code of Conduct

Please be respectful and inclusive when contributing to this project. Treat all participants with respect and adhere to professional standards of communication.

## Getting Started

### Prerequisites

- Flutter SDK 3.0.0 or higher
- Dart SDK 3.0.0 or higher
- Git

### Setup

1. Fork the repository
2. Clone your fork locally:
   ```bash
   git clone https://github.com/YOUR-USERNAME/money_lover_chat.git
   cd money_lover_chat
   ```
3. Add the upstream remote:
   ```bash
   git remote add upstream https://github.com/ORIGINAL-OWNER/money_lover_chat.git
   ```
4. Install dependencies:
   ```bash
   flutter pub get
   ```

## Development Workflow

1. **Create a branch for your changes**:
   ```bash
   git checkout -b feature/your-feature-name
   ```
   Use prefixes like `feature/`, `bugfix/`, `docs/`, etc.

2. **Make your changes**: Follow the coding standards and ensure tests pass.

3. **Commit your changes**: Write clear, descriptive commit messages:
   ```bash
   git commit -m "Add feature: description of your feature"
   ```

4. **Keep your branch updated**:
   ```bash
   git fetch upstream
   git rebase upstream/main
   ```

5. **Push your changes**:
   ```bash
   git push origin feature/your-feature-name
   ```

## Pull Request Process

1. **Open a Pull Request (PR)** from your branch to the main repository's `main` branch.

2. **Fill out the PR template** with:
   - Description of changes
   - Related issue(s)
   - Screenshots/GIFs for UI changes
   - Testing steps

3. **Code Review**: Wait for maintainers to review your PR. Address any feedback promptly.

4. **Continuous Integration**: Ensure all CI checks pass.

5. **Merge**: Once approved, a maintainer will merge your PR.

## Coding Standards

### Dart Style Guide

Follow the [Effective Dart Style Guide](https://dart.dev/guides/language/effective-dart/style) and the project's established patterns.

### Key Guidelines

- Use English for all code and documentation
- Use meaningful variable and function names
- Write clear, concise comments
- Keep functions small and focused
- Follow existing architectural patterns
- Use PascalCase for classes and camelCase for variables and functions
- Maintain proper indentation (2 spaces)

### Formatting

Run the formatter before committing:
```bash
flutter format .
```

### Linting

Ensure your code passes all linting rules:
```bash
flutter analyze
```

## Documentation

### Code Documentation

- Use `///` for DartDoc comments on all public APIs
- Document the purpose of classes, methods, and complex logic
- Update documentation when changing existing code

### Feature Documentation

For new features, update or create relevant documentation in the `docs/` directory:

- Update `README.md` if necessary
- Add to `docs/codebase.md` if you've added new files/directories
- Add feature guides for significant features

## Testing

### Writing Tests

- Write unit tests for business logic
- Write widget tests for UI components
- Test edge cases and error scenarios

### Running Tests

Run tests before submitting a PR:
```bash
flutter test
```

## Issue Reporting

### Bug Reports

When reporting bugs, include:

- Clear description of the issue
- Steps to reproduce
- Expected vs. actual behavior
- Screenshots/videos if applicable
- Environment details (Flutter version, device/OS)

### Feature Requests

For feature requests, include:

- Clear description of the proposed feature
- Use cases and benefits
- Any relevant mockups or examples

---

Thank you for contributing to Money Lover Chat! Your efforts help improve the app for everyone. 