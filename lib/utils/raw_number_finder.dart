import '../models/amount_candidate.dart';
import '../utils/amount_utils.dart';
import '../utils/currency_utils.dart';

/// Utility class that implements independent raw number finding logic
/// This finder discovers ALL numeric values in text without any filtering
/// for embedded numbers, as required by the PRD's "Trust but Verify" approach
class RawNumberFinder {
  /// Find all numbers in the given text and return them as AmountCandidate objects
  /// This method is completely independent and does not filter out any numbers
  /// 
  /// Supports:
  /// - Basic numbers: 123, 45.67
  /// - Abbreviated numbers: 100k, 2.5M, 1.2B
  /// - Numbers with thousands separators: 1,500
  /// - Numbers with currency symbols: $100, €50
  /// - Multiple numbers in one text
  /// - Embedded numbers in vendor names (NOT filtered out)
  /// 
  /// Returns List<AmountCandidate> with accurate start/end positions,
  /// parsed amounts, and detected currencies
  static List<AmountCandidate> findAllNumbers(String text) {
    if (text.isEmpty) return [];

    final List<AmountCandidate> candidates = [];

    // Comprehensive regex pattern to find all potential numbers
    // This pattern captures:
    // 1. Optional currency symbols at the start
    // 2. Numbers with optional thousands separators and decimals
    // 3. Optional abbreviation suffixes (k, m, b)
    // 4. Optional currency codes/names after the number
    final numberPattern = RegExp(
      r'(?:(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)\s*)?'  // Optional currency symbol
      r'(\d+(?:,\d{3})*(?:\.\d+)?)'  // Number with optional thousands separators and decimals
      r'([kKmMbB])?'  // Optional abbreviation
      r'(?:\s*(usd|eur|gbp|jpy|cny|inr|krw|aud|cad|chf|vnd|thb|try|ils|brl|rub|mxn|'
      r'dollars?|euros?|pounds?|yen|yuan|rupees?|won|pesos?|dong|baht|lira|shekel|reais?|rubles?))?',  // Optional currency name
      caseSensitive: false,
    );

    final matches = numberPattern.allMatches(text);

    for (final match in matches) {
      final currencySymbol = match.group(1);
      final numberText = match.group(2);
      final abbreviation = match.group(3);
      final currencyName = match.group(4);

      if (numberText == null || numberText.isEmpty) continue;



      // Normalize the number text by removing thousands separators
      String normalizedNumberText = numberText.replaceAll(',', '');

      // Parse the number with abbreviation support
      String fullNumberText = normalizedNumberText;
      if (abbreviation != null) {
        fullNumberText += abbreviation;
      }

      final amount = AmountUtils.parseAbbreviatedNumber(fullNumberText);
      if (amount == null) continue;

      // Determine currency from symbol or name
      String? currency;
      if (currencySymbol != null) {
        currency = CurrencyUtils.symbolToCurrencyCode(currencySymbol, context: text);
      } else if (currencyName != null) {
        currency = _currencyNameToCode(currencyName.toLowerCase());
      }

      // If no currency found from symbol/name, try to detect from context
      if (currency == null) {
        currency = _detectCurrencyFromContext(text, match.start, match.end);
      }

      // Create candidate with accurate positions
      final candidate = AmountCandidate.fromRawNumberFinder(
        amount: amount,
        currency: currency,
        start: match.start,
        end: match.end,
        sourceText: match.group(0)!,
      );

      candidates.add(candidate);
    }

    // Also find standalone numbers that might not have been caught by the main pattern
    // This catches simple numbers like "123" or "45.67" without currency context
    final simpleNumberPattern = RegExp(r'\b\d+(?:\.\d+)?(?:[kKmMbB])?\b');
    final simpleMatches = simpleNumberPattern.allMatches(text);

    for (final match in simpleMatches) {
      final numberText = match.group(0)!;
      final amount = AmountUtils.parseAbbreviatedNumber(numberText);
      
      if (amount == null) continue;

      // Check if this match is already covered by a more comprehensive match
      final alreadyCovered = candidates.any((candidate) =>
          candidate.start <= match.start && candidate.end >= match.end);
      
      if (alreadyCovered) continue;

      // Try to detect currency from surrounding context
      final currency = _detectCurrencyFromContext(text, match.start, match.end);

      final candidate = AmountCandidate.fromRawNumberFinder(
        amount: amount,
        currency: currency,
        start: match.start,
        end: match.end,
        sourceText: numberText,
      );

      candidates.add(candidate);
    }

    // Sort candidates by position for consistent ordering
    candidates.sort((a, b) => a.start.compareTo(b.start));



    return candidates;
  }

  /// Convert currency name to currency code
  static String? _currencyNameToCode(String currencyName) {
    final nameMap = {
      'dollar': 'USD',
      'dollars': 'USD',
      'euro': 'EUR',
      'euros': 'EUR',
      'pound': 'GBP',
      'pounds': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupee': 'INR',
      'rupees': 'INR',
      'won': 'KRW',
      'peso': 'MXN',
      'pesos': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'real': 'BRL',
      'reais': 'BRL',
      'ruble': 'RUB',
      'rubles': 'RUB',
      'usd': 'USD',
      'eur': 'EUR',
      'gbp': 'GBP',
      'jpy': 'JPY',
      'cny': 'CNY',
      'inr': 'INR',
      'krw': 'KRW',
      'aud': 'AUD',
      'cad': 'CAD',
      'chf': 'CHF',
      'vnd': 'VND',
      'thb': 'THB',
      'try': 'TRY',
      'ils': 'ILS',
      'brl': 'BRL',
      'rub': 'RUB',
      'mxn': 'MXN',
    };

    return nameMap[currencyName];
  }

  /// Detect currency from the surrounding context of a number
  static String? _detectCurrencyFromContext(String text, int start, int end) {
    // Look for currency indicators within a reasonable distance from the number
    const contextWindow = 20;
    final contextStart = (start - contextWindow).clamp(0, text.length);
    final contextEnd = (end + contextWindow).clamp(0, text.length);
    final contextText = text.substring(contextStart, contextEnd);

    // Check for currency symbols
    final currencySymbolPattern = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)');
    final symbolMatch = currencySymbolPattern.firstMatch(contextText);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: contextText);
    }

    // Check for currency codes and names
    final currencyNamePattern = RegExp(
      r'\b(usd|eur|gbp|jpy|cny|inr|krw|aud|cad|chf|vnd|thb|try|ils|brl|rub|mxn|'
      r'dollars?|euros?|pounds?|yen|yuan|rupees?|won|pesos?|dong|baht|lira|shekel|reais?|rubles?)\b',
      caseSensitive: false,
    );
    final nameMatch = currencyNamePattern.firstMatch(contextText);
    if (nameMatch != null) {
      return _currencyNameToCode(nameMatch.group(1)!.toLowerCase());
    }

    return null;
  }
}
