import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// A simple startup performance measurement utility
/// Tracks timing of various startup operations to help identify bottlenecks
class StartupTimer {
  static StartupTimer? _instance;
  
  final Map<String, int> _timestamps = {};
  final Map<String, Duration> _measurements = {};
  
  StartupTimer._();
  
  /// Get the singleton instance
  static StartupTimer get instance {
    _instance ??= StartupTimer._();
    return _instance!;
  }
  
  /// Reset the timer for a new measurement session
  static void reset() {
    _instance?._timestamps.clear();
    _instance?._measurements.clear();
  }
  
  /// Mark a timing point with a label
  /// Records the current timestamp for the given label
  void mark(String label) {
    if (!kDebugMode) return; // Only active in debug builds
    
    final timestamp = DateTime.now().microsecondsSinceEpoch;
    _timestamps[label] = timestamp;
    
    developer.log('Startup marker: $label at ${timestamp}μs', name: 'StartupTimer');
  }
  
  /// Measure the duration between two timing points
  /// Returns the duration between startLabel and endLabel
  Duration? measure(String startLabel, String endLabel) {
    if (!kDebugMode) return null;
    
    final startTime = _timestamps[startLabel];
    final endTime = _timestamps[endLabel];
    
    if (startTime == null || endTime == null) {
      developer.log('Cannot measure: missing timestamp for $startLabel or $endLabel', 
          name: 'StartupTimer');
      return null;
    }
    
    final duration = Duration(microseconds: endTime - startTime);
    _measurements['$startLabel -> $endLabel'] = duration;
    
    developer.log('Startup measurement: $startLabel -> $endLabel = ${duration.inMilliseconds}ms', 
        name: 'StartupTimer');
    
    return duration;
  }
  
  /// Get the duration since a specific marker
  Duration? durationSince(String label) {
    if (!kDebugMode) return null;
    
    final startTime = _timestamps[label];
    if (startTime == null) {
      developer.log('Cannot measure duration: missing timestamp for $label', 
          name: 'StartupTimer');
      return null;
    }
    
    final currentTime = DateTime.now().microsecondsSinceEpoch;
    return Duration(microseconds: currentTime - startTime);
  }
  
  /// Print a summary of all timing measurements
  void printSummary() {
    if (!kDebugMode) return;
    
    developer.log('=== Startup Performance Summary ===', name: 'StartupTimer');
    
    // Print all markers in chronological order
    final sortedMarkers = _timestamps.entries.toList()
      ..sort((a, b) => a.value.compareTo(b.value));
    
    developer.log('Timing Markers:', name: 'StartupTimer');
    for (final entry in sortedMarkers) {
      final relativeTime = entry.value - (sortedMarkers.first.value);
      developer.log('  ${entry.key}: +${relativeTime ~/ 1000}ms', name: 'StartupTimer');
    }
    
    // Print all measurements
    if (_measurements.isNotEmpty) {
      developer.log('Duration Measurements:', name: 'StartupTimer');
      for (final entry in _measurements.entries) {
        developer.log('  ${entry.key}: ${entry.value.inMilliseconds}ms', name: 'StartupTimer');
      }
    }
    
    // Calculate total startup time if we have app-start marker
    if (_timestamps.containsKey('app-start')) {
      final totalTime = durationSince('app-start');
      if (totalTime != null) {
        developer.log('Total startup time: ${totalTime.inMilliseconds}ms', name: 'StartupTimer');
      }
    }
    
    developer.log('=== End Summary ===', name: 'StartupTimer');
  }
  
  /// Get all recorded timestamps (for testing)
  Map<String, int> get timestamps => Map.unmodifiable(_timestamps);
  
  /// Get all recorded measurements (for testing)
  Map<String, Duration> get measurements => Map.unmodifiable(_measurements);
  
  /// Check if a marker exists
  bool hasMarker(String label) => _timestamps.containsKey(label);
  
  /// Get the timestamp for a specific marker
  int? getTimestamp(String label) => _timestamps[label];
}
