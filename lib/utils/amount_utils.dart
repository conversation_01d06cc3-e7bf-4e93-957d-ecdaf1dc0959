import 'currency_utils.dart';

/// Utility class for handling amount parsing with abbreviation support
/// Provides methods to parse abbreviated numbers (k/M/B) and extract amounts from text
class AmountUtils {
  
  /// Parse abbreviated number strings like '100k', '2.5M', '1.2B'
  /// Returns the expanded numeric value or null for invalid input
  /// 
  /// Supported abbreviations:
  /// - k/K: thousands (multiply by 1,000)
  /// - m/M: millions (multiply by 1,000,000)  
  /// - b/B: billions (multiply by 1,000,000,000)
  /// 
  /// Examples:
  /// - '100k' → 100000.0
  /// - '2.5M' → 2500000.0
  /// - '1.2B' → 1200000000.0
  /// - '0k' → 0.0
  /// - 'invalid' → null
  static double? parseAbbreviatedNumber(String token) {
    if (token.isEmpty) return null;
    
    final trimmed = token.trim();
    if (trimmed.isEmpty) return null;
    
    // Check if the token ends with an abbreviation
    final lastChar = trimmed[trimmed.length - 1].toLowerCase();
    double multiplier;
    String numberPart;
    
    switch (lastChar) {
      case 'k':
        multiplier = 1000.0;
        numberPart = trimmed.substring(0, trimmed.length - 1);
        break;
      case 'm':
        multiplier = 1000000.0;
        numberPart = trimmed.substring(0, trimmed.length - 1);
        break;
      case 'b':
        multiplier = 1000000000.0;
        numberPart = trimmed.substring(0, trimmed.length - 1);
        break;
      default:
        // No abbreviation, try to parse as regular number
        return double.tryParse(trimmed);
    }
    
    // Parse the numeric part
    final baseNumber = double.tryParse(numberPart);
    if (baseNumber == null) return null;
    
    // Handle edge cases
    if (baseNumber.isNaN || baseNumber.isInfinite) return null;
    
    final result = baseNumber * multiplier;
    
    // Check for overflow/underflow
    if (result.isInfinite || result.isNaN) return null;
    
    return result;
  }
  
  /// Extract amount and currency from text with abbreviation support
  /// Returns a map with 'amount' (double) and 'currency' (String?) keys
  /// 
  /// Supports:
  /// - Number abbreviations: '100k', '2.5M', '1.2B'
  /// - Currency symbols: '$', '€', '£', '¥', etc.
  /// - Currency codes: 'USD', 'EUR', 'GBP', etc.
  /// - Currency names: 'dollars', 'euros', 'pounds', etc.
  /// - Thousands separators: '1,500k', '2,500.50M'
  /// - Decimal separators: configurable via parameters
  /// 
  /// Parameters:
  /// - text: The text to parse
  /// - thousandsSeparator: Optional thousands separator (default: ',')
  /// - decimalSeparator: Optional decimal separator (default: '.')
  /// 
  /// Returns:
  /// - Map with 'amount' and 'currency' keys, or null if no amount found
  static Map<String, dynamic>? extractAmountFromText(
    String text, {
    String? thousandsSeparator,
    String? decimalSeparator,
  }) {
    if (text.isEmpty) return null;
    
    final thousandsSep = thousandsSeparator ?? ',';
    final decimalSep = decimalSeparator ?? '.';
    
    // Build currency symbols pattern
    final currencySymbolsPattern = r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)';
    
    // Build amount pattern with abbreviation support
    // Escape separators for regex
    final thousandsEscaped = RegExp.escape(thousandsSep);
    final decimalEscaped = RegExp.escape(decimalSep);
    
    // Pattern for numbers with optional thousands separators, decimals, and abbreviations
    final amountPattern = r'(\d+(?:' + thousandsEscaped + r'\d{3})*(?:' + decimalEscaped + r'\d+)?[kKmMbB]?)';
    
    // Combined regex pattern for currency symbol + amount or amount + currency
    final combinedPattern = currencySymbolsPattern + r'\s?' + amountPattern + 
                           r'|' + amountPattern + r'\s?(?:dollars?|USD|euros?|EUR|pounds?|GBP|yen|JPY|yuan|CNY|rupees?|INR|rubles?|RUB|won|KRW|pesos?|MXN|PHP|dong|VND|baht|THB|lira|TRY|shekel|ILS|reais?|BRL|SGD|HKD|AUD|CAD|NZD|' + currencySymbolsPattern + r')?';
    
    final regex = RegExp(combinedPattern, caseSensitive: false);
    final match = regex.firstMatch(text);
    
    if (match == null) return null;
    
    String? currencySymbol;
    String? amountString;
    
    // Check which pattern matched
    if (match.group(1) != null) {
      // Currency symbol + amount pattern
      currencySymbol = match.group(1);
      amountString = match.group(2);
    } else if (match.group(3) != null) {
      // Amount + optional currency pattern
      amountString = match.group(3);
      currencySymbol = match.group(4); // This might be null
    }
    
    if (amountString == null) return null;
    
    // Normalize the amount string by removing thousands separators
    String normalizedAmount = amountString.replaceAll(thousandsSep, '');
    
    // Replace decimal separator with standard dot if different
    if (decimalSep != '.') {
      normalizedAmount = normalizedAmount.replaceAll(decimalSep, '.');
    }
    
    // Parse the amount (with potential abbreviation)
    final amount = parseAbbreviatedNumber(normalizedAmount);
    if (amount == null) return null;
    
    // Extract currency information
    String? currency = _extractCurrencyFromText(text);
    if (currency == null && currencySymbol != null) {
      currency = CurrencyUtils.symbolToCurrencyCode(currencySymbol, context: text);
    }
    
    return {
      'amount': amount,
      'currency': currency,
    };
  }
  
  /// Extract currency information from text
  /// Returns currency code or null if not found
  static String? _extractCurrencyFromText(String text) {
    // Check for currency symbols first with context-aware detection
    final symbolRegex = RegExp(r'(\$|€|£|¥|₹|₽|₩|₱|₫|฿|₺|₪|R\$|S\$|HK\$|A\$|C\$|NZ\$)');
    final symbolMatch = symbolRegex.firstMatch(text);
    if (symbolMatch != null) {
      return CurrencyUtils.symbolToCurrencyCode(symbolMatch.group(1)!, context: text);
    }
    
    // Check for currency codes
    final codeRegex = RegExp(r'\b(USD|EUR|GBP|JPY|CNY|INR|KRW|MXN|PHP|VND|THB|TRY|ILS|BRL|SGD|HKD|AUD|CAD|NZD|RUB)\b', caseSensitive: false);
    final codeMatch = codeRegex.firstMatch(text);
    if (codeMatch != null) {
      return codeMatch.group(1)!.toUpperCase();
    }
    
    // Check for currency names
    final nameMap = {
      'dollars?': 'USD',
      'euros?': 'EUR',
      'pounds?': 'GBP',
      'yen': 'JPY',
      'yuan': 'CNY',
      'rupees?': 'INR',
      'won': 'KRW',
      'pesos?': 'MXN',
      'dong': 'VND',
      'baht': 'THB',
      'lira': 'TRY',
      'shekel': 'ILS',
      'reais?': 'BRL',
      'rubles?': 'RUB',
    };
    
    for (final entry in nameMap.entries) {
      if (RegExp(entry.key, caseSensitive: false).hasMatch(text)) {
        return entry.value;
      }
    }
    
    return null;
  }
}
