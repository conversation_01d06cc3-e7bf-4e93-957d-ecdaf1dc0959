import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/transaction_model.dart';
import '../services/storage_service.dart';
import '../utils/currency_utils.dart';
import '../theme.dart';
import 'categories_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  String _currencyCode = "USD";
  late StorageService _storageService;
  
  // Keep this widget alive when it's not visible
  @override
  bool get wantKeepAlive => true;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
    _animationController.forward();
    _initializeStorageService();
  }

  Future<void> _initializeStorageService() async {
    _storageService = StorageService();
    await _storageService.init();
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    final currencyCode = await _storageService.getDefaultCurrency();
    setState(() {
      _currencyCode = currencyCode;
    });
  }

  Future<void> _setCurrency(String currencyCode) async {
    await _storageService.saveDefaultCurrency(currencyCode);
    setState(() {
      _currencyCode = currencyCode;
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _clearAllData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear All Data'),
        content: const Text(
          'This will permanently delete all your transactions and settings. '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('CANCEL'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('DELETE'),
          ),
        ],
      ),
    ) ?? false;

    if (confirmed) {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();
      
      // Reset the provider
      if (mounted) {
        final provider = Provider.of<TransactionProvider>(context, listen: false);
        // This will reload default categories but clear all transactions
        await provider.clearAllData();
        
        // Show confirmation
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('All data has been cleared')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Call super.build to properly handle keep alive
    super.build(context);
    
    final theme = Theme.of(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return Scaffold(
      appBar: AppBar(
        title: const Row(
          children: [
            Icon(Icons.settings),
            SizedBox(width: 8),
            Text('Settings'),
          ],
        ),
      ),
      body: FadeTransition(
        opacity: _animation,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // Appearance Section
            _buildSectionHeader('Appearance', Icons.palette),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Theme',
              description: 'Switch between light and dark mode',
              icon: Icons.brightness_6,
              trailing: Switch(
                value: themeProvider.isDarkMode,
                onChanged: (value) {
                  themeProvider.toggleTheme();
                },
                activeColor: theme.colorScheme.primary,
              ),
            ),

            const SizedBox(height: 24),
            
            // Preferences Section
            _buildSectionHeader('Preferences', Icons.tune),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Currency',
              description: 'Select your preferred currency',
              icon: Icons.attach_money,
              onTap: () => _showCurrencyPicker(),
              trailing: Text(
                _getCurrencyDisplayName(_currencyCode),
                style: TextStyle(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Categories',
              description: 'Manage transaction categories',
              icon: Icons.category,
              onTap: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const CategoriesScreen(),
                ),
              ),
            ),

            const SizedBox(height: 24),
            
            // Data Management Section
            _buildSectionHeader('Data Management', Icons.storage),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Clear All Data',
              description: 'Delete all transactions and settings',
              icon: Icons.delete_forever,
              iconColor: Colors.red,
              onTap: _clearAllData,
            ),

            const SizedBox(height: 24),
            
            // About Section
            _buildSectionHeader('About', Icons.info_outline),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'App Version',
              description: 'Money Lover Chat v1.0.0',
              icon: Icons.new_releases,
            ),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Privacy Policy',
              description: 'Read our privacy policy',
              icon: Icons.privacy_tip,
              onTap: () {
                // Show privacy policy dialog
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Privacy Policy'),
                    content: const SingleChildScrollView(
                      child: Text(
                        'This app processes and stores all data locally on your device. '
                        'No personal data is sent to external servers unless you explicitly '
                        'choose to use the AI parsing feature, which would send your message '
                        'text to our servers for processing.\n\n'
                        'We do not collect any user analytics or tracking information.'
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('CLOSE'),
                      ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(height: 8),
            _buildSettingCard(
              title: 'Terms of Service',
              description: 'Read our terms of service',
              icon: Icons.gavel,
              onTap: () {
                // Show terms dialog
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('Terms of Service'),
                    content: const SingleChildScrollView(
                      child: Text(
                        'By using this app, you agree to the following terms:\n\n'
                        '1. The app is provided "as is" without warranty of any kind.\n'
                        '2. Your data is stored locally on your device.\n'
                        '3. You are responsible for managing your own financial data.\n'
                        '4. We reserve the right to modify the app features at any time.'
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('CLOSE'),
                      ),
                    ],
                  ),
                );
              },
            ),
            
            const SizedBox(height: 32),
            
            // Footer
            Center(
              child: Text(
                'Made with ❤️ for personal finance',
                style: TextStyle(
                  fontSize: 12,
                  color: theme.brightness == Brightness.light 
                      ? Colors.black54 
                      : Colors.grey.shade300,
                ),
              ),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    final theme = Theme.of(context);
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          title.toUpperCase(),
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            letterSpacing: 1.2,
            color: theme.colorScheme.primary,
          ),
        ),
      ],
    );
  }

  Widget _buildSettingCard({
    required String title,
    required String description,
    required IconData icon,
    Color? iconColor,
    Widget? trailing,
    VoidCallback? onTap,
  }) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: theme.colorScheme.surface,
      margin: EdgeInsets.zero,
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: (iconColor ?? theme.colorScheme.primary).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(
                  icon,
                  color: iconColor ?? theme.colorScheme.primary,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 14,
                        color: theme.brightness == Brightness.light 
                            ? Colors.black54 
                            : Colors.grey.shade400,
                      ),
                    ),
                  ],
                ),
              ),
              if (trailing != null) trailing,
              if (onTap != null && trailing == null)
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: theme.brightness == Brightness.light 
                      ? Colors.black38 
                      : Colors.grey.shade400,
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showCurrencyPicker() {
    final currencies = CurrencyUtils.supportedCurrencies;
    
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const SizedBox(height: 16),
            Center(
              child: Container(
                width: 50,
                height: 5,
                decoration: BoxDecoration(
                  color: Colors.grey.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 16),
              child: Text(
                'Select Currency',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 8),
            Expanded(
              child: ListView.builder(
                itemCount: currencies.length,
                itemBuilder: (context, index) {
                  final currency = currencies[index];
                  final isSelected = currency['code'] == _currencyCode;
                  
                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    color: isSelected 
                        ? Theme.of(context).colorScheme.primary.withOpacity(0.1) 
                        : Colors.transparent,
                    child: ListTile(
                      leading: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: Text(
                            currency['symbol']!,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                        ),
                      ),
                      title: Text('${currency['code']} (${currency['name']})'),
                      trailing: isSelected 
                          ? Icon(
                              Icons.check_circle,
                              color: Theme.of(context).colorScheme.primary,
                            )
                          : null,
                      onTap: () {
                        _setCurrency(currency['code']!);
                        Navigator.of(context).pop();
                      },
                    ),
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  String _getCurrencyDisplayName(String currencyCode) {
    final symbol = CurrencyUtils.getCurrencySymbol(currencyCode);
    final name = CurrencyUtils.getCurrencyName(currencyCode);
    return '$symbol ($name)';
  }
}