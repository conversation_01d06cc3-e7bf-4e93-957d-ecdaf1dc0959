import 'dart:developer' as developer;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'services/storage_service.dart';
import 'services/parser/transaction_parsing_service.dart';
import 'services/error_handling_service.dart';
import 'services/logging_service.dart';
import 'navigation/app_navigation.dart';
import 'theme.dart';
import 'utils/startup_timer.dart';
import 'models/transaction_model.dart';
import 'widgets/error_widgets.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize logging service first
  await LoggingService.instance.initialize();

  // Initialize global error handling
  ErrorHandlingService.initialize();

  // Start performance measurement
  final timer = StartupTimer.instance;
  timer.mark('app-start');

  // Initialize storage service (fast, essential)
  final storageService = StorageService();
  await storageService.init();
  timer.mark('storage-ready');

  // Create a provider for ML Kit service that will be populated later
  final mlKitProvider = ValueNotifier<TransactionParsingService?>(null);

  runApp(ErrorBoundary(
    onError: (error, stackTrace) {
      ErrorHandlingService.instance.handleSpecificError(
        error: error,
        context: 'App Root',
        stackTrace: stackTrace,
      );
    },
    child: MultiProvider(
      providers: [
        // Provide nullable ML Kit service initially
        ChangeNotifierProvider<ValueNotifier<TransactionParsingService?>>.value(value: mlKitProvider),
        // Provide theme provider for light/dark mode switching
        ChangeNotifierProvider(create: (_) => ThemeProvider()),
        // Provide transaction provider for managing transactions and messages
        ChangeNotifierProvider(create: (_) => TransactionProvider(storageService)),
      ],
      child: const MoneyLoverChatApp(),
    ),
  ));

  timer.mark('ui-shown');

  // Initialize ML Kit in background after first frame with additional delay
  WidgetsBinding.instance.addPostFrameCallback((_) {
    // Add a small delay to ensure UI is fully rendered before starting heavy initialization
    Future.delayed(const Duration(milliseconds: 100), () {
      _initializeMlKitInBackground(storageService, mlKitProvider, timer);
    });
  });
}

/// Initialize ML Kit service in background after UI is shown
Future<void> _initializeMlKitInBackground(
  StorageService storageService,
  ValueNotifier<TransactionParsingService?> mlKitProvider,
  StartupTimer timer,
) async {
  try {
    developer.log('Starting background ML Kit initialization', name: 'StartupPerformance');

    // Initialize ML Kit service
    final mlKitService = await TransactionParsingService.getInstance(storageService);

    // Update the provider with the initialized service
    mlKitProvider.value = mlKitService;

    timer.mark('mlkit-ready');
    timer.measure('ui-shown', 'mlkit-ready');

    developer.log('ML Kit initialized successfully in background', name: 'StartupPerformance');

    // Print performance summary
    timer.printSummary();

  } catch (e) {
    developer.log('Background ML Kit initialization failed: $e', name: 'StartupPerformance');
    // ML Kit failure is not critical - app will continue with fallback parsing
    timer.mark('mlkit-failed');
  }
}

class MoneyLoverChatApp extends StatelessWidget {
  const MoneyLoverChatApp({super.key});

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context);
    
    return MaterialApp(
      title: 'Money Lover Chat',
      theme: themeProvider.themeData,
      debugShowCheckedModeBanner: false,
      home: const AppNavigation(),
    );
  }
}