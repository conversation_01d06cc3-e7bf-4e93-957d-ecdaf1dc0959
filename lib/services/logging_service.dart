import 'dart:async';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:logger/logger.dart';

/// Comprehensive logging service with Firebase Crashlytics integration
class LoggingService {
  static LoggingService? _instance;
  static LoggingService get instance => _instance ??= LoggingService._();
  
  LoggingService._();

  late Logger _logger;
  bool _isInitialized = false;
  bool _crashlyticsEnabled = false;

  /// Initialize the logging service
  Future<void> initialize() async {
    if (_isInitialized) return;

    // Initialize local logger
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 2,
        errorMethodCount: 8,
        lineLength: 120,
        colors: true,
        printEmojis: true,
        printTime: true,
      ),
      level: kDebugMode ? Level.debug : Level.info,
    );

    // Initialize Firebase Crashlytics if available
    try {
      await _initializeCrashlytics();
    } catch (e) {
      _logger.w('Failed to initialize Crashlytics: $e');
    }

    _isInitialized = true;
    _logger.i('LoggingService initialized successfully');
  }

  /// Initialize Firebase Crashlytics
  Future<void> _initializeCrashlytics() async {
    try {
      // Initialize Firebase if not already done
      if (Firebase.apps.isEmpty) {
        await Firebase.initializeApp();
      }

      // Enable Crashlytics collection
      await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
      
      // Set up Crashlytics error handling
      FlutterError.onError = (errorDetails) {
        FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
      };

      // Pass all uncaught asynchronous errors to Crashlytics
      PlatformDispatcher.instance.onError = (error, stack) {
        FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
        return true;
      };

      _crashlyticsEnabled = true;
      _logger.i('Firebase Crashlytics initialized successfully');
    } catch (e) {
      _crashlyticsEnabled = false;
      _logger.w('Failed to initialize Firebase Crashlytics: $e');
      // Continue without Crashlytics - local logging will still work
    }
  }

  /// Log debug message
  void debug(String message, {String? tag, Map<String, dynamic>? metadata}) {
    _ensureInitialized();
    _logger.d('[$tag] $message');
    developer.log(message, name: tag ?? 'Debug');
    
    if (_crashlyticsEnabled && metadata != null) {
      _setCustomKeys(metadata);
    }
  }

  /// Log info message
  void info(String message, {String? tag, Map<String, dynamic>? metadata}) {
    _ensureInitialized();
    _logger.i('[$tag] $message');
    developer.log(message, name: tag ?? 'Info');
    
    if (_crashlyticsEnabled && metadata != null) {
      _setCustomKeys(metadata);
    }
  }

  /// Log warning message
  void warning(String message, {String? tag, Map<String, dynamic>? metadata}) {
    _ensureInitialized();
    _logger.w('[$tag] $message');
    developer.log(message, name: tag ?? 'Warning');
    
    if (_crashlyticsEnabled) {
      FirebaseCrashlytics.instance.log('WARNING: [$tag] $message');
      if (metadata != null) {
        _setCustomKeys(metadata);
      }
    }
  }

  /// Log error message
  void error(
    String message, {
    Object? error,
    StackTrace? stackTrace,
    String? tag,
    Map<String, dynamic>? metadata,
    bool fatal = false,
  }) {
    _ensureInitialized();
    _logger.e('[$tag] $message', error: error, stackTrace: stackTrace);
    developer.log(
      message,
      name: tag ?? 'Error',
      error: error,
      stackTrace: stackTrace,
    );
    
    if (_crashlyticsEnabled) {
      if (metadata != null) {
        _setCustomKeys(metadata);
      }
      
      if (error != null) {
        FirebaseCrashlytics.instance.recordError(
          error,
          stackTrace,
          fatal: fatal,
          information: [message],
        );
      } else {
        FirebaseCrashlytics.instance.log('ERROR: [$tag] $message');
      }
    }
  }

  /// Log fatal error
  void fatal(
    String message, {
    required Object errorObject,
    StackTrace? stackTrace,
    String? tag,
    Map<String, dynamic>? metadata,
  }) {
    error(
      message,
      error: errorObject,
      stackTrace: stackTrace,
      tag: tag,
      metadata: metadata,
      fatal: true,
    );
  }

  /// Set user identifier for crash reports
  void setUserId(String userId) {
    if (_crashlyticsEnabled) {
      FirebaseCrashlytics.instance.setUserIdentifier(userId);
    }
  }

  /// Set custom key-value pairs for crash reports
  void setCustomKey(String key, dynamic value) {
    if (_crashlyticsEnabled) {
      FirebaseCrashlytics.instance.setCustomKey(key, value);
    }
  }

  /// Set multiple custom keys
  void _setCustomKeys(Map<String, dynamic> metadata) {
    if (_crashlyticsEnabled) {
      for (final entry in metadata.entries) {
        FirebaseCrashlytics.instance.setCustomKey(entry.key, entry.value);
      }
    }
  }

  /// Log breadcrumb for debugging
  void breadcrumb(String message, {String? category, Map<String, dynamic>? data}) {
    _ensureInitialized();
    final breadcrumbMessage = '🍞 [$category] $message';
    _logger.d(breadcrumbMessage);
    
    if (_crashlyticsEnabled) {
      FirebaseCrashlytics.instance.log(breadcrumbMessage);
    }
  }

  /// Log performance metric
  void performance(String operation, Duration duration, {Map<String, dynamic>? metadata}) {
    _ensureInitialized();
    final message = '⏱️ $operation took ${duration.inMilliseconds}ms';
    _logger.i(message);
    
    if (_crashlyticsEnabled) {
      FirebaseCrashlytics.instance.log(message);
      setCustomKey('${operation}_duration_ms', duration.inMilliseconds);
      if (metadata != null) {
        _setCustomKeys(metadata);
      }
    }
  }

  /// Log network request
  void networkRequest({
    required String method,
    required String url,
    int? statusCode,
    Duration? duration,
    String? error,
  }) {
    _ensureInitialized();
    final message = '🌐 $method $url ${statusCode ?? 'FAILED'} ${duration?.inMilliseconds ?? 0}ms';
    
    if (error != null) {
      _logger.e(message, error: error);
    } else {
      _logger.d(message);
    }
    
    if (_crashlyticsEnabled) {
      FirebaseCrashlytics.instance.log(message);
      setCustomKey('last_network_request', '$method $url');
      if (statusCode != null) {
        setCustomKey('last_network_status', statusCode);
      }
    }
  }

  /// Force crash for testing (debug only)
  void testCrash() {
    if (kDebugMode && _crashlyticsEnabled) {
      FirebaseCrashlytics.instance.crash();
    }
  }

  /// Check if Crashlytics is enabled
  bool get isCrashlyticsEnabled => _crashlyticsEnabled;

  /// Ensure the service is initialized
  void _ensureInitialized() {
    if (!_isInitialized) {
      // Fallback to basic logging if not initialized
      print('LoggingService not initialized, using fallback logging');
    }
  }

  /// Dispose resources
  void dispose() {
    _logger.close();
    _isInitialized = false;
  }
}
