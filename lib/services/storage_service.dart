import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'error_handling_service.dart';

class StorageService {
  SharedPreferences? _prefs;
  bool _isInFallbackMode = false;
  final Map<String, dynamic> _fallbackStorage = {};

  bool get isInFallbackMode => _isInFallbackMode;

  Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _isInFallbackMode = false;
      if (kDebugMode) {
        print('StorageService: SharedPreferences initialized successfully');
      }
    } catch (e) {
      _handleStorageError(e, 'initialization');
      _isInFallbackMode = true;
      if (kDebugMode) {
        print('StorageService: Failed to initialize SharedPreferences, using in-memory fallback');
      }
    }
  }

  void _handleStorageError(Object error, String operation) {
    ErrorHandlingService.instance.handleSpecificError(
      error: error,
      context: 'StorageService.$operation',
      metadata: {'operation': operation, 'fallbackMode': _isInFallbackMode},
    );
  }

  // String operations with fallback
  Future<bool> setString(String key, String value) async {
    if (_isInFallbackMode || _prefs == null) {
      _fallbackStorage[key] = value;
      return true;
    }

    try {
      return await _prefs!.setString(key, value);
    } catch (e) {
      _handleStorageError(e, 'setString');
      _fallbackStorage[key] = value;
      return false;
    }
  }

  String? getString(String key) {
    if (_isInFallbackMode || _prefs == null) {
      return _fallbackStorage[key] as String?;
    }

    try {
      return _prefs!.getString(key);
    } catch (e) {
      _handleStorageError(e, 'getString');
      return _fallbackStorage[key] as String?;
    }
  }

  // String list operations with fallback
  Future<bool> setStringList(String key, List<String> value) async {
    if (_isInFallbackMode || _prefs == null) {
      _fallbackStorage[key] = List<String>.from(value);
      return true;
    }

    try {
      return await _prefs!.setStringList(key, value);
    } catch (e) {
      _handleStorageError(e, 'setStringList');
      _fallbackStorage[key] = List<String>.from(value);
      return false;
    }
  }

  List<String>? getStringList(String key) {
    if (_isInFallbackMode || _prefs == null) {
      final value = _fallbackStorage[key];
      return value is List<String> ? value : null;
    }

    try {
      return _prefs!.getStringList(key);
    } catch (e) {
      _handleStorageError(e, 'getStringList');
      final value = _fallbackStorage[key];
      return value is List<String> ? value : null;
    }
  }

  // Bool operations with fallback
  Future<bool> setBool(String key, bool value) async {
    if (_isInFallbackMode || _prefs == null) {
      _fallbackStorage[key] = value;
      return true;
    }

    try {
      return await _prefs!.setBool(key, value);
    } catch (e) {
      _handleStorageError(e, 'setBool');
      _fallbackStorage[key] = value;
      return false;
    }
  }

  bool? getBool(String key) {
    if (_isInFallbackMode || _prefs == null) {
      return _fallbackStorage[key] as bool?;
    }

    try {
      return _prefs!.getBool(key);
    } catch (e) {
      _handleStorageError(e, 'getBool');
      return _fallbackStorage[key] as bool?;
    }
  }

  // Int operations with fallback
  Future<bool> setInt(String key, int value) async {
    if (_isInFallbackMode || _prefs == null) {
      _fallbackStorage[key] = value;
      return true;
    }

    try {
      return await _prefs!.setInt(key, value);
    } catch (e) {
      _handleStorageError(e, 'setInt');
      _fallbackStorage[key] = value;
      return false;
    }
  }

  int? getInt(String key) {
    if (_isInFallbackMode || _prefs == null) {
      return _fallbackStorage[key] as int?;
    }

    try {
      return _prefs!.getInt(key);
    } catch (e) {
      _handleStorageError(e, 'getInt');
      return _fallbackStorage[key] as int?;
    }
  }

  // Double operations with fallback
  Future<bool> setDouble(String key, double value) async {
    if (_isInFallbackMode || _prefs == null) {
      _fallbackStorage[key] = value;
      return true;
    }

    try {
      return await _prefs!.setDouble(key, value);
    } catch (e) {
      _handleStorageError(e, 'setDouble');
      _fallbackStorage[key] = value;
      return false;
    }
  }

  double? getDouble(String key) {
    if (_isInFallbackMode || _prefs == null) {
      return _fallbackStorage[key] as double?;
    }

    try {
      return _prefs!.getDouble(key);
    } catch (e) {
      _handleStorageError(e, 'getDouble');
      return _fallbackStorage[key] as double?;
    }
  }

  // Remove a specific key with fallback
  Future<bool> remove(String key) async {
    if (_isInFallbackMode || _prefs == null) {
      _fallbackStorage.remove(key);
      return true;
    }

    try {
      final result = await _prefs!.remove(key);
      _fallbackStorage.remove(key); // Also remove from fallback
      return result;
    } catch (e) {
      _handleStorageError(e, 'remove');
      _fallbackStorage.remove(key);
      return false;
    }
  }

  // Clear all data with fallback
  Future<bool> clear() async {
    if (_isInFallbackMode || _prefs == null) {
      _fallbackStorage.clear();
      return true;
    }

    try {
      final result = await _prefs!.clear();
      _fallbackStorage.clear(); // Also clear fallback
      return result;
    } catch (e) {
      _handleStorageError(e, 'clear');
      _fallbackStorage.clear();
      return false;
    }
  }

  // Check if a key exists with fallback
  bool containsKey(String key) {
    if (_isInFallbackMode || _prefs == null) {
      return _fallbackStorage.containsKey(key);
    }

    try {
      return _prefs!.containsKey(key);
    } catch (e) {
      _handleStorageError(e, 'containsKey');
      return _fallbackStorage.containsKey(key);
    }
  }

  // Default currency operations
  Future<void> saveDefaultCurrency(String currencyCode) async {
    await setString('default_currency', currencyCode);
  }

  Future<String> getDefaultCurrency() async {
    return getString('default_currency') ?? 'USD';
  }
}