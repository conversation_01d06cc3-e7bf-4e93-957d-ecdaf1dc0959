import 'dart:async';
import 'dart:developer' as developer;
import 'dart:io';
import 'dart:isolate';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'logging_service.dart';

/// Comprehensive error handling service for production deployment
class ErrorHandlingService {
  static ErrorHandlingService? _instance;
  static ErrorHandlingService get instance => _instance ??= ErrorHandlingService._();
  
  ErrorHandlingService._();

  /// Initialize global error handlers
  static void initialize() {
    final service = ErrorHandlingService.instance;
    
    // Handle Flutter framework errors
    FlutterError.onError = service._handleFlutterError;
    
    // Handle platform/Dart errors
    PlatformDispatcher.instance.onError = service._handlePlatformError;
    
    // Handle async errors in isolates
    Isolate.current.addErrorListener(
      RawReceivePort((pair) async {
        final List<dynamic> errorAndStacktrace = pair;
        await service._handleIsolateError(
          errorAndStacktrace.first,
          errorAndStacktrace.length > 1 ? errorAndStacktrace.last : null,
        );
      }).sendPort,
    );

    developer.log('Global error handlers initialized', name: 'ErrorHandling');
  }

  /// Handle Flutter framework errors
  void _handleFlutterError(FlutterErrorDetails details) {
    // Log to console in debug mode
    if (kDebugMode) {
      FlutterError.presentError(details);
    }

    // Log error details
    _logError(
      error: details.exception,
      stackTrace: details.stack,
      context: 'Flutter Framework',
      library: details.library,
      additionalInfo: {
        'context': details.context?.toString(),
        'informationCollector': details.informationCollector?.toString(),
      },
    );

    // Report to crash analytics (will be implemented later)
    _reportToCrashlytics(details.exception, details.stack, {
      'type': 'flutter_error',
      'library': details.library,
      'context': details.context?.toString(),
    });
  }

  /// Handle platform and Dart errors
  bool _handlePlatformError(Object error, StackTrace stackTrace) {
    _logError(
      error: error,
      stackTrace: stackTrace,
      context: 'Platform/Dart',
      additionalInfo: {
        'platform': Platform.operatingSystem,
        'version': Platform.operatingSystemVersion,
      },
    );

    // Report to crash analytics
    _reportToCrashlytics(error, stackTrace, {
      'type': 'platform_error',
      'platform': Platform.operatingSystem,
    });

    // Return true to prevent the error from being handled by the default handler
    return true;
  }

  /// Handle isolate errors
  Future<void> _handleIsolateError(dynamic error, dynamic stackTrace) async {
    _logError(
      error: error,
      stackTrace: stackTrace is StackTrace ? stackTrace : null,
      context: 'Isolate',
      additionalInfo: {
        'isolate': Isolate.current.debugName,
      },
    );

    // Report to crash analytics
    _reportToCrashlytics(error, stackTrace, {
      'type': 'isolate_error',
      'isolate': Isolate.current.debugName,
    });
  }

  /// Log error with comprehensive details
  void _logError({
    required Object error,
    StackTrace? stackTrace,
    required String context,
    String? library,
    Map<String, dynamic>? additionalInfo,
  }) {
    final timestamp = DateTime.now().toIso8601String();
    final errorType = error.runtimeType.toString();
    
    developer.log(
      'ERROR CAPTURED',
      name: 'ErrorHandling',
      error: error,
      stackTrace: stackTrace,
      time: DateTime.now(),
    );

    // Detailed error information
    final errorDetails = {
      'timestamp': timestamp,
      'context': context,
      'library': library,
      'errorType': errorType,
      'message': error.toString(),
      'stackTrace': stackTrace?.toString(),
      ...?additionalInfo,
    };

    // Log to console with formatting
    print('🚨 ERROR CAPTURED 🚨');
    print('Context: $context');
    print('Type: $errorType');
    print('Message: $error');
    if (library != null) print('Library: $library');
    if (stackTrace != null) {
      print('Stack Trace:');
      print(stackTrace.toString());
    }
    if (additionalInfo != null) {
      print('Additional Info: $additionalInfo');
    }
    print('─' * 50);
  }

  /// Report error to crash analytics service
  void _reportToCrashlytics(Object error, StackTrace? stackTrace, Map<String, dynamic> metadata) {
    try {
      LoggingService.instance.error(
        'Error reported to crashlytics',
        error: error,
        stackTrace: stackTrace,
        tag: 'ErrorHandling',
        metadata: metadata,
        fatal: metadata['type'] == 'flutter_error' || metadata['type'] == 'platform_error',
      );
    } catch (e) {
      // Fallback to developer log if logging service fails
      developer.log(
        'Failed to report to logging service: $e. Original error: ${error.runtimeType} - ${error.toString()}',
        name: 'ErrorHandling',
      );
    }
  }

  /// Handle specific error types with custom logic
  void handleSpecificError({
    required Object error,
    required String context,
    StackTrace? stackTrace,
    Map<String, dynamic>? metadata,
    VoidCallback? onRetry,
  }) {
    _logError(
      error: error,
      stackTrace: stackTrace,
      context: context,
      additionalInfo: metadata,
    );

    // Categorize error and handle accordingly
    if (error is PlatformException) {
      _handlePlatformException(error, context, onRetry);
    } else if (error is SocketException) {
      _handleNetworkError(error, context, onRetry);
    } else if (error is TimeoutException) {
      _handleTimeoutError(error, context, onRetry);
    } else if (error is FormatException) {
      _handleDataFormatError(error, context);
    } else {
      _handleGenericError(error, context, onRetry);
    }

    // Report to analytics
    _reportToCrashlytics(error, stackTrace, {
      'context': context,
      'handled': true,
      ...?metadata,
    });
  }

  void _handlePlatformException(PlatformException error, String context, VoidCallback? onRetry) {
    developer.log('Platform exception in $context: ${error.code} - ${error.message}', name: 'ErrorHandling');
  }

  void _handleNetworkError(SocketException error, String context, VoidCallback? onRetry) {
    developer.log('Network error in $context: ${error.message}', name: 'ErrorHandling');
  }

  void _handleTimeoutError(TimeoutException error, String context, VoidCallback? onRetry) {
    developer.log('Timeout error in $context: ${error.message}', name: 'ErrorHandling');
  }

  void _handleDataFormatError(FormatException error, String context) {
    developer.log('Data format error in $context: ${error.message}', name: 'ErrorHandling');
  }

  void _handleGenericError(Object error, String context, VoidCallback? onRetry) {
    developer.log('Generic error in $context: ${error.toString()}', name: 'ErrorHandling');
  }

  /// Check if error is recoverable
  bool isRecoverableError(Object error) {
    return error is SocketException ||
           error is TimeoutException ||
           error is HttpException ||
           (error is PlatformException && error.code != 'PERMISSION_DENIED');
  }

  /// Get user-friendly error message
  String getUserFriendlyMessage(Object error) {
    if (error is SocketException) {
      return 'Network connection error. Please check your internet connection and try again.';
    } else if (error is TimeoutException) {
      return 'The operation timed out. Please try again.';
    } else if (error is PlatformException) {
      switch (error.code) {
        case 'PERMISSION_DENIED':
          return 'Permission denied. Please check app permissions.';
        case 'NOT_AVAILABLE':
          return 'This feature is not available on your device.';
        default:
          return 'An error occurred: ${error.message ?? 'Unknown error'}';
      }
    } else if (error is FormatException) {
      return 'Data format error. Please try again or contact support.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }
}
