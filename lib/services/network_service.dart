import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'error_handling_service.dart';

/// Network service with comprehensive error handling and retry mechanisms
class NetworkService {
  static NetworkService? _instance;
  static NetworkService get instance => _instance ??= NetworkService._();
  
  NetworkService._();

  static const Duration _defaultTimeout = Duration(seconds: 30);
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 2);

  /// Make a GET request with error handling and retry logic
  Future<NetworkResult<T>> get<T>({
    required String url,
    Map<String, String>? headers,
    Duration? timeout,
    int? maxRetries,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    return _makeRequest<T>(
      method: 'GET',
      url: url,
      headers: headers,
      timeout: timeout,
      maxRetries: maxRetries,
      fromJson: from<PERSON>son,
    );
  }

  /// Make a POST request with error handling and retry logic
  Future<NetworkResult<T>> post<T>({
    required String url,
    Map<String, String>? headers,
    Object? body,
    Duration? timeout,
    int? maxRetries,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    return _makeRequest<T>(
      method: 'POST',
      url: url,
      headers: headers,
      body: body,
      timeout: timeout,
      maxRetries: maxRetries,
      fromJson: fromJson,
    );
  }

  /// Make a PUT request with error handling and retry logic
  Future<NetworkResult<T>> put<T>({
    required String url,
    Map<String, String>? headers,
    Object? body,
    Duration? timeout,
    int? maxRetries,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    return _makeRequest<T>(
      method: 'PUT',
      url: url,
      headers: headers,
      body: body,
      timeout: timeout,
      maxRetries: maxRetries,
      fromJson: fromJson,
    );
  }

  /// Make a DELETE request with error handling and retry logic
  Future<NetworkResult<T>> delete<T>({
    required String url,
    Map<String, String>? headers,
    Duration? timeout,
    int? maxRetries,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    return _makeRequest<T>(
      method: 'DELETE',
      url: url,
      headers: headers,
      timeout: timeout,
      maxRetries: maxRetries,
      fromJson: fromJson,
    );
  }

  /// Core method for making HTTP requests with comprehensive error handling
  Future<NetworkResult<T>> _makeRequest<T>({
    required String method,
    required String url,
    Map<String, String>? headers,
    Object? body,
    Duration? timeout,
    int? maxRetries,
    T Function(Map<String, dynamic>)? fromJson,
  }) async {
    final effectiveTimeout = timeout ?? _defaultTimeout;
    final effectiveMaxRetries = maxRetries ?? _maxRetries;
    
    for (int attempt = 0; attempt <= effectiveMaxRetries; attempt++) {
      try {
        final response = await _executeRequest(
          method: method,
          url: url,
          headers: headers,
          body: body,
          timeout: effectiveTimeout,
        );

        if (response.statusCode >= 200 && response.statusCode < 300) {
          // Success
          if (fromJson != null && response.body.isNotEmpty) {
            try {
              final jsonData = jsonDecode(response.body) as Map<String, dynamic>;
              final data = fromJson(jsonData);
              return NetworkResult.success(data);
            } catch (e) {
              return NetworkResult.error(
                NetworkError.parseError('Failed to parse response: $e'),
              );
            }
          } else {
            return NetworkResult.success(null);
          }
        } else {
          // HTTP error
          final error = _createHttpError(response);
          if (attempt < effectiveMaxRetries && _shouldRetry(response.statusCode)) {
            await Future.delayed(_retryDelay * (attempt + 1));
            continue;
          }
          return NetworkResult.error(error);
        }
      } catch (e) {
        final error = _categorizeError(e);
        
        // Log the error
        ErrorHandlingService.instance.handleSpecificError(
          error: e,
          context: 'NetworkService._makeRequest',
          metadata: {
            'method': method,
            'url': url,
            'attempt': attempt + 1,
            'maxRetries': effectiveMaxRetries,
          },
        );

        if (attempt < effectiveMaxRetries && error.isRetryable) {
          if (kDebugMode) {
            print('Network request failed (attempt ${attempt + 1}), retrying in ${_retryDelay.inSeconds}s...');
          }
          await Future.delayed(_retryDelay * (attempt + 1));
          continue;
        }
        
        return NetworkResult.error(error);
      }
    }

    return NetworkResult.error(
      NetworkError.unknown('Max retries exceeded'),
    );
  }

  /// Execute the actual HTTP request
  Future<http.Response> _executeRequest({
    required String method,
    required String url,
    Map<String, String>? headers,
    Object? body,
    required Duration timeout,
  }) async {
    final uri = Uri.parse(url);
    final client = http.Client();
    
    try {
      final defaultHeaders = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...?headers,
      };

      switch (method.toUpperCase()) {
        case 'GET':
          return await client.get(uri, headers: defaultHeaders).timeout(timeout);
        case 'POST':
          return await client.post(
            uri,
            headers: defaultHeaders,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(timeout);
        case 'PUT':
          return await client.put(
            uri,
            headers: defaultHeaders,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(timeout);
        case 'DELETE':
          return await client.delete(uri, headers: defaultHeaders).timeout(timeout);
        default:
          throw ArgumentError('Unsupported HTTP method: $method');
      }
    } finally {
      client.close();
    }
  }

  /// Categorize errors for appropriate handling
  NetworkError _categorizeError(Object error) {
    if (error is SocketException) {
      return NetworkError.connectionError(error.message);
    } else if (error is TimeoutException) {
      return NetworkError.timeoutError('Request timed out');
    } else if (error is HttpException) {
      return NetworkError.httpError(error.message, null);
    } else if (error is FormatException) {
      return NetworkError.parseError('Invalid response format');
    } else {
      return NetworkError.unknown(error.toString());
    }
  }

  /// Create HTTP error from response
  NetworkError _createHttpError(http.Response response) {
    final statusCode = response.statusCode;
    String message;

    if (statusCode >= 400 && statusCode < 500) {
      message = 'Client error: ${response.reasonPhrase}';
    } else if (statusCode >= 500) {
      message = 'Server error: ${response.reasonPhrase}';
    } else {
      message = 'HTTP error: ${response.reasonPhrase}';
    }

    return NetworkError.httpError(message, statusCode);
  }

  /// Determine if a status code should trigger a retry
  bool _shouldRetry(int statusCode) {
    return statusCode >= 500 || // Server errors
           statusCode == 408 || // Request timeout
           statusCode == 429;   // Too many requests
  }

  /// Check network connectivity
  Future<bool> isConnected() async {
    try {
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}

/// Result wrapper for network operations
class NetworkResult<T> {
  final T? data;
  final NetworkError? error;
  final bool isSuccess;

  const NetworkResult._({
    this.data,
    this.error,
    required this.isSuccess,
  });

  factory NetworkResult.success(T? data) {
    return NetworkResult._(data: data, isSuccess: true);
  }

  factory NetworkResult.error(NetworkError error) {
    return NetworkResult._(error: error, isSuccess: false);
  }

  /// Get data or throw error
  T get dataOrThrow {
    if (isSuccess) {
      return data as T;
    } else {
      throw error!;
    }
  }
}

/// Network error types and information
class NetworkError implements Exception {
  final NetworkErrorType type;
  final String message;
  final int? statusCode;
  final bool isRetryable;

  const NetworkError._({
    required this.type,
    required this.message,
    this.statusCode,
    required this.isRetryable,
  });

  factory NetworkError.connectionError(String message) {
    return NetworkError._(
      type: NetworkErrorType.connection,
      message: message,
      isRetryable: true,
    );
  }

  factory NetworkError.timeoutError(String message) {
    return NetworkError._(
      type: NetworkErrorType.timeout,
      message: message,
      isRetryable: true,
    );
  }

  factory NetworkError.httpError(String message, int? statusCode) {
    return NetworkError._(
      type: NetworkErrorType.http,
      message: message,
      statusCode: statusCode,
      isRetryable: statusCode != null && (statusCode >= 500 || statusCode == 408 || statusCode == 429),
    );
  }

  factory NetworkError.parseError(String message) {
    return NetworkError._(
      type: NetworkErrorType.parse,
      message: message,
      isRetryable: false,
    );
  }

  factory NetworkError.unknown(String message) {
    return NetworkError._(
      type: NetworkErrorType.unknown,
      message: message,
      isRetryable: true,
    );
  }

  @override
  String toString() {
    return 'NetworkError(type: $type, message: $message, statusCode: $statusCode)';
  }
}

/// Types of network errors
enum NetworkErrorType {
  connection,
  timeout,
  http,
  parse,
  unknown,
}
