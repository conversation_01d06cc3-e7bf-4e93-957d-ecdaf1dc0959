import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import '../models/localization_data.dart';

/// Abstract interface for localization services
abstract class LocalizationServiceInterface {
  Future<LocalizationData> getPatternsForLocale(Locale locale);
  Future<void> preloadLocale(Locale locale);
  void clearCache();
  bool isLocaleCached(Locale locale);
  int get cacheSize;
  List<String> get availableLocales;
}

/// Service for loading and caching localization data for transaction parsing
class LocalizationService implements LocalizationServiceInterface {
  static LocalizationService? _instance;
  static LocalizationService get instance => _instance ??= LocalizationService._();

  LocalizationService._();

  /// Public constructor for testing
  @visibleForTesting
  LocalizationService.forTesting();

  // Cache for loaded localization data
  final Map<String, LocalizationData> _cache = {};

  // Default fallback locale
  static const String _defaultLocale = 'en';

  /// Gets localization patterns for the specified locale
  /// Falls back to English if the requested locale is not available
  Future<LocalizationData> getPatternsForLocale(Locale locale) async {
    final localeKey = _getLocaleKey(locale);
    
    // Check cache first
    if (_cache.containsKey(localeKey)) {
      return _cache[localeKey]!;
    }

    try {
      // Try to load the requested locale
      final data = await _loadLocaleData(localeKey);
      _cache[localeKey] = data;
      return data;
    } catch (e) {
      // If loading fails and it's not the default locale, try fallback
      if (localeKey != _defaultLocale) {
        try {
          final fallbackData = await _loadLocaleData(_defaultLocale);
          _cache[_defaultLocale] = fallbackData;
          return fallbackData;
        } catch (fallbackError) {
          throw Exception('Failed to load localization data for $localeKey and fallback $_defaultLocale: $fallbackError');
        }
      } else {
        throw Exception('Failed to load default localization data: $e');
      }
    }
  }

  /// Preloads localization data for the specified locale for performance optimization
  Future<void> preloadLocale(Locale locale) async {
    final localeKey = _getLocaleKey(locale);
    if (!_cache.containsKey(localeKey)) {
      try {
        await getPatternsForLocale(locale);
      } catch (e) {
        // Preloading failures are not critical, just log them
        print('Warning: Failed to preload locale $localeKey: $e');
      }
    }
  }

  /// Clears the localization cache to free memory
  void clearCache() {
    _cache.clear();
  }

  /// Gets the number of cached locales (for testing/debugging)
  int get cacheSize => _cache.length;

  /// Checks if a locale is cached
  bool isLocaleCached(Locale locale) {
    return _cache.containsKey(_getLocaleKey(locale));
  }

  /// Loads localization data from assets
  Future<LocalizationData> _loadLocaleData(String localeKey) async {
    final assetPath = 'assets/l10n/$localeKey.json';
    
    try {
      final jsonString = await rootBundle.loadString(assetPath);
      return LocalizationData.fromJsonString(jsonString);
    } on FlutterError catch (e) {
      if (e.message.contains('Unable to load asset')) {
        throw Exception('Localization file not found: $assetPath');
      }
      rethrow;
    } catch (e) {
      throw Exception('Failed to parse localization file $assetPath: $e');
    }
  }

  /// Converts a Locale to a cache key
  String _getLocaleKey(Locale locale) {
    // Use language code only for simplicity (e.g., 'en' instead of 'en-US')
    return locale.languageCode;
  }

  /// Gets available locales based on cached data
  List<String> get availableLocales => _cache.keys.toList();

  /// Validates that a LocalizationData object has all required fields
  static bool validateLocalizationData(LocalizationData data) {
    try {
      // Check that all required fields are present and non-empty
      if (data.locale.isEmpty) return false;
      if (data.decimalSeparator.isEmpty) return false;
      if (data.thousandsSeparator.isEmpty) return false;
      if (data.expenseKeywords.isEmpty) return false;
      if (data.incomeKeywords.isEmpty) return false;
      if (data.loanKeywords.isEmpty) return false;
      if (data.currencySymbols.isEmpty) return false;
      
      // Check that keywords don't contain empty strings
      if (data.expenseKeywords.any((k) => k.isEmpty)) return false;
      if (data.incomeKeywords.any((k) => k.isEmpty)) return false;
      if (data.loanKeywords.any((k) => k.isEmpty)) return false;
      if (data.currencySymbols.any((k) => k.isEmpty)) return false;
      
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Creates a test instance for unit testing
  static LocalizationService createTestInstance() {
    return LocalizationService._();
  }

  /// Resets the singleton instance (for testing)
  static void resetInstance() {
    _instance = null;
  }

  /// Sets a test instance as the singleton (for testing)
  static void setTestInstance(LocalizationService testInstance) {
    _instance = testInstance;
  }
}
