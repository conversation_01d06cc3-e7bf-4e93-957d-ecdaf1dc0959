import 'package:flutter/material.dart';
import '../widgets/error_widgets.dart';
import 'error_handling_service.dart';
import 'network_service.dart';
import 'parser/ml_kit_error_handler.dart';

/// Service for showing user-friendly error notifications
class ErrorNotificationService {
  static ErrorNotificationService? _instance;
  static ErrorNotificationService get instance => _instance ??= ErrorNotificationService._();
  
  ErrorNotificationService._();

  /// Show error dialog for critical errors that require user attention
  Future<bool?> showErrorDialog({
    required BuildContext context,
    required Object error,
    String? title,
    VoidCallback? onRetry,
    bool showRetry = true,
  }) async {
    return ErrorDialog.show(
      context: context,
      error: error,
      title: title,
      onRetry: onRetry,
      showRetry: showRetry,
    );
  }

  /// Show error snackbar for non-critical errors
  void showErrorSnackBar({
    required BuildContext context,
    required Object error,
    VoidCallback? onRetry,
    Duration duration = const Duration(seconds: 4),
  }) {
    ErrorSnackBar.show(
      context: context,
      error: error,
      onRetry: onRetry,
      duration: duration,
    );
  }

  /// Show ML Kit specific error notification
  void showMLKitError({
    required BuildContext context,
    required Object error,
    VoidCallback? onRetry,
    bool showDialog = false,
  }) {
    final recoveryResult = MLKitErrorHandler.handleError(error, 'user_notification');
    
    if (showDialog) {
      showErrorDialog(
        context: context,
        error: error,
        title: 'Parsing Service Error',
        onRetry: recoveryResult.shouldRetry ? onRetry : null,
        showRetry: recoveryResult.shouldRetry,
      );
    } else {
      showErrorSnackBar(
        context: context,
        error: error,
        onRetry: recoveryResult.shouldRetry ? onRetry : null,
        duration: const Duration(seconds: 6),
      );
    }
  }

  /// Show network error notification
  void showNetworkError({
    required BuildContext context,
    required NetworkError error,
    VoidCallback? onRetry,
    bool showDialog = false,
  }) {
    if (showDialog) {
      showErrorDialog(
        context: context,
        error: error,
        title: 'Network Error',
        onRetry: error.isRetryable ? onRetry : null,
        showRetry: error.isRetryable,
      );
    } else {
      showErrorSnackBar(
        context: context,
        error: error,
        onRetry: error.isRetryable ? onRetry : null,
        duration: const Duration(seconds: 5),
      );
    }
  }

  /// Show storage error notification
  void showStorageError({
    required BuildContext context,
    required Object error,
    VoidCallback? onRetry,
    bool showDialog = true,
  }) {
    final title = 'Storage Error';
    final isRecoverable = ErrorHandlingService.instance.isRecoverableError(error);
    
    if (showDialog) {
      showErrorDialog(
        context: context,
        error: error,
        title: title,
        onRetry: isRecoverable ? onRetry : null,
        showRetry: isRecoverable,
      );
    } else {
      showErrorSnackBar(
        context: context,
        error: error,
        onRetry: isRecoverable ? onRetry : null,
        duration: const Duration(seconds: 4),
      );
    }
  }

  /// Show provider error notification
  void showProviderError({
    required BuildContext context,
    required Object error,
    String? providerName,
    VoidCallback? onRetry,
    bool showDialog = false,
  }) {
    final title = providerName != null ? '$providerName Error' : 'Provider Error';
    final isRecoverable = ErrorHandlingService.instance.isRecoverableError(error);
    
    if (showDialog) {
      showErrorDialog(
        context: context,
        error: error,
        title: title,
        onRetry: isRecoverable ? onRetry : null,
        showRetry: isRecoverable,
      );
    } else {
      showErrorSnackBar(
        context: context,
        error: error,
        onRetry: isRecoverable ? onRetry : null,
        duration: const Duration(seconds: 4),
      );
    }
  }

  /// Show generic error with automatic categorization
  void showError({
    required BuildContext context,
    required Object error,
    String? title,
    VoidCallback? onRetry,
    bool preferDialog = false,
  }) {
    // Categorize error and show appropriate notification
    if (error is NetworkError) {
      showNetworkError(
        context: context,
        error: error,
        onRetry: onRetry,
        showDialog: preferDialog,
      );
    } else if (error.toString().toLowerCase().contains('mlkit') ||
               error.toString().toLowerCase().contains('entity')) {
      showMLKitError(
        context: context,
        error: error,
        onRetry: onRetry,
        showDialog: preferDialog,
      );
    } else if (error.toString().toLowerCase().contains('storage') ||
               error.toString().toLowerCase().contains('preferences')) {
      showStorageError(
        context: context,
        error: error,
        onRetry: onRetry,
        showDialog: preferDialog,
      );
    } else {
      // Generic error
      if (preferDialog) {
        showErrorDialog(
          context: context,
          error: error,
          title: title ?? 'Error',
          onRetry: onRetry,
        );
      } else {
        showErrorSnackBar(
          context: context,
          error: error,
          onRetry: onRetry,
        );
      }
    }
  }

  /// Show success message
  void showSuccess({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// Show warning message
  void showWarning({
    required BuildContext context,
    required String message,
    VoidCallback? onAction,
    String? actionLabel,
    Duration duration = const Duration(seconds: 4),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: duration,
        action: onAction != null && actionLabel != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: Colors.white,
                onPressed: onAction,
              )
            : null,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// Show info message
  void showInfo({
    required BuildContext context,
    required String message,
    Duration duration = const Duration(seconds: 3),
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}
