import 'dart:ui';
import '../../models/parse_result.dart';

/// A data transfer object for passing information between parsing strategies.
///
/// This class serves as a container for all the data that parsing strategies
/// need to access during the parsing process. It supports extensibility
/// through the [extras] map and allows strategies to store partial results
/// in [interimResult].
///
/// The context is mutable to allow strategies to modify it during processing,
/// enabling communication between strategies in a chain of responsibility.
class ParsingContext {
  /// The user input text to be parsed.
  final String text;

  /// The locale for localization support.
  /// Used by services like FallbackParserService for locale-specific parsing.
  final Locale? locale;

  /// Correlation ID for tracking log messages across the parsing pipeline.
  ///
  /// This optional field is used to correlate log messages from different
  /// strategies and services during a single parse operation. When provided,
  /// all logging should use this ID to enable easy filtering and tracking
  /// of related log entries. Can be null for backward compatibility.
  final String? parseId;

  /// Extensible data map for passing additional information without API changes.
  ///
  /// This allows future strategies to access services, configuration, or
  /// intermediate data without requiring modifications to the ParsingContext
  /// constructor or existing strategy implementations.
  final Map<String, dynamic> extras;

  /// Mutable field for storing partial parsing outcomes.
  /// 
  /// Strategies can use this to store intermediate results that subsequent
  /// strategies in the chain might need to access or modify.
  ParseResult? interimResult;

  /// Creates a new [ParsingContext] with the specified parameters.
  ///
  /// [text] is required and contains the user input to be parsed.
  /// [locale] is optional and provides localization context.
  /// [parseId] is optional and provides correlation ID for log tracking.
  /// [extras] defaults to an empty map if not provided.
  ParsingContext({
    required this.text,
    this.locale,
    this.parseId,
    Map<String, dynamic>? extras,
  }) : extras = extras ?? {};
}
