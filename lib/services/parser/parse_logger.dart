import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

/// Utility class for structured logging with unique correlation ID generation.
/// 
/// This class provides a centralized logging interface for the parsing pipeline
/// that generates unique correlation IDs for each parse operation. All log messages
/// are prefixed with the correlation ID to enable easy filtering and tracking
/// of related log entries across the entire parsing flow.
/// 
/// Usage:
/// ```dart
/// // Start a new parse operation and get correlation ID
/// final parseId = ParseLogger.start("buy coffee 5$");
///
/// // Log messages with correlation ID
/// ParseLogger.i(parseId, "Trying strategy: MlKitStrategy");
/// ParseLogger.d(parseId, "ML Kit found 2 entities");
/// ParseLogger.w(parseId, "Ambiguous amount detected", error);
/// ParseLogger.e(parseId, "Strategy failed", error, stackTrace);
/// ```
///
/// All log messages are formatted as: `[parse:<id>] <message>`
/// This allows easy filtering in logs: `grep "parse:a1b2c3d4" logs.txt`
class ParseLogger {
  static Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: false,
    ),
  );

  static const Uuid _uuid = Uuid();

  /// Starts a new parse operation and generates a unique correlation ID.
  /// 
  /// This method should be called at the beginning of each parse operation
  /// to generate a unique 8-character correlation ID and log the start message.
  /// 
  /// [text] The input text being parsed
  /// 
  /// Returns the generated correlation ID that should be used for all
  /// subsequent log messages related to this parse operation.
  static String start(String text) {
    final id = _uuid.v4().substring(0, 8);
    _logger.i('[parse:$id] START: "$text"');
    return id;
  }

  /// Logs a debug message with the specified correlation ID.
  /// 
  /// Debug messages are used for detailed tracing of the parsing flow
  /// and are typically only visible in debug builds or when debug
  /// logging is explicitly enabled.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The debug message to log
  static void d(String id, String message) {
    if (id.isEmpty) {
      throw ArgumentError('Correlation ID cannot be empty');
    }
    _logger.d('[parse:$id] $message');
  }

  /// Logs an info message with the specified correlation ID.
  /// 
  /// Info messages are used for important events in the parsing flow
  /// such as strategy selection, successful parsing, etc.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The info message to log
  static void i(String id, String message) {
    if (id.isEmpty) {
      throw ArgumentError('Correlation ID cannot be empty');
    }
    _logger.i('[parse:$id] $message');
  }

  /// Logs a warning message with the specified correlation ID.
  /// 
  /// Warning messages are used for recoverable errors or unexpected
  /// conditions that don't prevent parsing from continuing.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The warning message to log
  /// [error] Optional error object to include in the log
  static void w(String id, String message, [Object? error]) {
    if (id.isEmpty) {
      throw ArgumentError('Correlation ID cannot be empty');
    }
    _logger.w('[parse:$id] $message', error: error);
  }

  /// Logs an error message with the specified correlation ID.
  /// 
  /// Error messages are used for serious errors that may prevent
  /// parsing from completing successfully.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The error message to log
  /// [error] Optional error object to include in the log
  /// [stackTrace] Optional stack trace to include in the log
  static void e(String id, String message, [Object? error, StackTrace? stackTrace]) {
    if (id.isEmpty) {
      throw ArgumentError('Correlation ID cannot be empty');
    }
    _logger.e('[parse:$id] $message', error: error, stackTrace: stackTrace);
  }

  /// Sets a custom log output for testing purposes.
  ///
  /// This method allows injection of custom LogOutput implementations
  /// for testing, such as MemoryLogOutput to capture log records
  /// for verification in tests.
  ///
  /// [output] The LogOutput implementation to use
  static void setLogOutput(LogOutput output) {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 3,
        lineLength: 120,
        colors: true,
        printEmojis: false,
      ),
      output: output,
    );
  }
}
