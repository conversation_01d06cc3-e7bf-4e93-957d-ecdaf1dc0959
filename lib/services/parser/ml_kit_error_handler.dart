import 'dart:io';
import 'package:flutter/services.dart';
import '../error_handling_service.dart';

/// Specific error types for ML Kit operations
enum MLKitErrorType {
  networkError,
  modelDownloadFailure,
  modelNotAvailable,
  parsingError,
  permissionDenied,
  deviceNotSupported,
  unknownError,
}

/// Enhanced error handler for ML Kit operations
class MLKitErrorHandler {
  static const String _logContext = 'MLKitErrorHandler';

  /// Categorize ML Kit errors based on error type and message
  static MLKitErrorType categorizeError(Object error) {
    if (error is PlatformException) {
      return _categorizePlatformException(error);
    } else if (error is SocketException) {
      return MLKitErrorType.networkError;
    } else if (error is HttpException) {
      return MLKitErrorType.networkError;
    } else if (error.toString().toLowerCase().contains('network')) {
      return MLKitErrorType.networkError;
    } else if (error.toString().toLowerCase().contains('model')) {
      return MLKitErrorType.modelDownloadFailure;
    } else if (error.toString().toLowerCase().contains('permission')) {
      return MLKitErrorType.permissionDenied;
    } else {
      return MLKitErrorType.unknownError;
    }
  }

  static MLKitErrorType _categorizePlatformException(PlatformException error) {
    final code = error.code.toLowerCase();
    final message = error.message?.toLowerCase() ?? '';

    if (code.contains('network') || message.contains('network')) {
      return MLKitErrorType.networkError;
    } else if (code.contains('model') || message.contains('model')) {
      if (message.contains('download') || message.contains('not available')) {
        return MLKitErrorType.modelDownloadFailure;
      } else {
        return MLKitErrorType.modelNotAvailable;
      }
    } else if (code.contains('permission') || message.contains('permission')) {
      return MLKitErrorType.permissionDenied;
    } else if (code.contains('not_available') || message.contains('not supported')) {
      return MLKitErrorType.deviceNotSupported;
    } else if (code.contains('error') && message.contains('null reference')) {
      return MLKitErrorType.modelNotAvailable;
    } else {
      return MLKitErrorType.unknownError;
    }
  }

  /// Handle ML Kit errors with appropriate recovery strategies
  static MLKitErrorRecoveryResult handleError(Object error, String operation) {
    final errorType = categorizeError(error);
    
    // Log the error with categorization
    ErrorHandlingService.instance.handleSpecificError(
      error: error,
      context: '$_logContext.$operation',
      metadata: {
        'operation': operation,
        'errorType': errorType.name,
        'isRecoverable': isRecoverable(errorType),
      },
    );

    return MLKitErrorRecoveryResult(
      errorType: errorType,
      isRecoverable: isRecoverable(errorType),
      userMessage: getUserMessage(errorType),
      technicalMessage: getTechnicalMessage(errorType, error),
      recoveryAction: getRecoveryAction(errorType),
      shouldRetry: shouldRetry(errorType),
      fallbackToRegex: shouldFallbackToRegex(errorType),
    );
  }

  /// Determine if an error type is recoverable
  static bool isRecoverable(MLKitErrorType errorType) {
    switch (errorType) {
      case MLKitErrorType.networkError:
      case MLKitErrorType.modelDownloadFailure:
        return true;
      case MLKitErrorType.modelNotAvailable:
      case MLKitErrorType.parsingError:
        return false; // Can fallback to regex
      case MLKitErrorType.permissionDenied:
      case MLKitErrorType.deviceNotSupported:
        return false;
      case MLKitErrorType.unknownError:
        return true; // Worth trying again
    }
  }

  /// Get user-friendly error message
  static String getUserMessage(MLKitErrorType errorType) {
    switch (errorType) {
      case MLKitErrorType.networkError:
        return 'Network connection required for advanced parsing. Using basic parsing mode.';
      case MLKitErrorType.modelDownloadFailure:
        return 'Failed to download parsing models. Using basic parsing mode.';
      case MLKitErrorType.modelNotAvailable:
        return 'Advanced parsing not available. Using basic parsing mode.';
      case MLKitErrorType.parsingError:
        return 'Parsing error occurred. Using basic parsing mode.';
      case MLKitErrorType.permissionDenied:
        return 'Permissions required for advanced parsing. Using basic parsing mode.';
      case MLKitErrorType.deviceNotSupported:
        return 'Advanced parsing not supported on this device. Using basic parsing mode.';
      case MLKitErrorType.unknownError:
        return 'Advanced parsing temporarily unavailable. Using basic parsing mode.';
    }
  }

  /// Get technical error message for debugging
  static String getTechnicalMessage(MLKitErrorType errorType, Object error) {
    return '${errorType.name}: ${error.toString()}';
  }

  /// Get recovery action description
  static String getRecoveryAction(MLKitErrorType errorType) {
    switch (errorType) {
      case MLKitErrorType.networkError:
        return 'Check internet connection and try again';
      case MLKitErrorType.modelDownloadFailure:
        return 'Ensure stable internet connection and sufficient storage';
      case MLKitErrorType.modelNotAvailable:
        return 'No action required - using fallback parsing';
      case MLKitErrorType.parsingError:
        return 'No action required - using fallback parsing';
      case MLKitErrorType.permissionDenied:
        return 'Grant required permissions in app settings';
      case MLKitErrorType.deviceNotSupported:
        return 'No action required - device limitation';
      case MLKitErrorType.unknownError:
        return 'Restart app or try again later';
    }
  }

  /// Determine if retry is recommended
  static bool shouldRetry(MLKitErrorType errorType) {
    switch (errorType) {
      case MLKitErrorType.networkError:
      case MLKitErrorType.modelDownloadFailure:
      case MLKitErrorType.unknownError:
        return true;
      default:
        return false;
    }
  }

  /// Determine if should fallback to regex parsing
  static bool shouldFallbackToRegex(MLKitErrorType errorType) {
    // All ML Kit errors should fallback to regex parsing
    return true;
  }

  /// Get retry delay based on error type
  static Duration getRetryDelay(MLKitErrorType errorType) {
    switch (errorType) {
      case MLKitErrorType.networkError:
        return const Duration(seconds: 5);
      case MLKitErrorType.modelDownloadFailure:
        return const Duration(seconds: 10);
      case MLKitErrorType.unknownError:
        return const Duration(seconds: 3);
      default:
        return const Duration(seconds: 1);
    }
  }
}

/// Result of ML Kit error handling
class MLKitErrorRecoveryResult {
  final MLKitErrorType errorType;
  final bool isRecoverable;
  final String userMessage;
  final String technicalMessage;
  final String recoveryAction;
  final bool shouldRetry;
  final bool fallbackToRegex;

  const MLKitErrorRecoveryResult({
    required this.errorType,
    required this.isRecoverable,
    required this.userMessage,
    required this.technicalMessage,
    required this.recoveryAction,
    required this.shouldRetry,
    required this.fallbackToRegex,
  });

  @override
  String toString() {
    return 'MLKitErrorRecoveryResult(type: $errorType, recoverable: $isRecoverable, message: $userMessage)';
  }
}
