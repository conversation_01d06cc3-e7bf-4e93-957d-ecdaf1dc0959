import 'dart:developer' as developer;
import 'package:google_mlkit_entity_extraction/google_mlkit_entity_extraction.dart' as mlkit;
import 'entity_extractor_base.dart';
import '../storage_service.dart';
import 'parse_logger.dart';

/// Concrete implementation of EntityAnnotationBase that wraps ML Kit EntityAnnotation
class RealEntityAnnotation implements EntityAnnotationBase {
  final mlkit.EntityAnnotation _mlkitAnnotation;

  RealEntityAnnotation(this._mlkitAnnotation);

  @override
  String get text => _mlkitAnnotation.text;

  @override
  int get start => _mlkitAnnotation.start;

  @override
  int get end => _mlkitAnnotation.end;

  @override
  EntityType get entityType {
    // Convert ML Kit entity type to our abstraction
    // Using runtime type checking as the current implementation does
    final runtimeType = _mlkitAnnotation.runtimeType.toString();
    if (runtimeType.contains('Money')) {
      return EntityType.money;
    } else if (runtimeType.contains('DateTime')) {
      return EntityType.dateTime;
    } else if (runtimeType.contains('Address')) {
      return EntityType.address;
    } else if (runtimeType.contains('Email')) {
      return EntityType.email;
    } else if (runtimeType.contains('Phone')) {
      return EntityType.phone;
    } else if (runtimeType.contains('Url')) {
      return EntityType.url;
    } else {
      return EntityType.other;
    }
  }

  /// Get the original ML Kit annotation for cases where specific ML Kit functionality is needed
  mlkit.EntityAnnotation get mlkitAnnotation => _mlkitAnnotation;
}

/// Concrete implementation of EntityExtractorBase that wraps the actual Google ML Kit EntityExtractor
class RealEntityExtractor implements EntityExtractorBase {
  mlkit.EntityExtractor? _entityExtractor;
  bool _isInitialized = false;
  StorageService? _storageService;

  // Cache key for ML Kit model download status
  static const String _modelDownloadedKey = 'ml_kit_en_model_downloaded';

  /// Constructor that optionally accepts a StorageService for caching
  RealEntityExtractor([StorageService? storageService]) : _storageService = storageService;

  /// Initialize the ML Kit entity extractor
  /// This handles model downloading and initialization with caching support
  Future<void> initialize() async {
    if (_isInitialized) return;

    final stopwatch = Stopwatch()..start();

    try {
      developer.log('Initializing Real ML Kit Entity Extractor...', name: 'MLKitPerformance');

      // Check cache first if StorageService is available
      bool modelCached = false;
      if (_storageService != null) {
        modelCached = await _storageService!.getBool(_modelDownloadedKey) ?? false;
        if (modelCached) {
          developer.log('ML Kit model already cached, skipping download check', name: 'MLKitPerformance');
        }
      }

      final manager = mlkit.EntityExtractorModelManager();
      const languageTag = 'en';

      bool isDownloaded = false;
      if (!modelCached) {
        // Only check if model is downloaded if not cached
        developer.log('Checking ML Kit model availability...', name: 'MLKitPerformance');
        isDownloaded = await manager.isModelDownloaded(languageTag);

        if (!isDownloaded) {
          developer.log('Downloading ML Kit model...', name: 'MLKitPerformance');
          await manager.downloadModel(languageTag);
          developer.log('ML Kit model downloaded successfully', name: 'MLKitPerformance');

          // Cache the successful download
          if (_storageService != null) {
            await _storageService!.setBool(_modelDownloadedKey, true);
            developer.log('ML Kit model download status cached', name: 'MLKitPerformance');
          }
        } else {
          // Model was already downloaded, cache this information
          if (_storageService != null) {
            await _storageService!.setBool(_modelDownloadedKey, true);
          }
        }
      }

      _entityExtractor = mlkit.EntityExtractor(language: mlkit.EntityExtractorLanguage.english);
      _isInitialized = true;

      stopwatch.stop();
      developer.log('Real ML Kit Entity Extractor initialized successfully in ${stopwatch.elapsedMilliseconds}ms',
          name: 'MLKitPerformance');

    } catch (e) {
      stopwatch.stop();
      developer.log('Failed to initialize Real ML Kit Entity Extractor: $e', name: 'MLKitPerformance');

      // Reset cache flag on error
      if (_storageService != null) {
        try {
          await _storageService!.setBool(_modelDownloadedKey, false);
        } catch (cacheError) {
          developer.log('Failed to reset cache flag: $cacheError', name: 'MLKitPerformance');
        }
      }

      _isInitialized = false;
      rethrow;
    }
  }

  @override
  Future<List<EntityAnnotationBase>> annotateText(String text) async {
    if (!_isInitialized || _entityExtractor == null) {
      await initialize();
    }

    if (_entityExtractor == null) {
      throw Exception('ML Kit Entity Extractor not initialized');
    }

    try {
      final entities = await _entityExtractor!.annotateText(text);
      return entities.map((entity) => RealEntityAnnotation(entity)).toList();
    } catch (e) {
      ParseLogger.e('mlkit', 'Error during ML Kit entity annotation: $e', e);
      rethrow;
    }
  }

  @override
  Future<void> close() async {
    if (_entityExtractor != null) {
      await _entityExtractor!.close();
      _entityExtractor = null;
    }
    _isInitialized = false;
  }

  @override
  bool get isInitialized => _isInitialized && _entityExtractor != null;
}
