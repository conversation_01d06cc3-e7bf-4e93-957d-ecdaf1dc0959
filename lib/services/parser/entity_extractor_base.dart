/// Abstract base classes for entity extraction to enable dependency injection and testing
/// This provides a clean abstraction layer over ML Kit entity extraction functionality

/// Enumeration of entity types that can be extracted
enum EntityType {
  money,
  dateTime,
  address,
  email,
  phone,
  url,
  other,
}

/// Abstract base class representing an entity annotation
/// This defines the minimal interface needed by the parser service
abstract class EntityAnnotationBase {
  /// The text content of the entity
  String get text;
  
  /// Start position of the entity in the original text
  int get start;
  
  /// End position of the entity in the original text
  int get end;
  
  /// Type of the entity (money, datetime, etc.)
  EntityType get entityType;
}

/// Abstract base class for entity extraction functionality
/// This defines the interface that both real ML Kit and mock implementations must follow
abstract class EntityExtractorBase {
  /// Extract entities from the given text
  /// Returns a list of entity annotations found in the text
  Future<List<EntityAnnotationBase>> annotateText(String text);
  
  /// Close and dispose of any resources used by the extractor
  Future<void> close();
  
  /// Check if the extractor is properly initialized and ready to use
  bool get isInitialized;
}
