import 'package:flutter/foundation.dart';

/// Immutable configuration class that centralizes all hardcoded parsing constants.
/// 
/// This class provides a centralized way to configure parsing behavior across
/// all parsing strategies and services. It includes thresholds for embedded
/// number detection, amount formatting cutoffs, default currency settings,
/// and regex patterns for abbreviation detection.
/// 
/// All fields have sensible defaults that maintain backward compatibility
/// with existing parsing behavior.
@immutable
class ParsingConfig {
  /// Threshold for basic embedded letter detection.
  /// 
  /// Used in basic embedded number detection logic to determine if a number
  /// is embedded within text (e.g., "A2B" has 1 letter on each side).
  /// Default: 2
  final int embeddedLetterThreshold;

  /// Threshold for strict embedded letter detection.
  /// 
  /// Used in ML Kit strategy's strict embedded detection logic to filter out
  /// numbers that are likely part of vendor names or addresses.
  /// Examples: "lux68" (3 letters before), "hotel789" (5 letters before)
  /// Default: 3
  final int strictEmbeddedLetterThreshold;

  /// Cutoff value for thousand abbreviation in amount display formatting.
  /// 
  /// Amounts >= this value may be displayed with 'k' abbreviation.
  /// Default: 1000.0
  final double thousandCutoff;

  /// Cutoff value for million abbreviation in amount display formatting.
  /// 
  /// Amounts >= this value may be displayed with 'M' abbreviation.
  /// Default: 1000000.0
  final double millionCutoff;

  /// Cutoff value for billion abbreviation in amount display formatting.
  /// 
  /// Amounts >= this value may be displayed with 'B' abbreviation.
  /// Default: 1000000000.0
  final double billionCutoff;

  /// Default currency code used when no currency is detected in the text.
  /// 
  /// This fallback currency is used across all parsing strategies when
  /// currency extraction fails or returns null.
  /// Default: 'USD'
  final String defaultCurrency;

  /// Regex pattern for detecting amount abbreviations in text.
  /// 
  /// This pattern is used to identify abbreviation suffixes like 'k', 'K',
  /// 'm', 'M', 'b', 'B' in amount strings (e.g., "100k", "2.5M", "1.2B").
  /// Default: '[kKmMbB]'
  final String abbreviationPattern;

  /// Creates a new ParsingConfig with the specified values.
  /// 
  /// All parameters are optional and will use sensible defaults if not provided.
  /// This ensures backward compatibility with existing code.
  const ParsingConfig({
    this.embeddedLetterThreshold = 2,
    this.strictEmbeddedLetterThreshold = 3,
    this.thousandCutoff = 1000.0,
    this.millionCutoff = 1000000.0,
    this.billionCutoff = 1000000000.0,
    this.defaultCurrency = 'USD',
    this.abbreviationPattern = '[kKmMbB]',
  });

  /// Default configuration instance with all standard values.
  /// 
  /// This provides easy access to the default configuration without
  /// needing to create a new instance with default parameters.
  static const ParsingConfig defaults = ParsingConfig();

  /// Creates a copy of this configuration with the specified fields replaced.
  /// 
  /// This method preserves immutability by creating a new instance with
  /// the specified changes while keeping all other fields unchanged.
  /// 
  /// Example:
  /// ```dart
  /// final customConfig = ParsingConfig.defaults.copyWith(
  ///   defaultCurrency: 'EUR',
  ///   strictEmbeddedLetterThreshold: 2,
  /// );
  /// ```
  ParsingConfig copyWith({
    int? embeddedLetterThreshold,
    int? strictEmbeddedLetterThreshold,
    double? thousandCutoff,
    double? millionCutoff,
    double? billionCutoff,
    String? defaultCurrency,
    String? abbreviationPattern,
  }) {
    return ParsingConfig(
      embeddedLetterThreshold: embeddedLetterThreshold ?? this.embeddedLetterThreshold,
      strictEmbeddedLetterThreshold: strictEmbeddedLetterThreshold ?? this.strictEmbeddedLetterThreshold,
      thousandCutoff: thousandCutoff ?? this.thousandCutoff,
      millionCutoff: millionCutoff ?? this.millionCutoff,
      billionCutoff: billionCutoff ?? this.billionCutoff,
      defaultCurrency: defaultCurrency ?? this.defaultCurrency,
      abbreviationPattern: abbreviationPattern ?? this.abbreviationPattern,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ParsingConfig) return false;

    return embeddedLetterThreshold == other.embeddedLetterThreshold &&
        strictEmbeddedLetterThreshold == other.strictEmbeddedLetterThreshold &&
        thousandCutoff == other.thousandCutoff &&
        millionCutoff == other.millionCutoff &&
        billionCutoff == other.billionCutoff &&
        defaultCurrency == other.defaultCurrency &&
        abbreviationPattern == other.abbreviationPattern;
  }

  @override
  int get hashCode {
    return Object.hash(
      embeddedLetterThreshold,
      strictEmbeddedLetterThreshold,
      thousandCutoff,
      millionCutoff,
      billionCutoff,
      defaultCurrency,
      abbreviationPattern,
    );
  }

  @override
  String toString() {
    return 'ParsingConfig('
        'embeddedLetterThreshold: $embeddedLetterThreshold, '
        'strictEmbeddedLetterThreshold: $strictEmbeddedLetterThreshold, '
        'thousandCutoff: $thousandCutoff, '
        'millionCutoff: $millionCutoff, '
        'billionCutoff: $billionCutoff, '
        'defaultCurrency: $defaultCurrency, '
        'abbreviationPattern: $abbreviationPattern'
        ')';
  }
}
