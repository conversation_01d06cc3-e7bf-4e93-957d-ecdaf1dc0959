import 'dart:async';
import 'package:uuid/uuid.dart';
import '../fallback_parser_service.dart';
import '../parsing_strategy.dart';
import '../parsing_context.dart';
import '../parsing_config.dart';
import '../../../models/parse_result.dart';
import '../../../models/transaction_model.dart';
import '../parse_logger.dart';

/// Strategy that handles fallback regex parsing.
/// 
/// This strategy serves as a simple wrapper around the existing FallbackParserService
/// without any code duplication. It always returns a non-null ParseResult since the
/// fallback parser is designed to handle all inputs and never decline.
class FallbackRegexStrategy implements ParsingStrategy {
  final FallbackParserService _fallbackParser;
  final ParsingConfig _config;

  /// Creates a new FallbackRegexStrategy with required dependencies.
  ///
  /// [fallbackParser] - The fallback parser service to delegate to
  /// [config] - Parsing configuration for consistent default currency usage
  FallbackRegexStrategy(this._fallbackParser, this._config);

  @override
  String get name => 'FallbackRegexStrategy';

  @override
  Future<ParseResult?> execute(ParsingContext context) async {
    final parseId = context.parseId ?? 'unknown';
    try {
      // Delegate to FallbackParserService with proper locale handling
      final result = await _fallbackParser.parseTransaction(
        context.text,
        locale: context.locale,
      );

      // FallbackParserService always returns a ParseResult, never null
      return result;
    } catch (e) {
      ParseLogger.e(parseId, 'Error in FallbackRegexStrategy: $e', e);
      // Even on error, try to return a basic failed result rather than null
      // since this is the fallback strategy
      return ParseResult.failed(
        await _createBasicTransaction(context.text),
        'Fallback parsing failed: $e',
      );
    }
  }

  /// Create a basic transaction for error cases
  Future<Transaction> _createBasicTransaction(String text) async {
    return Transaction(
      id: const Uuid().v4(),
      amount: 0.0,
      type: TransactionType.expense,
      categoryId: 'unknown',
      date: DateTime.now(),
      description: text.trim(),
      tags: [],
      currencyCode: _config.defaultCurrency, // Use configurable default currency
    );
  }
}
