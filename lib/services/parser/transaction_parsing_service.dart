import 'dart:async';
import 'package:uuid/uuid.dart';
import '../../models/transaction_model.dart';
import '../../models/parse_result.dart';
import '../storage_service.dart';
import '../error_handling_service.dart';
import 'category_finder_service.dart';
import 'fallback_parser_service.dart';
import 'learned_association_service.dart';
import 'entity_extractor_base.dart';
import 'real_entity_extractor.dart';
import 'parsing_strategy.dart';
import 'parsing_context.dart';
import 'parsing_config.dart';
import 'strategies/learned_association_strategy.dart';
import 'strategies/mlkit_strategy.dart';
import 'strategies/fallback_regex_strategy.dart';
import 'parse_logger.dart';
import 'ml_kit_error_handler.dart';

/// Main orchestrator service that coordinates the entire parsing pipeline using the strategy pattern
class TransactionParsingService {
  static TransactionParsingService? _instance;

  // Dependencies
  EntityExtractorBase? _entityExtractor;
  late CategoryFinderService _categoryFinder;
  late FallbackParserService _fallbackParser;
  late StorageService _storageService;
  late LearnedAssociationService _learnedAssociationService;
  final Uuid _uuid = const Uuid();

  // Configuration
  late ParsingConfig _config;

  // State
  bool _isInitialized = false;
  bool _mlKitAvailable = false;

  // Strategy list
  late List<ParsingStrategy> _strategies;

  TransactionParsingService._();

  /// Factory constructor to get singleton instance
  /// [entityExtractor] - Optional entity extractor for dependency injection (mainly for testing)
  /// [config] - Optional parsing configuration (uses defaults if not provided)
  static Future<TransactionParsingService> getInstance(
    StorageService storageService, {
    EntityExtractorBase? entityExtractor,
    ParsingConfig? config,
  }) async {
    if (_instance == null) {
      _instance = _createInstance(storageService, config);
      await _instance!._initialize(entityExtractor);
    }
    return _instance!;
  }

  /// Reset singleton instance (for testing only)
  static void resetInstance() {
    _instance?._entityExtractor?.close();
    _instance = null;
  }

  /// Static background initialization method for deferred loading
  static Future<TransactionParsingService> initializeInBackground(
    StorageService storageService, {
    EntityExtractorBase? entityExtractor,
    ParsingConfig? config,
  }) async {
    if (_instance == null) {
      _instance = _createInstance(storageService, config);
      await _instance!._initializeWithCaching(entityExtractor);
    }
    return _instance!;
  }

  /// Helper method to create and set up common instance properties
  static TransactionParsingService _createInstance(StorageService storageService, ParsingConfig? config) {
    final instance = TransactionParsingService._();
    instance._storageService = storageService;
    instance._categoryFinder = CategoryFinderService(storageService);
    instance._fallbackParser = FallbackParserService(storageService);
    instance._config = config ?? ParsingConfig.defaults;
    return instance;
  }

  /// Check if ML Kit is fully initialized and ready
  bool get isReady => _isInitialized && _mlKitAvailable && _entityExtractor != null;

  /// Get the category finder service for advanced category suggestions
  CategoryFinderService get categoryFinder => _categoryFinder;

  /// Initialize ML Kit models with fallback support
  /// [entityExtractor] - Optional injected entity extractor (for testing)
  Future<void> _initialize([EntityExtractorBase? entityExtractor]) async {
    if (_isInitialized) return;

    // Initialize learned association service first
    _learnedAssociationService = await LearnedAssociationService.getInstance(_storageService);

    // If an entity extractor was injected, use it instead of creating a real one
    if (entityExtractor != null) {
      _entityExtractor = entityExtractor;
      _mlKitAvailable = entityExtractor.isInitialized;
      ParseLogger.i('init', 'Using injected entity extractor');
      _isInitialized = true;
      _initializeStrategies();
      return;
    }

    try {
      ParseLogger.i('init', 'Attempting to initialize ML Kit...');
      // Create and initialize the real entity extractor with StorageService for caching
      final realExtractor = RealEntityExtractor(_storageService);
      await realExtractor.initialize();

      _entityExtractor = realExtractor;
      _mlKitAvailable = true;
      ParseLogger.i('init', 'ML Kit initialized successfully');

    } catch (e) {
      final recoveryResult = MLKitErrorHandler.handleError(e, 'initialization');

      ParseLogger.w('init', 'Error initializing ML Kit: ${recoveryResult.technicalMessage}', e);
      ParseLogger.w('init', 'User message: ${recoveryResult.userMessage}');
      ParseLogger.w('init', 'Recovery action: ${recoveryResult.recoveryAction}');

      if (recoveryResult.fallbackToRegex) {
        ParseLogger.w('init', 'Falling back to regex-based parsing');
        _mlKitAvailable = false;
        // ML Kit will be null, will use fallback parser
      }
    }

    _isInitialized = true;
    _initializeStrategies();
  }

  /// Initialize ML Kit models with enhanced caching support for background initialization
  /// [entityExtractor] - Optional injected entity extractor (for testing)
  Future<void> _initializeWithCaching([EntityExtractorBase? entityExtractor]) async {
    if (_isInitialized) return;

    // Initialize learned association service first
    _learnedAssociationService = await LearnedAssociationService.getInstance(_storageService);

    // If an entity extractor was injected, use it instead of creating a real one
    if (entityExtractor != null) {
      _entityExtractor = entityExtractor;
      _mlKitAvailable = entityExtractor.isInitialized;
      ParseLogger.i('init', 'Using injected entity extractor');
      _isInitialized = true;
      _initializeStrategies();
      return;
    }

    try {
      ParseLogger.i('init', 'Attempting to initialize ML Kit with caching...');
      // Create and initialize the real entity extractor with StorageService for caching
      final realExtractor = RealEntityExtractor(_storageService);
      await realExtractor.initialize();

      _entityExtractor = realExtractor;
      _mlKitAvailable = true;
      ParseLogger.i('init', 'ML Kit initialized successfully with caching');

    } catch (e) {
      final recoveryResult = MLKitErrorHandler.handleError(e, 'background_initialization');

      ParseLogger.w('init', 'Error initializing ML Kit with caching: ${recoveryResult.technicalMessage}', e);
      ParseLogger.w('init', 'User message: ${recoveryResult.userMessage}');
      ParseLogger.w('init', 'Recovery action: ${recoveryResult.recoveryAction}');

      if (recoveryResult.fallbackToRegex) {
        ParseLogger.w('init', 'Falling back to regex-based parsing');
        _mlKitAvailable = false;
        // ML Kit will be null, will use fallback parser
      }
    }

    _isInitialized = true;
    _initializeStrategies();
  }

  /// Initialize the strategy list with proper dependency injection
  void _initializeStrategies() {
    _strategies = [
      // Strategy 1: Check learned associations first
      LearnedAssociationStrategy(
        _learnedAssociationService,
        _storageService,
        _uuid,
        _config,
      ),
      // Strategy 2: ML Kit parsing with Trust but Verify approach
      MlKitStrategy(
        _entityExtractor,
        _storageService,
        _categoryFinder,
        _uuid,
        _mlKitAvailable,
        _config,
      ),
      // Strategy 3: Fallback regex parsing (always returns non-null)
      FallbackRegexStrategy(_fallbackParser, _config),
    ];
  }

  /// Main entry point for parsing transactions using strategy pattern
  Future<ParseResult> parseTransaction(String text) async {
    final parseId = ParseLogger.start(text);
    try {
      if (!_isInitialized) {
        await _initialize();
      }

      // Handle empty or whitespace-only input
      if (text.trim().isEmpty) {
        return ParseResult.failed(
          await _createFallbackTransaction(text),
          'Empty or whitespace-only input',
        );
      }

      // Execute strategy chain
      final context = ParsingContext(text: text, locale: null, parseId: parseId);
      for (final strategy in _strategies) {
        ParseLogger.i(parseId, 'Trying strategy: ${strategy.name}');
        final result = await strategy.execute(context);
        if (result != null) {
          ParseLogger.i(parseId, 'Strategy ${strategy.name} handled the input');
          return result;
        }
        ParseLogger.i(parseId, 'Strategy ${strategy.name} declined to handle');
      }

      // This should never happen since FallbackRegexStrategy always returns non-null
      return ParseResult.failed(
        await _createFallbackTransaction(text),
        'All strategies failed to handle input',
      );

    } catch (e) {
      return ParseResult.failed(
        await _createFallbackTransaction(text),
        'Parsing failed: $e',
      );
    }
  }

  /// Learn a category association for future parsing
  Future<void> learnCategory(String text, String categoryId) async {
    if (!_isInitialized) {
      await _initialize();
    }
    
    // Extract transaction type from text for better learning
    final type = _detectTransactionType(text) ?? TransactionType.expense;
    
    await _learnedAssociationService.learn(
      text,
      type: type,
      categoryId: categoryId,
    );
  }

  /// Complete a transaction with confirmed amount
  Future<ParseResult> completeTransaction(String originalText, double confirmedAmount) async {
    if (!_isInitialized) {
      await _initialize();
    }

    try {
      // Learn the confirmed amount association
      await _learnedAssociationService.learn(originalText, confirmedAmount: confirmedAmount);
      ParseLogger.d('complete', 'Learned amount association for future use');

      // Determine transaction type
      final type = _detectTransactionType(originalText) ?? TransactionType.expense;
      ParseLogger.d('complete', 'Detected transaction type: $type');

      // Find category
      final categoryId = await _categoryFinder.findCategory(originalText, type);
      ParseLogger.d('complete', 'Found category: $categoryId');

      // Extract currency from original text
      final currency = _extractCurrencyFromText(originalText) ?? await _storageService.getDefaultCurrency();
      ParseLogger.d('complete', 'Using currency: $currency');

      // Create the transaction
      final transaction = Transaction(
        id: _uuid.v4(),
        amount: confirmedAmount,
        type: type,
        categoryId: categoryId ?? 'unknown',
        date: DateTime.now(),
        description: originalText.trim(),
        tags: _extractTags(originalText),
        currencyCode: currency,
      );

      // Return result indicating if category selection is needed
      if (categoryId == null) {
        return ParseResult.needsCategory(transaction, ambiguityType: AmbiguityType.ambiguousCategory);
      } else {
        return ParseResult.success(transaction);
      }

    } catch (e) {
      return ParseResult.failed(
        await _createFallbackTransaction(originalText),
        'Transaction completion failed: $e',
      );
    }
  }

  /// Dispose resources
  void dispose() {
    _entityExtractor?.close();
  }

  /// Create a fallback transaction for error cases
  Future<Transaction> _createFallbackTransaction(String text) async {
    return Transaction(
      id: _uuid.v4(),
      amount: 0.0,
      type: TransactionType.expense,
      categoryId: 'unknown',
      date: DateTime.now(),
      description: text.trim(),
      tags: [],
      currencyCode: await _storageService.getDefaultCurrency(),
    );
  }

  /// Detect transaction type from text
  TransactionType? _detectTransactionType(String text, {bool isNegativeAmount = false}) {
    final lowerText = text.toLowerCase();

    // Income indicators
    if (lowerText.contains('salary') ||
        lowerText.contains('income') ||
        lowerText.contains('bonus') ||
        lowerText.contains('refund') ||
        lowerText.contains('deposit') ||
        lowerText.contains('received')) {
      return TransactionType.income;
    }

    // Loan indicators
    if (lowerText.contains('loan') ||
        lowerText.contains('borrow') ||
        lowerText.contains('lend')) {
      return TransactionType.loan;
    }

    // Default to expense for most cases
    if (lowerText.contains('buy') ||
        lowerText.contains('purchase') ||
        lowerText.contains('pay') ||
        lowerText.contains('spent') ||
        lowerText.contains('cost') ||
        isNegativeAmount) {
      return TransactionType.expense;
    }

    // Return null if type is unclear - will trigger needsType flow
    return null;
  }

  /// Extract currency from text
  String? _extractCurrencyFromText(String text) {
    // Common currency symbols and codes
    final currencyPatterns = {
      r'\$': 'USD',
      r'€': 'EUR',
      r'£': 'GBP',
      r'¥': 'JPY',
      r'₹': 'INR',
      r'USD': 'USD',
      r'EUR': 'EUR',
      r'GBP': 'GBP',
      r'JPY': 'JPY',
      r'INR': 'INR',
    };

    for (final pattern in currencyPatterns.keys) {
      if (RegExp(pattern, caseSensitive: false).hasMatch(text)) {
        return currencyPatterns[pattern];
      }
    }

    return null;
  }

  /// Extract tags from text
  List<String> _extractTags(String text) {
    final tags = <String>[];

    // Extract hashtags
    final hashtagRegex = RegExp(r'#(\w+)');
    final hashtagMatches = hashtagRegex.allMatches(text);
    for (final match in hashtagMatches) {
      tags.add(match.group(1)!);
    }

    return tags;
  }
}
