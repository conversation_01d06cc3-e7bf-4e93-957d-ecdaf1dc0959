import 'dart:async';
import '../../models/parse_result.dart';
import 'parsing_context.dart';

/// Abstract base class that defines the interface for all parsing strategies
/// in the strategy pattern.
///
/// This class follows the chain of responsibility pattern where strategies
/// can decline to handle input by returning null. Multiple strategies can
/// be tried in sequence, with each strategy having the option to decline
/// handling by returning null.
///
/// Concrete strategies must implement:
/// - [execute]: The main parsing logic that processes the input context
/// - [name]: A unique identifier for debugging and logging purposes
abstract class ParsingStrategy {
  /// Executes the parsing strategy on the given context.
  ///
  /// Returns a [ParseResult] if the strategy successfully handles the input,
  /// or null if the strategy declines to handle this input (following the
  /// chain of responsibility pattern).
  ///
  /// The [context] contains all necessary information for parsing, including
  /// the input text, locale, and any additional data in the extras map.
  Future<ParseResult?> execute(ParsingContext context);

  /// The name of this parsing strategy.
  ///
  /// Used for debugging, logging, and identification purposes.
  /// Should be unique among all strategy implementations.
  String get name;
}
