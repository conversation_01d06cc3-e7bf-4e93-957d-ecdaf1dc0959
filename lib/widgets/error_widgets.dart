import 'package:flutter/material.dart';
import '../services/error_handling_service.dart';

/// Error dialog for displaying user-friendly error messages with retry options
class ErrorDialog extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onRetry;
  final VoidCallback? onCancel;
  final bool showRetry;
  final IconData? icon;

  const ErrorDialog({
    super.key,
    required this.title,
    required this.message,
    this.onRetry,
    this.onCancel,
    this.showRetry = true,
    this.icon,
  });

  /// Show error dialog with automatic error message generation
  static Future<bool?> show({
    required BuildContext context,
    required Object error,
    String? title,
    VoidCallback? onRetry,
    bool showRetry = true,
  }) {
    final errorService = ErrorHandlingService.instance;
    final userMessage = errorService.getUserFriendlyMessage(error);
    final isRecoverable = errorService.isRecoverableError(error);

    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => ErrorDialog(
        title: title ?? 'Error',
        message: userMessage,
        onRetry: isRecoverable && showRetry ? onRetry : null,
        showRetry: isRecoverable && showRetry,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      title: Row(
        children: [
          Icon(
            icon ?? Icons.error_outline,
            color: theme.colorScheme.error,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            message,
            style: theme.textTheme.bodyMedium,
          ),
          if (showRetry && onRetry != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 20,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'You can try again or continue without this feature.',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
      actions: [
        if (onCancel != null || !showRetry)
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(false);
              onCancel?.call();
            },
            child: Text(showRetry ? 'Cancel' : 'OK'),
          ),
        if (showRetry && onRetry != null)
          ElevatedButton.icon(
            onPressed: () {
              Navigator.of(context).pop(true);
              onRetry?.call();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Retry'),
          ),
      ],
    );
  }
}

/// Error state widget for displaying errors within screens
class ErrorStateWidget extends StatelessWidget {
  final String title;
  final String message;
  final VoidCallback? onRetry;
  final IconData? icon;
  final bool showRetry;

  const ErrorStateWidget({
    super.key,
    required this.title,
    required this.message,
    this.onRetry,
    this.icon,
    this.showRetry = true,
  });

  /// Create error state widget from error object
  factory ErrorStateWidget.fromError({
    required Object error,
    String? title,
    VoidCallback? onRetry,
    IconData? icon,
  }) {
    final errorService = ErrorHandlingService.instance;
    final userMessage = errorService.getUserFriendlyMessage(error);
    final isRecoverable = errorService.isRecoverableError(error);

    return ErrorStateWidget(
      title: title ?? 'Something went wrong',
      message: userMessage,
      onRetry: isRecoverable ? onRetry : null,
      icon: icon,
      showRetry: isRecoverable,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: theme.colorScheme.errorContainer.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon ?? Icons.error_outline,
                size: 64,
                color: theme.colorScheme.error,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              title,
              style: theme.textTheme.headlineSmall?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            Text(
              message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (showRetry && onRetry != null) ...[
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Loading state widget with error fallback
class LoadingWithErrorWidget extends StatelessWidget {
  final bool isLoading;
  final Object? error;
  final Widget child;
  final VoidCallback? onRetry;
  final String? errorTitle;

  const LoadingWithErrorWidget({
    super.key,
    required this.isLoading,
    required this.child,
    this.error,
    this.onRetry,
    this.errorTitle,
  });

  @override
  Widget build(BuildContext context) {
    if (error != null) {
      return ErrorStateWidget.fromError(
        error: error!,
        title: errorTitle,
        onRetry: onRetry,
      );
    }

    if (isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Loading...'),
          ],
        ),
      );
    }

    return child;
  }
}

/// Snackbar for showing non-critical errors
class ErrorSnackBar {
  static void show({
    required BuildContext context,
    required Object error,
    VoidCallback? onRetry,
    Duration duration = const Duration(seconds: 4),
  }) {
    final errorService = ErrorHandlingService.instance;
    final message = errorService.getUserFriendlyMessage(error);
    final isRecoverable = errorService.isRecoverableError(error);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).colorScheme.error,
        duration: duration,
        action: isRecoverable && onRetry != null
            ? SnackBarAction(
                label: 'Retry',
                textColor: Colors.white,
                onPressed: onRetry,
              )
            : null,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}

/// Error boundary widget that catches errors in its child tree
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error, VoidCallback retry)? errorBuilder;
  final void Function(Object error, StackTrace? stackTrace)? onError;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.errorBuilder,
    this.onError,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  
  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      return widget.errorBuilder?.call(_error!, _retry) ??
          ErrorStateWidget.fromError(
            error: _error!,
            onRetry: _retry,
          );
    }
    
    return widget.child;
  }
  
  void _retry() {
    setState(() {
      _error = null;
    });
  }
  
  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    ErrorWidget.builder = (FlutterErrorDetails details) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _error = details.exception;
          });
          widget.onError?.call(details.exception, details.stack);
        }
      });
      return ErrorStateWidget.fromError(error: details.exception);
    };
  }
}
