import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../models/transaction_model.dart';

class TransactionEditDialog extends StatefulWidget {
  final Transaction transaction;
  
  const TransactionEditDialog({
    Key? key,
    required this.transaction,
  }) : super(key: key);

  @override
  State<TransactionEditDialog> createState() => _TransactionEditDialogState();
}

class _TransactionEditDialogState extends State<TransactionEditDialog> {
  late TextEditingController _amountController;
  late TextEditingController _descriptionController;
  late DateTime _selectedDate;
  late String _selectedCategoryId;
  late TransactionType _selectedType;
  
  @override
  void initState() {
    super.initState();
    _amountController = TextEditingController(text: widget.transaction.amount.toString());
    _descriptionController = TextEditingController(text: widget.transaction.description);
    _selectedDate = widget.transaction.date;
    _selectedCategoryId = widget.transaction.categoryId;
    _selectedType = widget.transaction.type;
  }
  
  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final provider = Provider.of<TransactionProvider>(context);
    
    return AlertDialog(
      title: const Text('Edit Transaction'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Amount field
            TextField(
              controller: _amountController,
              decoration: const InputDecoration(
                labelText: 'Amount',
                prefixText: '\$',
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
              ],
            ),
            const SizedBox(height: 16),
            
            // Transaction type selection
            DropdownButtonFormField<TransactionType>(
              value: _selectedType,
              decoration: const InputDecoration(
                labelText: 'Transaction Type',
              ),
              items: TransactionType.values.map((type) {
                String label = type.name[0].toUpperCase() + type.name.substring(1);
                IconData icon;
                Color color;
                
                switch (type) {
                  case TransactionType.expense:
                    icon = Icons.arrow_upward;
                    color = Colors.red;
                    break;
                  case TransactionType.income:
                    icon = Icons.arrow_downward;
                    color = Colors.green;
                    break;
                  case TransactionType.loan:
                    icon = Icons.sync_alt;
                    color = Colors.orange;
                    break;
                }
                
                return DropdownMenuItem<TransactionType>(
                  value: type,
                  child: Row(
                    children: [
                      Icon(icon, color: color, size: 18),
                      const SizedBox(width: 8),
                      Text(label),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedType = value;
                    
                    // Update category when type changes
                    final categories = provider.categories.where((c) => c.type == value).toList();
                    if (categories.isNotEmpty) {
                      _selectedCategoryId = categories.first.id;
                    }
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            
            // Category selection
            DropdownButtonFormField<String>(
              value: _selectedCategoryId,
              decoration: const InputDecoration(
                labelText: 'Category',
              ),
              items: provider.categories
                  .where((category) => category.type == _selectedType)
                  .map((category) {
                return DropdownMenuItem<String>(
                  value: category.id,
                  child: Row(
                    children: [
                      Text(category.icon),
                      const SizedBox(width: 8),
                      Text(category.name),
                    ],
                  ),
                );
              }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedCategoryId = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            
            // Description field
            TextField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'Description',
              ),
              maxLines: 2,
            ),
            const SizedBox(height: 16),
            
            // Date picker
            ListTile(
              contentPadding: EdgeInsets.zero,
              title: const Text('Date'),
              subtitle: Text(_formatDate(_selectedDate)),
              trailing: const Icon(Icons.calendar_today),
              onTap: () async {
                final picked = await showDatePicker(
                  context: context,
                  initialDate: _selectedDate,
                  firstDate: DateTime(2020),
                  lastDate: DateTime.now().add(const Duration(days: 365)),
                );
                
                if (picked != null) {
                  setState(() {
                    _selectedDate = DateTime(
                      picked.year,
                      picked.month,
                      picked.day,
                      _selectedDate.hour,
                      _selectedDate.minute,
                    );
                  });
                }
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        FilledButton(
          onPressed: () {
            try {
              final amount = double.parse(_amountController.text);
              
              if (amount <= 0) {
                _showErrorSnackBar('Amount must be greater than zero');
                return;
              }
              
              final updatedTransaction = widget.transaction.copyWith(
                amount: amount,
                type: _selectedType,
                categoryId: _selectedCategoryId,
                date: _selectedDate,
                description: _descriptionController.text.trim(),
              );
              
              Navigator.of(context).pop(updatedTransaction);
            } catch (e) {
              _showErrorSnackBar('Please enter a valid amount');
            }
          },
          child: const Text('Save'),
        ),
      ],
    );
  }
  
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
  
  String _formatDate(DateTime date) {
    final day = date.day.toString().padLeft(2, '0');
    final month = date.month.toString().padLeft(2, '0');
    final year = date.year;
    return '$day/$month/$year';
  }
} 