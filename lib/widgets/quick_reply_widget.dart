import 'package:flutter/material.dart';
import '../models/parse_result.dart';
import '../models/transaction_model.dart';

/// Data class for quick reply options with enhanced features
class QuickReplyOption {
  final String text;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;
  final bool isPrimary;
  final String? semanticLabel;

  const QuickReplyOption({
    required this.text,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.isPrimary = false,
    this.semanticLabel,
  });
}

/// A reusable widget that displays horizontal quick reply buttons for user interaction
class QuickReplyWidget extends StatelessWidget {
  final List<String> replyOptions;
  final List<QuickReplyOption>? enhancedOptions;
  final Function(String) onReplySelected;
  final EdgeInsets? padding;
  final double? spacing;
  final bool enabled;
  final bool showIcons;
  final bool groupByPriority;

  const QuickReplyWidget({
    super.key,
    required this.replyOptions,
    required this.onReplySelected,
    this.enhancedOptions,
    this.padding,
    this.spacing,
    this.enabled = true,
    this.showIcons = false,
    this.groupByPriority = false,
  });

  /// Factory constructor for ambiguity-aware quick replies
  factory QuickReplyWidget.forAmbiguityType({
    required String ambiguityType,
    required List<String> options,
    required Function(String) onReplySelected,
    bool enabled = true,
  }) {
    final enhancedOptions = _createEnhancedOptionsForAmbiguity(ambiguityType, options);

    return QuickReplyWidget(
      replyOptions: options,
      enhancedOptions: enhancedOptions,
      onReplySelected: onReplySelected,
      enabled: enabled,
      showIcons: true,
      groupByPriority: true,
    );
  }

  /// Factory constructor with icons support
  factory QuickReplyWidget.withIcons({
    required List<QuickReplyOption> options,
    required Function(String) onReplySelected,
    bool enabled = true,
    EdgeInsets? padding,
    double? spacing,
  }) {
    return QuickReplyWidget(
      replyOptions: options.map((opt) => opt.text).toList(),
      enhancedOptions: options,
      onReplySelected: onReplySelected,
      enabled: enabled,
      showIcons: true,
      padding: padding,
      spacing: spacing,
    );
  }

  /// Creates enhanced options for different ambiguity types
  static List<QuickReplyOption> _createEnhancedOptionsForAmbiguity(String ambiguityType, List<String> options) {
    switch (ambiguityType) {
      case AmbiguityType.missingAmount:
        return options.map((option) {
          if (option.startsWith('\$') || option.contains('Amount')) {
            return QuickReplyOption(
              text: option,
              icon: option.contains('Amount') ? Icons.edit : Icons.attach_money,
              isPrimary: true,
              semanticLabel: 'Amount suggestion: $option',
            );
          }
          return QuickReplyOption(text: option);
        }).toList();

      case AmbiguityType.ambiguousType:
        return options.map((option) {
          IconData? icon;
          bool isPrimary = false;
          if (option == 'Expense') {
            icon = Icons.remove_circle_outline;
            isPrimary = true;
          } else if (option == 'Income') {
            icon = Icons.add_circle_outline;
            isPrimary = true;
          } else if (option == 'Cancel') {
            icon = Icons.cancel_outlined;
          }
          return QuickReplyOption(
            text: option,
            icon: icon,
            isPrimary: isPrimary,
            semanticLabel: 'Transaction type: $option',
          );
        }).toList();

      case AmbiguityType.ambiguousCategory:
        return options.map((option) {
          IconData? icon;
          bool isPrimary = false;
          if (option != 'Other' && option != 'Cancel') {
            icon = Icons.category_outlined;
            isPrimary = true;
          } else if (option == 'Other') {
            icon = Icons.more_horiz;
          } else if (option == 'Cancel') {
            icon = Icons.cancel_outlined;
          }
          return QuickReplyOption(
            text: option,
            icon: icon,
            isPrimary: isPrimary,
            semanticLabel: 'Category suggestion: $option',
          );
        }).toList();

      default:
        return options.map((option) => QuickReplyOption(text: option)).toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (replyOptions.isEmpty) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Use enhanced options if available, otherwise create basic options
    final optionsToRender = enhancedOptions ??
        replyOptions.map((option) => QuickReplyOption(text: option)).toList();

    // Group options by priority if requested
    List<QuickReplyOption> primaryOptions = [];
    List<QuickReplyOption> secondaryOptions = [];

    if (groupByPriority) {
      for (final option in optionsToRender) {
        if (option.isPrimary) {
          primaryOptions.add(option);
        } else {
          secondaryOptions.add(option);
        }
      }
    }

    return Container(
      padding: padding ?? const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (groupByPriority && primaryOptions.isNotEmpty) ...[
            Wrap(
              spacing: spacing ?? 8.0,
              runSpacing: 8.0,
              children: primaryOptions.map((option) => _buildEnhancedReplyButton(
                context, option, colorScheme, true,
              )).toList(),
            ),
            if (secondaryOptions.isNotEmpty) const SizedBox(height: 8.0),
          ],
          if (groupByPriority && secondaryOptions.isNotEmpty)
            Wrap(
              spacing: spacing ?? 8.0,
              runSpacing: 8.0,
              children: secondaryOptions.map((option) => _buildEnhancedReplyButton(
                context, option, colorScheme, false,
              )).toList(),
            )
          else if (!groupByPriority)
            Wrap(
              spacing: spacing ?? 8.0,
              runSpacing: 8.0,
              children: optionsToRender.map((option) => _buildEnhancedReplyButton(
                context, option, colorScheme, option.isPrimary,
              )).toList(),
            ),
        ],
      ),
    );
  }

  /// Enhanced reply button builder with icon and styling support
  Widget _buildEnhancedReplyButton(
    BuildContext context,
    QuickReplyOption option,
    ColorScheme colorScheme,
    bool isPrimary,
  ) {
    final backgroundColor = option.backgroundColor ??
        (enabled
            ? (isPrimary ? colorScheme.primaryContainer : colorScheme.secondaryContainer)
            : colorScheme.surfaceContainerHighest.withValues(alpha: 0.5));

    final textColor = option.textColor ??
        (enabled
            ? (isPrimary ? colorScheme.onPrimaryContainer : colorScheme.onSecondaryContainer)
            : colorScheme.onSurfaceVariant.withValues(alpha: 0.5));

    return Material(
      child: InkWell(
        onTap: enabled ? () => onReplySelected(option.text) : null,
        borderRadius: BorderRadius.circular(20.0),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: showIcons && option.icon != null ? 12.0 : 16.0,
            vertical: 8.0,
          ),
          decoration: BoxDecoration(
            color: backgroundColor,
            borderRadius: BorderRadius.circular(20.0),
            border: Border.all(
              color: enabled
                  ? (isPrimary
                      ? colorScheme.primary.withValues(alpha: 0.3)
                      : colorScheme.outline.withValues(alpha: 0.3))
                  : colorScheme.outline.withValues(alpha: 0.1),
              width: isPrimary ? 1.5 : 1.0,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (showIcons && option.icon != null) ...[
                Icon(
                  option.icon,
                  size: 16.0,
                  color: textColor,
                ),
                const SizedBox(width: 6.0),
              ],
              Text(
                option.text,
                style: TextStyle(
                  color: textColor,
                  fontSize: 14.0,
                  fontWeight: isPrimary ? FontWeight.w600 : FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// A specialized quick reply widget for transaction type selection
class TransactionTypeQuickReply extends StatelessWidget {
  final Function(TransactionType) onTypeSelected;
  final Function()? onCancel;
  final bool enabled;

  const TransactionTypeQuickReply({
    super.key,
    required this.onTypeSelected,
    this.onCancel,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickReplyWidget(
      replyOptions: const ['Expense', 'Income', 'Loan', 'Cancel'],
      onReplySelected: (selectedType) {
        switch (selectedType) {
          case 'Expense':
            onTypeSelected(TransactionType.expense);
            break;
          case 'Income':
            onTypeSelected(TransactionType.income);
            break;
          case 'Loan':
            onTypeSelected(TransactionType.loan);
            break;
          case 'Cancel':
            onCancel?.call();
            break;
        }
      },
      enabled: enabled,
    );
  }
}

/// A specialized quick reply widget for category selection
class CategoryQuickReply extends StatelessWidget {
  final List<Category> categories;
  final Function(Category) onCategorySelected;
  final bool enabled;

  const CategoryQuickReply({
    super.key,
    required this.categories,
    required this.onCategorySelected,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final categoryNames = categories.map((c) => c.name).toList();
    final options = [...categoryNames, 'Other', 'Cancel'];

    return QuickReplyWidget(
      replyOptions: options,
      onReplySelected: (selectedName) {
        if (selectedName == 'Other' || selectedName == 'Cancel') {
          // Handle special cases - for now just pass the first category
          if (categories.isNotEmpty) {
            onCategorySelected(categories.first);
          }
        } else {
          // Find the category by name
          final category = categories.firstWhere(
            (c) => c.name == selectedName,
            orElse: () => categories.first,
          );
          onCategorySelected(category);
        }
      },
      enabled: enabled,
    );
  }
}

/// Specialized quick reply widget for ambiguity scenarios
class AmbiguityQuickReply extends StatelessWidget {
  final String ambiguityType;
  final List<String> options;
  final Function(String) onOptionSelected;
  final bool enabled;

  const AmbiguityQuickReply({
    super.key,
    required this.ambiguityType,
    required this.options,
    required this.onOptionSelected,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickReplyWidget.forAmbiguityType(
      ambiguityType: ambiguityType,
      options: options,
      onReplySelected: onOptionSelected,
      enabled: enabled,
    );
  }
}

/// Quick reply widget for missing amount scenarios with smart suggestions
class AmountSuggestionQuickReply extends StatelessWidget {
  final List<String> amountSuggestions;
  final Function(String) onAmountSelected;
  final bool enabled;

  const AmountSuggestionQuickReply({
    super.key,
    required this.amountSuggestions,
    required this.onAmountSelected,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickReplyWidget.forAmbiguityType(
      ambiguityType: AmbiguityType.missingAmount,
      options: amountSuggestions,
      onReplySelected: onAmountSelected,
      enabled: enabled,
    );
  }
}

/// Enhanced category suggestion quick reply with confidence indicators
class CategorySuggestionQuickReply extends StatelessWidget {
  final List<String> categorySuggestions;
  final Function(String) onCategorySelected;
  final bool enabled;

  const CategorySuggestionQuickReply({
    super.key,
    required this.categorySuggestions,
    required this.onCategorySelected,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickReplyWidget.forAmbiguityType(
      ambiguityType: AmbiguityType.ambiguousCategory,
      options: categorySuggestions,
      onReplySelected: onCategorySelected,
      enabled: enabled,
    );
  }
}

/// Transaction type selection with contextual hints
class TypeContextQuickReply extends StatelessWidget {
  final Function(String) onTypeSelected;
  final bool enabled;

  const TypeContextQuickReply({
    super.key,
    required this.onTypeSelected,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickReplyWidget.forAmbiguityType(
      ambiguityType: AmbiguityType.ambiguousType,
      options: const ['Expense', 'Income', 'Cancel'],
      onReplySelected: onTypeSelected,
      enabled: enabled,
    );
  }
}
