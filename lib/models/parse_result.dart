import 'transaction_model.dart';
import 'pending_transaction_state.dart';

/// Enum representing the status of parsing results
enum ParseStatus {
  success,
  needsCategory,
  needsType,
  needsAmountConfirmation,
  failed,
  missingAmount,
  ambiguousAmount
}

/// Constants for valid ambiguity types used in the 4-priority decision tree
class AmbiguityType {
  /// No valid amount candidates found (highest priority)
  static const String missingAmount = 'missing_amount';
  
  /// Multiple valid amount candidates found
  static const String ambiguousAmount = 'ambiguous_amount';
  
  /// Transaction type cannot be determined
  static const String ambiguousType = 'ambiguous_type';
  
  /// Category cannot be determined (lowest priority)
  static const String ambiguousCategory = 'ambiguous_category';
  
  /// List of all valid ambiguity types
  static const List<String> validTypes = [
    missingAmount,
    ambiguousAmount,
    ambiguousType,
    ambiguousCategory,
  ];
  
  /// Validates if the given ambiguity type is valid
  static bool isValid(String? ambiguityType) {
    return ambiguityType == null || validTypes.contains(ambiguityType);
  }
}

/// Data transfer object for communicating parsing results between the ML Kit parser and the UI
class ParseResult {
  final Transaction transaction;
  final ParseStatus status;
  final String? error;
  final List<double>? candidateAmounts;
  final List<String>? candidateTexts;
  
  /// The type of ambiguity detected during parsing.
  /// Must be one of the valid types defined in [AmbiguityType] constants:
  /// - [AmbiguityType.missingAmount]: No valid amount candidates found
  /// - [AmbiguityType.ambiguousAmount]: Multiple valid amount candidates found
  /// - [AmbiguityType.ambiguousType]: Transaction type cannot be determined
  /// - [AmbiguityType.ambiguousCategory]: Category cannot be determined
  /// 
  /// Can be null if no ambiguity was detected.
  final String? ambiguityType;

  const ParseResult._({
    required this.transaction,
    required this.status,
    this.error,
    this.candidateAmounts,
    this.candidateTexts,
    this.ambiguityType,
  });

  /// Creates a ParseResult with validation of the ambiguityType parameter
  factory ParseResult({
    required Transaction transaction,
    required ParseStatus status,
    String? error,
    List<double>? candidateAmounts,
    List<String>? candidateTexts,
    String? ambiguityType,
  }) {
    if (!AmbiguityType.isValid(ambiguityType)) {
      throw ArgumentError(
        'Invalid ambiguityType: $ambiguityType. Must be one of: ${AmbiguityType.validTypes}',
      );
    }
    
    return ParseResult._(
      transaction: transaction,
      status: status,
      error: error,
      candidateAmounts: candidateAmounts,
      candidateTexts: candidateTexts,
      ambiguityType: ambiguityType,
    );
  }

  /// Factory constructor for successful parsing without user input needed
  factory ParseResult.success(Transaction transaction, {String? ambiguityType}) {
    if (!AmbiguityType.isValid(ambiguityType)) {
      throw ArgumentError(
        'Invalid ambiguityType: $ambiguityType. Must be one of: ${AmbiguityType.validTypes}',
      );
    }
    
    return ParseResult._(
      transaction: transaction,
      status: ParseStatus.success,
      ambiguityType: ambiguityType,
    );
  }

  /// Factory constructor for successful parsing but category selection needed
  factory ParseResult.needsCategory(Transaction transaction, {String? ambiguityType}) {
    if (!AmbiguityType.isValid(ambiguityType)) {
      throw ArgumentError(
        'Invalid ambiguityType: $ambiguityType. Must be one of: ${AmbiguityType.validTypes}',
      );
    }
    
    return ParseResult._(
      transaction: transaction,
      status: ParseStatus.needsCategory,
      ambiguityType: ambiguityType,
    );
  }

  /// Factory constructor for successful parsing but transaction type selection needed
  factory ParseResult.needsType(Transaction partialTransaction, {String? ambiguityType}) {
    if (!AmbiguityType.isValid(ambiguityType)) {
      throw ArgumentError(
        'Invalid ambiguityType: $ambiguityType. Must be one of: ${AmbiguityType.validTypes}',
      );
    }
    
    return ParseResult._(
      transaction: partialTransaction,
      status: ParseStatus.needsType,
      ambiguityType: ambiguityType,
    );
  }

  /// Factory constructor for successful parsing but amount confirmation needed
  factory ParseResult.needsAmountConfirmation(
    Transaction partialTransaction,
    List<double> candidateAmounts,
    List<String> candidateTexts, {
    String? ambiguityType,
  }) {
    if (!AmbiguityType.isValid(ambiguityType)) {
      throw ArgumentError(
        'Invalid ambiguityType: $ambiguityType. Must be one of: ${AmbiguityType.validTypes}',
      );
    }
    
    return ParseResult._(
      transaction: partialTransaction,
      status: ParseStatus.needsAmountConfirmation,
      candidateAmounts: candidateAmounts,
      candidateTexts: candidateTexts,
      ambiguityType: ambiguityType,
    );
  }

  /// Factory constructor for failed parsing
  factory ParseResult.failed(Transaction fallbackTransaction, String error, {String? ambiguityType}) {
    if (!AmbiguityType.isValid(ambiguityType)) {
      throw ArgumentError(
        'Invalid ambiguityType: $ambiguityType. Must be one of: ${AmbiguityType.validTypes}',
      );
    }
    
    return ParseResult._(
      transaction: fallbackTransaction,
      status: ParseStatus.failed,
      error: error,
      ambiguityType: ambiguityType,
    );
  }

  /// Factory constructor for when no valid amount candidates are found
  factory ParseResult.missingAmount(Transaction fallbackTransaction, {String? ambiguityType}) {
    if (!AmbiguityType.isValid(ambiguityType)) {
      throw ArgumentError(
        'Invalid ambiguityType: $ambiguityType. Must be one of: ${AmbiguityType.validTypes}',
      );
    }
    
    return ParseResult._(
      transaction: fallbackTransaction,
      status: ParseStatus.missingAmount,
      ambiguityType: ambiguityType,
    );
  }

  /// Factory constructor for when multiple amount candidates exist and need user confirmation
  factory ParseResult.ambiguousAmount(
    Transaction partialTransaction,
    List<double> candidateAmounts,
    List<String> candidateTexts, {
    String? ambiguityType,
  }) {
    if (!AmbiguityType.isValid(ambiguityType)) {
      throw ArgumentError(
        'Invalid ambiguityType: $ambiguityType. Must be one of: ${AmbiguityType.validTypes}',
      );
    }
    
    return ParseResult._(
      transaction: partialTransaction,
      status: ParseStatus.ambiguousAmount,
      candidateAmounts: candidateAmounts,
      candidateTexts: candidateTexts,
      ambiguityType: ambiguityType,
    );
  }

  /// Helper method to check if parsing was successful
  bool get isSuccess => status == ParseStatus.success && error == null;

  /// Helper method to check if user input is required
  bool get requiresUserInput => status == ParseStatus.needsCategory ||
                               status == ParseStatus.needsType ||
                               status == ParseStatus.needsAmountConfirmation ||
                               status == ParseStatus.missingAmount ||
                               status == ParseStatus.ambiguousAmount;

  /// Helper method to check if there was an error
  bool get hasError => error != null;

  /// Helper method to check if category selection is needed
  bool get needsCategorySelection => status == ParseStatus.needsCategory;

  /// Helper method to check if transaction type selection is needed
  bool get needsTypeSelection => status == ParseStatus.needsType;

  /// Helper method to check if amount confirmation is needed
  bool get needsAmountConfirmation => status == ParseStatus.needsAmountConfirmation;

  /// Helper method to check if missing amount handling is needed
  bool get needsMissingAmountHandling => status == ParseStatus.missingAmount;

  /// Helper method to check if ambiguous amount handling is needed
  bool get needsAmbiguousAmountHandling => status == ParseStatus.ambiguousAmount;

  @override
  String toString() {
    return 'ParseResult{transaction: $transaction, status: $status, error: $error, candidateAmounts: $candidateAmounts, candidateTexts: $candidateTexts, ambiguityType: $ambiguityType}';
  }
}

/// Extension on ParseStatus to support pending transaction state management
extension ParseStatusExtension on ParseStatus {
  /// Returns true if this status indicates a soft-fail that requires user input
  bool get isSoftFail {
    return this == ParseStatus.needsType ||
           this == ParseStatus.needsCategory ||
           this == ParseStatus.needsAmountConfirmation ||
           this == ParseStatus.missingAmount ||
           this == ParseStatus.ambiguousAmount;
  }

  /// Maps this ParseStatus to the corresponding PendingStage
  /// Returns null for statuses that don't require pending state
  PendingStage? toPendingStage() {
    switch (this) {
      case ParseStatus.needsType:
        return PendingStage.typeSelection;
      case ParseStatus.needsCategory:
        return PendingStage.categorySelection;
      case ParseStatus.needsAmountConfirmation:
      case ParseStatus.ambiguousAmount:
        return PendingStage.amountConfirmation;
      case ParseStatus.missingAmount:
        return PendingStage.missingAmount;
      default:
        return null;
    }
  }

  /// Returns true if this status requires amount confirmation
  bool get requiresAmountConfirmation {
    return this == ParseStatus.needsAmountConfirmation ||
           this == ParseStatus.ambiguousAmount;
  }
}
