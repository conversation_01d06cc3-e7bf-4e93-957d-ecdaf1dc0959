import 'package:flutter/foundation.dart';
import 'parse_result.dart';
import 'transaction_model.dart';

/// Enum representing the different stages of pending transaction state
enum PendingStage {
  typeSelection,
  categorySelection,
  amountConfirmation,
  missingAmount,
}

/// Immutable class that encapsulates all pending transaction state currently
/// managed by 4 separate nullable fields in ChatScreen.
/// 
/// This class centralizes the management of incomplete transactions that require
/// user input, providing a clean interface for state management and validation.
/// 
/// The class uses factory constructors to ensure that the ParseStatus matches
/// the intended stage, preventing invalid state combinations.
/// 
/// Usage:
/// ```dart
/// // Create pending state for category selection
/// final pendingState = PendingTransactionState.forCategorySelection(
///   originalText,
///   parseResult,
/// );
/// 
/// // Check the stage
/// if (pendingState.isCategorySelection) {
///   // Handle category selection UI
/// }
/// 
/// // Access transaction data
/// final transaction = pendingState.transaction;
/// final candidateAmounts = pendingState.candidateAmounts;
/// ```
@immutable
class PendingTransactionState {
  /// The parse result containing transaction data and parsing status
  final ParseResult parseResult;
  
  /// The original user input text that triggered this pending state
  final String originalText;
  
  /// The stage discriminator indicating the type of pending state
  final PendingStage stage;

  const PendingTransactionState._({
    required this.parseResult,
    required this.originalText,
    required this.stage,
  });

  /// Factory constructor for type selection pending state
  /// 
  /// Validates that parseResult.status is [ParseStatus.needsType]
  /// 
  /// Throws [ArgumentError] if the ParseStatus doesn't match the expected stage.
  factory PendingTransactionState.forTypeSelection(
    String originalText,
    ParseResult parseResult,
  ) {
    if (parseResult.status != ParseStatus.needsType) {
      throw ArgumentError(
        'ParseResult status must be needsType for type selection, '
        'but was ${parseResult.status}',
      );
    }
    
    return PendingTransactionState._(
      parseResult: parseResult,
      originalText: originalText,
      stage: PendingStage.typeSelection,
    );
  }

  /// Factory constructor for category selection pending state
  /// 
  /// Validates that parseResult.status is [ParseStatus.needsCategory]
  /// 
  /// Throws [ArgumentError] if the ParseStatus doesn't match the expected stage.
  factory PendingTransactionState.forCategorySelection(
    String originalText,
    ParseResult parseResult,
  ) {
    if (parseResult.status != ParseStatus.needsCategory) {
      throw ArgumentError(
        'ParseResult status must be needsCategory for category selection, '
        'but was ${parseResult.status}',
      );
    }
    
    return PendingTransactionState._(
      parseResult: parseResult,
      originalText: originalText,
      stage: PendingStage.categorySelection,
    );
  }

  /// Factory constructor for amount confirmation pending state
  /// 
  /// Validates that parseResult.status is [ParseStatus.needsAmountConfirmation]
  /// or [ParseStatus.ambiguousAmount]
  /// 
  /// Throws [ArgumentError] if the ParseStatus doesn't match the expected stage.
  factory PendingTransactionState.forAmountConfirmation(
    String originalText,
    ParseResult parseResult,
  ) {
    if (parseResult.status != ParseStatus.needsAmountConfirmation &&
        parseResult.status != ParseStatus.ambiguousAmount) {
      throw ArgumentError(
        'ParseResult status must be needsAmountConfirmation or ambiguousAmount '
        'for amount confirmation, but was ${parseResult.status}',
      );
    }
    
    return PendingTransactionState._(
      parseResult: parseResult,
      originalText: originalText,
      stage: PendingStage.amountConfirmation,
    );
  }

  /// Factory constructor for missing amount pending state
  /// 
  /// Validates that parseResult.status is [ParseStatus.missingAmount]
  /// 
  /// Throws [ArgumentError] if the ParseStatus doesn't match the expected stage.
  factory PendingTransactionState.forMissingAmount(
    String originalText,
    ParseResult parseResult,
  ) {
    if (parseResult.status != ParseStatus.missingAmount) {
      throw ArgumentError(
        'ParseResult status must be missingAmount for missing amount, '
        'but was ${parseResult.status}',
      );
    }
    
    return PendingTransactionState._(
      parseResult: parseResult,
      originalText: originalText,
      stage: PendingStage.missingAmount,
    );
  }

  // Convenience getters for stage checking
  
  /// Returns true if this pending state is for type selection
  bool get isTypeSelection => stage == PendingStage.typeSelection;
  
  /// Returns true if this pending state is for category selection
  bool get isCategorySelection => stage == PendingStage.categorySelection;
  
  /// Returns true if this pending state is for amount confirmation
  bool get isAmountConfirmation => stage == PendingStage.amountConfirmation;
  
  /// Returns true if this pending state is for missing amount
  bool get isMissingAmount => stage == PendingStage.missingAmount;

  // Convenience getters for accessing ParseResult data
  
  /// Returns the transaction from the parse result
  Transaction get transaction => parseResult.transaction;
  
  /// Returns the candidate amounts from the parse result (may be null)
  List<double>? get candidateAmounts => parseResult.candidateAmounts;
  
  /// Returns the candidate texts from the parse result (may be null)
  List<String>? get candidateTexts => parseResult.candidateTexts;
  
  /// Returns the ambiguity type from the parse result (may be null)
  String? get ambiguityType => parseResult.ambiguityType;

  /// Creates a copy of this pending state with updated fields
  /// 
  /// Maintains validation constraints by preserving the relationship between
  /// stage and parseResult.status
  PendingTransactionState copyWith({
    ParseResult? parseResult,
    String? originalText,
    PendingStage? stage,
  }) {
    return PendingTransactionState._(
      parseResult: parseResult ?? this.parseResult,
      originalText: originalText ?? this.originalText,
      stage: stage ?? this.stage,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is PendingTransactionState &&
           other.parseResult == parseResult &&
           other.originalText == originalText &&
           other.stage == stage;
  }

  @override
  int get hashCode => Object.hash(parseResult, originalText, stage);

  @override
  String toString() {
    return 'PendingTransactionState{'
           'parseResult: $parseResult, '
           'originalText: "$originalText", '
           'stage: $stage'
           '}';
  }
}
