/// Enum representing the source of an amount candidate
enum AmountSource {
  mlKit,
  rawNumberFinder,
}

/// Data class representing a potential amount found in text
/// This standardizes how we represent amount candidates from different sources
class AmountCandidate {
  final double amount;
  final String? currency;
  final int start;
  final int end;
  final String sourceText;
  final AmountSource source;

  const AmountCandidate({
    required this.amount,
    this.currency,
    required this.start,
    required this.end,
    required this.sourceText,
    required this.source,
  });

  /// Create an AmountCandidate from ML Kit entity
  factory AmountCandidate.fromMLKit({
    required double amount,
    String? currency,
    required int start,
    required int end,
    required String sourceText,
  }) {
    return AmountCandidate(
      amount: amount,
      currency: currency,
      start: start,
      end: end,
      sourceText: sourceText,
      source: AmountSource.mlKit,
    );
  }

  /// Create an AmountCandidate from raw number finder
  factory AmountCandidate.fromRawNumberFinder({
    required double amount,
    String? currency,
    required int start,
    required int end,
    required String sourceText,
  }) {
    return AmountCandidate(
      amount: amount,
      currency: currency,
      start: start,
      end: end,
      sourceText: sourceText,
      source: AmountSource.rawNumberFinder,
    );
  }

  /// Convert to Map for backward compatibility with existing code
  Map<String, dynamic> toMap() {
    return {
      'amount': amount,
      'currency': currency,
      'start': start,
      'end': end,
      'sourceText': sourceText,
      'source': source.name,
    };
  }

  /// Create AmountCandidate from Map for backward compatibility
  factory AmountCandidate.fromMap(Map<String, dynamic> map) {
    return AmountCandidate(
      amount: (map['amount'] as num).toDouble(),
      currency: map['currency'] as String?,
      start: map['start'] as int,
      end: map['end'] as int,
      sourceText: map['sourceText'] as String,
      source: AmountSource.values.byName(map['source'] as String),
    );
  }

  /// Equality comparison based on amount, start, and end positions
  /// Two candidates are considered equal if they have the same amount
  /// and overlap in position (within tolerance)
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AmountCandidate) return false;

    // Consider candidates equal if they have the same amount
    // and their positions overlap (within a small tolerance)
    const tolerance = 0.01; // For floating point comparison
    const positionTolerance = 2; // Allow small position differences

    final amountMatch = (amount - other.amount).abs() < tolerance;
    final positionOverlap = (start - other.start).abs() <= positionTolerance ||
                           (end - other.end).abs() <= positionTolerance ||
                           (start <= other.end && end >= other.start);

    return amountMatch && positionOverlap;
  }

  @override
  int get hashCode {
    // Use amount rounded to avoid floating point precision issues
    // Use broader position grouping to match equality logic with tolerance
    final roundedAmount = (amount * 100).round();
    final normalizedStart = start ~/ 5; // Group positions with broader tolerance
    final normalizedEnd = end ~/ 5;
    return Object.hash(roundedAmount, normalizedStart, normalizedEnd);
  }

  /// String representation for debugging
  @override
  String toString() {
    final currencyStr = currency != null ? ' $currency' : '';
    return 'AmountCandidate(amount: $amount$currencyStr, '
           'position: $start-$end, text: "$sourceText", source: ${source.name})';
  }

  /// Create a copy with updated fields
  AmountCandidate copyWith({
    double? amount,
    String? currency,
    int? start,
    int? end,
    String? sourceText,
    AmountSource? source,
  }) {
    return AmountCandidate(
      amount: amount ?? this.amount,
      currency: currency ?? this.currency,
      start: start ?? this.start,
      end: end ?? this.end,
      sourceText: sourceText ?? this.sourceText,
      source: source ?? this.source,
    );
  }

  /// Check if this candidate represents the same amount as another
  /// (ignoring position and source)
  bool hasSameAmount(AmountCandidate other) {
    const tolerance = 0.01;
    return (amount - other.amount).abs() < tolerance;
  }

  /// Check if this candidate's position overlaps with another
  bool overlapsPosition(AmountCandidate other) {
    return start <= other.end && end >= other.start;
  }

  /// Get the text span that this candidate covers
  String getTextSpan(String fullText) {
    if (start < 0 || end > fullText.length || start >= end) {
      return sourceText; // Fallback to stored source text
    }
    return fullText.substring(start, end);
  }
}
