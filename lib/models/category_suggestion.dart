/// Data model representing a category suggestion with confidence scoring
class CategorySuggestion {
  final String categoryId;
  final double confidence;
  final String matchReason;
  final CategorySuggestionSource source;

  const CategorySuggestion({
    required this.categoryId,
    required this.confidence,
    required this.matchReason,
    required this.source,
  });

  factory CategorySuggestion.fromJson(Map<String, dynamic> json) {
    return CategorySuggestion(
      categoryId: json['categoryId'],
      confidence: json['confidence'].toDouble(),
      matchReason: json['matchReason'],
      source: CategorySuggestionSource.values.byName(json['source']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'categoryId': categoryId,
      'confidence': confidence,
      'matchReason': matchReason,
      'source': source.name,
    };
  }

  CategorySuggestion copyWith({
    String? categoryId,
    double? confidence,
    String? matchReason,
    CategorySuggestionSource? source,
  }) {
    return CategorySuggestion(
      categoryId: categoryId ?? this.categoryId,
      confidence: confidence ?? this.confidence,
      matchReason: matchReason ?? this.matchReason,
      source: source ?? this.source,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CategorySuggestion &&
        other.categoryId == categoryId &&
        other.confidence == confidence &&
        other.matchReason == matchReason &&
        other.source == source;
  }

  @override
  int get hashCode {
    return categoryId.hashCode ^
        confidence.hashCode ^
        matchReason.hashCode ^
        source.hashCode;
  }

  @override
  String toString() {
    return 'CategorySuggestion(categoryId: $categoryId, confidence: $confidence, matchReason: $matchReason, source: $source)';
  }
}

/// Enum representing the source of a category suggestion
enum CategorySuggestionSource {
  /// Suggestion from learned user associations
  learned,
  
  /// Suggestion from keyword matching
  keyword,
  
  /// Suggestion from recent transaction patterns
  recent,
  
  /// Suggestion from transaction frequency analysis
  frequency,
}

/// Container for multiple category suggestions with metadata
class CategorySuggestionResult {
  final List<CategorySuggestion> suggestions;
  final String? fallbackCategoryId;

  const CategorySuggestionResult({
    required this.suggestions,
    this.fallbackCategoryId,
  });

  /// Check if any suggestion has high confidence (>= 0.8)
  bool get hasHighConfidenceSuggestion => suggestions.any((s) => s.confidence >= 0.8);

  /// Get the top suggestion if confidence is high enough
  CategorySuggestion? get topSuggestion {
    if (suggestions.isEmpty) return null;
    final top = suggestions.first;
    return top.confidence >= 0.7 ? top : null;
  }

  /// Get suggestions above a certain confidence threshold
  List<CategorySuggestion> getSuggestionsAboveThreshold(double threshold) {
    return suggestions.where((s) => s.confidence >= threshold).toList();
  }

  /// Check if there's a clear winner (significantly higher confidence than others)
  bool get hasClearWinner {
    if (suggestions.length < 2) return suggestions.isNotEmpty;
    return suggestions.first.confidence - suggestions[1].confidence >= 0.2;
  }

  factory CategorySuggestionResult.fromJson(Map<String, dynamic> json) {
    return CategorySuggestionResult(
      suggestions: (json['suggestions'] as List)
          .map((s) => CategorySuggestion.fromJson(s))
          .toList(),
      fallbackCategoryId: json['fallbackCategoryId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'suggestions': suggestions.map((s) => s.toJson()).toList(),
      'fallbackCategoryId': fallbackCategoryId,
      'hasHighConfidenceSuggestion': hasHighConfidenceSuggestion,
    };
  }

  @override
  String toString() {
    return 'CategorySuggestionResult(suggestions: ${suggestions.length}, hasHighConfidence: $hasHighConfidenceSuggestion)';
  }
}
