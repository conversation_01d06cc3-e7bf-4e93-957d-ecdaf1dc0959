import 'dart:convert';

/// Data model representing localization patterns for transaction parsing
class LocalizationData {
  final String locale;
  final String decimalSeparator;
  final String thousandsSeparator;
  final List<String> expenseKeywords;
  final List<String> incomeKeywords;
  final List<String> loanKeywords;
  final List<String> currencySymbols;
  final Map<String, String> specialPatterns;

  const LocalizationData({
    required this.locale,
    required this.decimalSeparator,
    required this.thousandsSeparator,
    required this.expenseKeywords,
    required this.incomeKeywords,
    required this.loanKeywords,
    required this.currencySymbols,
    required this.specialPatterns,
  });

  /// Creates LocalizationData from JSON map
  factory LocalizationData.fromJson(Map<String, dynamic> json) {
    try {
      // Validate required fields
      final locale = json['locale'] as String?;
      if (locale == null || locale.isEmpty) {
        throw FormatException('Missing or empty locale field');
      }

      final decimalSeparator = json['decimal_separator'] as String?;
      if (decimalSeparator == null || decimalSeparator.isEmpty) {
        throw FormatException('Missing or empty decimal_separator field');
      }

      final thousandsSeparator = json['thousands_separator'] as String?;
      if (thousandsSeparator == null || thousandsSeparator.isEmpty) {
        throw FormatException('Missing or empty thousands_separator field');
      }

      // Parse keyword arrays with validation
      final expenseKeywords = _parseStringList(json['expense_keywords'], 'expense_keywords');
      final incomeKeywords = _parseStringList(json['income_keywords'], 'income_keywords');
      final loanKeywords = _parseStringList(json['loan_keywords'], 'loan_keywords');
      final currencySymbols = _parseStringList(json['currency_symbols'], 'currency_symbols');

      // Parse special patterns map
      final specialPatternsRaw = json['special_patterns'] as Map<String, dynamic>?;
      final specialPatterns = <String, String>{};
      if (specialPatternsRaw != null) {
        for (final entry in specialPatternsRaw.entries) {
          if (entry.value is String) {
            specialPatterns[entry.key] = entry.value as String;
          }
        }
      }

      return LocalizationData(
        locale: locale,
        decimalSeparator: decimalSeparator,
        thousandsSeparator: thousandsSeparator,
        expenseKeywords: expenseKeywords,
        incomeKeywords: incomeKeywords,
        loanKeywords: loanKeywords,
        currencySymbols: currencySymbols,
        specialPatterns: specialPatterns,
      );
    } catch (e) {
      throw FormatException('Failed to parse LocalizationData: $e');
    }
  }

  /// Creates LocalizationData from JSON string
  factory LocalizationData.fromJsonString(String jsonString) {
    try {
      final Map<String, dynamic> json = jsonDecode(jsonString);
      return LocalizationData.fromJson(json);
    } catch (e) {
      throw FormatException('Failed to parse JSON string: $e');
    }
  }

  /// Helper method to parse and validate string lists
  static List<String> _parseStringList(dynamic value, String fieldName) {
    if (value == null) {
      throw FormatException('Missing $fieldName field');
    }

    if (value is! List) {
      throw FormatException('$fieldName must be a list');
    }

    final result = <String>[];
    for (int i = 0; i < value.length; i++) {
      final item = value[i];
      if (item is! String) {
        throw FormatException('$fieldName[$i] must be a string, got ${item.runtimeType}');
      }
      if (item.isEmpty) {
        throw FormatException('$fieldName[$i] cannot be empty');
      }
      result.add(item);
    }

    if (result.isEmpty) {
      throw FormatException('$fieldName cannot be empty');
    }

    return result;
  }

  /// Converts LocalizationData to JSON map
  Map<String, dynamic> toJson() {
    return {
      'locale': locale,
      'decimal_separator': decimalSeparator,
      'thousands_separator': thousandsSeparator,
      'expense_keywords': expenseKeywords,
      'income_keywords': incomeKeywords,
      'loan_keywords': loanKeywords,
      'currency_symbols': currencySymbols,
      'special_patterns': specialPatterns,
    };
  }

  /// Converts LocalizationData to JSON string
  String toJsonString() {
    return jsonEncode(toJson());
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! LocalizationData) return false;

    return locale == other.locale &&
        decimalSeparator == other.decimalSeparator &&
        thousandsSeparator == other.thousandsSeparator &&
        _listEquals(expenseKeywords, other.expenseKeywords) &&
        _listEquals(incomeKeywords, other.incomeKeywords) &&
        _listEquals(loanKeywords, other.loanKeywords) &&
        _listEquals(currencySymbols, other.currencySymbols) &&
        _mapEquals(specialPatterns, other.specialPatterns);
  }

  @override
  int get hashCode {
    return Object.hash(
      locale,
      decimalSeparator,
      thousandsSeparator,
      Object.hashAll(expenseKeywords),
      Object.hashAll(incomeKeywords),
      Object.hashAll(loanKeywords),
      Object.hashAll(currencySymbols),
      Object.hashAll(specialPatterns.entries.map((e) => Object.hash(e.key, e.value))),
    );
  }

  @override
  String toString() {
    return 'LocalizationData(locale: $locale, expenseKeywords: ${expenseKeywords.length}, '
        'incomeKeywords: ${incomeKeywords.length}, loanKeywords: ${loanKeywords.length})';
  }

  /// Helper method to compare lists
  bool _listEquals<T>(List<T> a, List<T> b) {
    if (a.length != b.length) return false;
    for (int i = 0; i < a.length; i++) {
      if (a[i] != b[i]) return false;
    }
    return true;
  }

  /// Helper method to compare maps
  bool _mapEquals<K, V>(Map<K, V> a, Map<K, V> b) {
    if (a.length != b.length) return false;
    for (final key in a.keys) {
      if (!b.containsKey(key) || a[key] != b[key]) return false;
    }
    return true;
  }
}
