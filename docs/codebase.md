# Codebase Structure

This document provides a detailed overview of the DreamFlow application codebase organization.

**Project Name**: DreamFlow (configured as `dreamflow` in pubspec.yaml)
**Display Name**: Money Lover Chat (used in UI and main application class)
**Description**: A Flutter application for managing finances with transaction tracking, categorization, statistics, and a chat interface for transaction entry.

## Project Overview

The application follows a layered architecture with clear separation of concerns:

- **Presentation Layer**: Screens and UI components
- **Business Logic Layer**: Providers for state management
- **Service Layer**: Encapsulates business logic, parsing, and data access
- **Data Layer**: Data models and persistence services

## Directory Structure

### Root Level

```
dreamflow/
├── android/          # Android-specific configuration
├── ios/              # iOS-specific configuration
├── lib/              # Main Dart code
├── assets/           # Application assets (e.g., localization files)
│   └── l10n/
│       ├── en.json
│       └── es.json
├── docs/             # Project documentation
├── scripts/          # Build and utility scripts
├── pubspec.yaml      # Dependencies and project configuration
└── ...
```

### Main Application Code (lib/)

```
lib/
├── models/           # Data models
│   ├── transaction_model.dart
│   ├── parse_result.dart
│   ├── pending_transaction_state.dart
│   └── ...
├── services/         # Business logic and data services
│   ├── parser/       # Hybrid ML parsing module
│   │   ├── transaction_parsing_service.dart
│   │   ├── parsing_strategy.dart
│   │   ├── parsing_context.dart
│   │   ├── parsing_config.dart
│   │   ├── strategies/
│   │   │   ├── learned_association_strategy.dart
│   │   │   ├── mlkit_strategy.dart
│   │   │   └── fallback_regex_strategy.dart
│   │   ├── learned_association_service.dart
│   │   ├── category_finder_service.dart
│   │   ├── ...
│   │   └── mlkit_parser_service.dart      # Deprecated Façade
│   ├── storage_service.dart
│   └── localization_service.dart
├── screens/          # UI screens
│   ├── chat_screen.dart
│   └── ...
├── widgets/          # Reusable UI components
│   └── ...
├── utils/            # Utility functions
│   └── ...
├── navigation/       # Navigation logic
│   └── app_navigation.dart
├── main.dart         # Application entry point
└── ...
```

## Key Components

### Models (`lib/models/`)

- **transaction_model.dart**: Contains the core data models: `Transaction`, `Category`, `TransactionType`, `ChatMessage`, and `TransactionProvider`.
- **parse_result.dart**: A data transfer object from the parser service to the UI, indicating the outcome of a parsing attempt (`ParseStatus`).
- **pending_transaction_state.dart**: Encapsulates the state of an ambiguous transaction that requires user input (a "soft fail").
- **amount_candidate.dart**: Represents a potential amount found during parsing.

### Services (`lib/services/`)

- **storage_service.dart**: Manages local data persistence using `shared_preferences`.
- **localization_service.dart**: Loads and provides localization data from JSON files for the `FallbackRegexStrategy`.
- **parser/transaction_parsing_service.dart**: The primary parsing service. It orchestrates the parsing pipeline by executing a series of strategies (`LearnedAssociationStrategy`, `MlKitStrategy`, `FallbackRegexStrategy`).
- **parser/strategies/mlkit_strategy.dart**: A parsing strategy that uses a "Trust but Verify" system, combining on-device ML Kit entity extraction with a `RawNumberFinder` to handle ambiguity.
- **parser/strategies/fallback_regex_strategy.dart**: A parsing strategy that uses a localizable, regex-based parser as a safety net.
- **parser/learned_association_service.dart**: The app's learning system. Stores and retrieves user corrections to improve parsing speed and accuracy over time.
- **parser/mlkit_parser_service.dart**: A deprecated façade that points to the new `TransactionParsingService` for backward compatibility.

### Utilities (`lib/utils/`)

- **currency_utils.dart**: Provides helper functions for currency formatting and handling.
- **amount_utils.dart**: Contains utilities for amount parsing and validation.
- **raw_number_finder.dart**: A utility that extracts all raw numeric values from text, a key part of the "Trust but Verify" strategy.
- **startup_timer.dart**: A utility for measuring app startup performance.

### Screens (`lib/screens/`)

- **chat_screen.dart**: The main chat interface for entering transactions. It manages the "soft fail" conversation flow using the `PendingTransactionState` model.
- **categories_screen.dart**: Allows users to manage transaction categories.
- **settings_screen.dart**: Allows users to configure app settings.
- **statistics_screen.dart**: Displays financial statistics and data visualizations.

### Widgets (`lib/widgets/`)

- **transaction_message.dart**: A widget to display a saved transaction in the chat.
- **category_picker_dialog.dart**: A dialog for manually selecting a category.
- **quick_reply_widget.dart**: A widget that displays interactive buttons in a chat message for ambiguity resolution.
- **transaction_edit_dialog.dart**: A dialog for editing existing transactions.

### Multimedia Support (`lib/`)

- **audio_recorder.dart**: Handles audio recording functionality.
- **file_upload.dart**: Provides file upload utilities.
- **image_upload.dart**: Manages image capture and selection.
- **video_recorder.dart**: Handles video recording and playback.