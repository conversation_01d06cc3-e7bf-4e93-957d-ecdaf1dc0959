# Codebase Structure

This document provides a detailed overview of the DreamFlow application codebase organization.

**Project Name**: DreamFlow (configured as `dreamflow` in pubspec.yaml)
**Display Name**: Money Lover Chat (used in UI and main application class)
**Description**: A Flutter application for managing finances with transaction tracking, categorization, statistics, and a chat interface for transaction entry using ML-powered parsing.

## Project Overview

The application follows a layered architecture with clear separation of concerns:

- **Presentation Layer**: Screens and UI components
- **Business Logic Layer**: Providers for state management
- **Service Layer**: Encapsulates business logic, parsing, and data access
- **Data Layer**: Data models and persistence services

## Technology Stack

- **Framework**: Flutter 3.0.0+ with Dart SDK 3.0.0+
- **State Management**: Provider pattern
- **Local Storage**: SharedPreferences
- **ML/AI**: Google ML Kit Entity Extraction
- **Backend**: Supabase (configured but optional)
- **Charts**: FL Chart for statistics visualization
- **Multimedia**: Image/video/audio recording and file upload support
- **Error Handling**: Firebase Crashlytics
- **Logging**: Custom logging service with performance monitoring

## Directory Structure

### Root Level

```
money_lover_chat/
├── android/          # Android-specific configuration and build files
├── ios/              # iOS-specific configuration and build files
├── web/              # Web platform support files
├── lib/              # Main Dart application code
├── test/             # Unit and integration tests
├── assets/           # Application assets (localization files)
│   └── l10n/
│       ├── en.json   # English localization data
│       └── es.json   # Spanish localization data
├── docs/             # Comprehensive project documentation
├── scripts/          # Build and utility scripts
├── build/            # Generated build artifacts (ignored by git)
├── pubspec.yaml      # Dependencies and project configuration
├── analysis_options.yaml  # Dart analysis configuration
├── devtools_options.yaml  # Flutter DevTools configuration
└── .gitignore        # Git ignore patterns
```

### Main Application Code (lib/)

```
lib/
├── main.dart                    # Application entry point with startup optimization
├── theme.dart                   # Theme configuration and provider
├── models/                      # Data models and DTOs
│   ├── transaction_model.dart   # Core Transaction, Category, TransactionType models
│   ├── parse_result.dart        # ParseResult and ParseStatus for parsing outcomes
│   ├── pending_transaction_state.dart  # State management for ambiguous transactions
│   ├── amount_candidate.dart    # Amount parsing candidate representation
│   ├── category_suggestion.dart # Category suggestion model
│   └── localization_data.dart   # Localization data structure
├── services/                    # Business logic and data services
│   ├── storage_service.dart     # Local data persistence with SharedPreferences
│   ├── localization_service.dart # Loads locale-specific parsing keywords
│   ├── logging_service.dart     # Application logging and performance monitoring
│   ├── error_handling_service.dart # Global error handling and reporting
│   ├── error_notification_service.dart # User-facing error notifications
│   ├── network_service.dart     # Network utilities and connectivity
│   └── parser/                  # Advanced ML-powered parsing module
│       ├── transaction_parsing_service.dart # Main parsing orchestrator
│       ├── parsing_strategy.dart # Abstract strategy interface
│       ├── parsing_context.dart  # Parsing context and metadata
│       ├── parsing_config.dart   # Configuration for parsing behavior
│       ├── parse_logger.dart     # Specialized logging for parsing operations
│       ├── entity_extractor_base.dart # Abstract ML entity extractor
│       ├── real_entity_extractor.dart # ML Kit implementation
│       ├── ml_kit_error_handler.dart # ML Kit specific error handling
│       ├── learned_association_service.dart # Learning system for user corrections
│       ├── learned_category_storage.dart # Storage for learned category associations
│       ├── category_finder_service.dart # Category detection and suggestion
│       ├── category_keyword_map.dart # Category keyword mappings
│       ├── fallback_parser_service.dart # Regex-based fallback parser
│       ├── mlkit_parser_service.dart # Legacy facade (deprecated)
│       └── strategies/          # Parsing strategy implementations
│           ├── learned_association_strategy.dart # Fast learned pattern matching
│           ├── mlkit_strategy.dart # ML Kit entity extraction with Trust-but-Verify
│           └── fallback_regex_strategy.dart # Regex-based safety net
├── screens/                     # UI screens and main interfaces
│   ├── chat_screen.dart         # Main chat interface for transaction entry
│   ├── categories_screen.dart   # Category management interface
│   ├── settings_screen.dart     # Application settings and preferences
│   └── statistics_screen.dart   # Financial statistics and data visualization
├── widgets/                     # Reusable UI components
│   ├── transaction_message.dart # Chat message display for transactions
│   ├── category_picker_dialog.dart # Category selection dialog
│   ├── quick_reply_widget.dart  # Interactive buttons for ambiguity resolution
│   ├── transaction_edit_dialog.dart # Transaction editing interface
│   └── error_widgets.dart       # Error display components
├── navigation/                  # Navigation and routing
│   └── app_navigation.dart      # Main navigation controller with bottom tabs
├── utils/                       # Utility functions and helpers
│   ├── currency_utils.dart      # Currency formatting and handling
│   ├── amount_utils.dart        # Amount parsing and validation utilities
│   ├── raw_number_finder.dart   # Numeric value extraction for Trust-but-Verify
│   └── startup_timer.dart       # Performance monitoring for app startup
├── audio_recorder.dart          # Audio recording functionality
├── video_recorder.dart          # Video recording and playback
├── image_upload.dart            # Image capture and selection
└── file_upload.dart             # General file upload utilities
```

## Key Components

### Models (`lib/models/`)

- **transaction_model.dart**: Contains the core data models: `Transaction`, `Category`, `TransactionType`, `ChatMessage`, and `TransactionProvider`. The Transaction model includes comprehensive fields for amount, type, category, date, description, tags, and currency code.
- **parse_result.dart**: A data transfer object from the parser service to the UI, indicating the outcome of a parsing attempt with `ParseStatus` enum (success, failed, needsCategory, needsAmountConfirmation) and `AmbiguityType` for different types of parsing ambiguity.
- **pending_transaction_state.dart**: Encapsulates the state of an ambiguous transaction that requires user input (a "soft fail"), managing the conversation flow for category selection and amount confirmation.
- **amount_candidate.dart**: Represents a potential amount found during parsing, including the value, confidence score, and source information for the Trust-but-Verify system.
- **category_suggestion.dart**: Represents category suggestions with confidence scores for intelligent category recommendation.
- **localization_data.dart**: Structure for locale-specific parsing keywords and patterns used by the fallback regex strategy.

### Core Services (`lib/services/`)

- **storage_service.dart**: Manages local data persistence using `shared_preferences`, handling transactions, categories, settings, and learned associations with proper error handling and data validation.
- **localization_service.dart**: Loads and provides localization data from JSON files for the `FallbackRegexStrategy`, supporting multiple languages (English, Spanish) with extensible keyword mapping.
- **logging_service.dart**: Comprehensive application logging with performance monitoring, error tracking, and configurable log levels for debugging and production use.
- **error_handling_service.dart**: Global error handling and reporting system with Firebase Crashlytics integration for crash reporting and error analytics.
- **error_notification_service.dart**: User-facing error notifications and messaging system for graceful error communication.
- **network_service.dart**: Network utilities and connectivity management for optional backend integration.

### Parsing Module (`lib/services/parser/`)

- **transaction_parsing_service.dart**: The primary parsing orchestrator implementing the Strategy pattern. Executes parsing strategies in sequence: `LearnedAssociationStrategy` → `MlKitStrategy` → `FallbackRegexStrategy`.
- **parsing_strategy.dart**: Abstract base class defining the strategy interface for pluggable parsing implementations.
- **parsing_context.dart**: Encapsulates parsing context including input text, locale, and metadata for strategy execution.
- **parsing_config.dart**: Configuration management for parsing behavior, thresholds, and strategy parameters.
- **parse_logger.dart**: Specialized logging system for parsing operations with detailed tracing and performance metrics.

### ML and Entity Extraction

- **entity_extractor_base.dart**: Abstract interface for ML entity extraction implementations.
- **real_entity_extractor.dart**: Google ML Kit implementation for on-device entity extraction with proper resource management.
- **ml_kit_error_handler.dart**: Specialized error handling for ML Kit operations and model management.

### Learning and Intelligence

- **learned_association_service.dart**: The app's learning system that stores and retrieves user corrections to improve parsing speed and accuracy over time.
- **learned_category_storage.dart**: Persistent storage for learned category associations with pattern matching.
- **category_finder_service.dart**: Intelligent category detection and suggestion system using keyword matching and learned patterns.
- **category_keyword_map.dart**: Configurable keyword mappings for category detection across different languages.

### Parsing Strategies

- **strategies/learned_association_strategy.dart**: Fast learned pattern matching strategy that bypasses ML processing for known patterns.
- **strategies/mlkit_strategy.dart**: ML Kit entity extraction strategy implementing the "Trust but Verify" system with ambiguity resolution.
- **strategies/fallback_regex_strategy.dart**: Regex-based safety net strategy using localizable patterns for reliable parsing fallback.

### Legacy Components

- **fallback_parser_service.dart**: Legacy regex-based parser maintained for compatibility.
- **mlkit_parser_service.dart**: Deprecated façade that points to the new `TransactionParsingService` for backward compatibility.

### Utilities (`lib/utils/`)

- **currency_utils.dart**: Comprehensive currency formatting and handling utilities with support for multiple currencies, locale-specific formatting, and currency symbol detection.
- **amount_utils.dart**: Advanced amount parsing and validation utilities with support for different number formats, decimal separators, and currency symbols.
- **raw_number_finder.dart**: Sophisticated numeric value extraction utility that finds all potential amounts in text, a critical component of the "Trust but Verify" strategy for handling ambiguous amounts.
- **startup_timer.dart**: Performance monitoring utility for measuring app startup times, initialization phases, and identifying performance bottlenecks.

### User Interface (`lib/screens/`)

- **chat_screen.dart**: The main conversational interface for transaction entry. Manages the complete "soft fail" conversation flow using the `PendingTransactionState` model, handles multimedia input, and provides real-time parsing feedback.
- **categories_screen.dart**: Comprehensive category management interface allowing users to create, edit, delete, and organize transaction categories with color coding and icon selection.
- **settings_screen.dart**: Application settings and preferences interface including theme selection, default currency, parsing sensitivity, and data management options.
- **statistics_screen.dart**: Advanced financial statistics and data visualization interface using FL Chart for interactive graphs, spending analysis, and financial insights.

### UI Components (`lib/widgets/`)

- **transaction_message.dart**: Sophisticated chat message widget for displaying saved transactions with formatting, editing capabilities, and visual indicators.
- **category_picker_dialog.dart**: Interactive dialog for category selection with search functionality, recent categories, and visual category representation.
- **quick_reply_widget.dart**: Dynamic interactive button widget for ambiguity resolution in chat messages, supporting amount confirmation and category selection.
- **transaction_edit_dialog.dart**: Comprehensive transaction editing interface with form validation, date/time pickers, and category selection.
- **error_widgets.dart**: Standardized error display components for consistent error presentation across the application.

### Navigation (`lib/navigation/`)

- **app_navigation.dart**: Main navigation controller implementing bottom tab navigation with four primary sections: Chat, Statistics, Categories, and Settings. Handles deep linking and navigation state management.

### Theme and Styling (`lib/`)

- **theme.dart**: Comprehensive theme management with Material 3 design system, dark/light mode support, custom color schemes, and theme persistence.

### Multimedia Support (`lib/`)

- **audio_recorder.dart**: Advanced audio recording functionality with format selection, quality settings, and playback capabilities for voice transaction input.
- **file_upload.dart**: General file upload utilities supporting multiple file types, progress tracking, and error handling for receipt and document attachment.
- **image_upload.dart**: Image capture and selection functionality with camera integration, gallery access, and image processing for receipt scanning.
- **video_recorder.dart**: Video recording and playback capabilities with quality controls and format optimization for transaction documentation.

## Testing Structure (`test/`)

The project includes comprehensive testing with organized test directories:

- **Unit Tests**: Individual component testing for models, services, and utilities
- **Integration Tests**: End-to-end testing for parsing workflows and user interactions
- **Widget Tests**: UI component testing for screens and widgets
- **Performance Tests**: Startup performance and parsing performance validation
- **Mock Data**: Comprehensive test data sets for various scenarios and edge cases