# Codebase Structure

This document provides a detailed overview of the DreamFlow application codebase organization.

**Project Name**: DreamFlow (configured as `dreamflow` in pubspec.yaml)
**Display Name**: Money Lover Chat (used in UI and main application class)
**Description**: A Flutter application for managing finances with transaction tracking, categorization, statistics, and a chat interface for transaction entry using ML-powered parsing.

## Project Overview

The application follows a layered architecture with clear separation of concerns:

- **Presentation Layer**: Screens and UI components
- **Business Logic Layer**: Providers for state management
- **Service Layer**: Encapsulates business logic, parsing, and data access
- **Data Layer**: Data models and persistence services

## Technology Stack

- **Framework**: Flutter 3.0.0+ with Dart SDK 3.0.0+
- **State Management**: Provider pattern
- **Local Storage**: SharedPreferences
- **ML/AI**: Google ML Kit Entity Extraction
- **Backend**: Supabase (configured but optional)
- **Charts**: FL Chart for statistics visualization
- **Multimedia**: Image/video/audio recording and file upload support
- **Error Handling**: Firebase Crashlytics
- **Logging**: Custom logging service with performance monitoring

## Directory Structure

### Root Level

```
money_lover_chat/
├── android/          # Android-specific configuration and build files
├── ios/              # iOS-specific configuration and build files
├── web/              # Web platform support files
├── lib/              # Main Dart application code
├── test/             # Unit and integration tests
├── assets/           # Application assets (localization files)
│   └── l10n/
│       ├── en.json   # English localization data
│       └── es.json   # Spanish localization data
├── docs/             # Comprehensive project documentation
├── scripts/          # Build and utility scripts
├── build/            # Generated build artifacts (ignored by git)
├── pubspec.yaml      # Dependencies and project configuration
├── analysis_options.yaml  # Dart analysis configuration
├── devtools_options.yaml  # Flutter DevTools configuration
└── .gitignore        # Git ignore patterns
```

### Main Application Code (lib/)

```
lib/
├── main.dart                    # Application entry point with startup optimization
├── theme.dart                   # Theme configuration and provider
├── models/                      # Data models and DTOs
│   ├── transaction_model.dart   # Core Transaction, Category, TransactionType models
│   ├── parse_result.dart        # ParseResult and ParseStatus for parsing outcomes
│   ├── pending_transaction_state.dart  # State management for ambiguous transactions
│   ├── amount_candidate.dart    # Amount parsing candidate representation
│   ├── category_suggestion.dart # Category suggestion model
│   └── localization_data.dart   # Localization data structure
├── services/                    # Business logic and data services
│   ├── storage_service.dart     # Local data persistence with SharedPreferences
│   ├── localization_service.dart # Loads locale-specific parsing keywords
│   ├── logging_service.dart     # Application logging and performance monitoring
│   ├── error_handling_service.dart # Global error handling and reporting
│   ├── error_notification_service.dart # User-facing error notifications
│   ├── network_service.dart     # Network utilities and connectivity
│   └── parser/                  # Advanced ML-powered parsing module
│       ├── transaction_parsing_service.dart # Main parsing orchestrator
│       ├── parsing_strategy.dart # Abstract strategy interface
│       ├── parsing_context.dart  # Parsing context and metadata
│       ├── parsing_config.dart   # Configuration for parsing behavior
│       ├── parse_logger.dart     # Specialized logging for parsing operations
│       ├── entity_extractor_base.dart # Abstract ML entity extractor
│       ├── real_entity_extractor.dart # ML Kit implementation
│       ├── ml_kit_error_handler.dart # ML Kit specific error handling
│       ├── learned_association_service.dart # Learning system for user corrections
│       ├── learned_category_storage.dart # Storage for learned category associations
│       ├── category_finder_service.dart # Category detection and suggestion
│       ├── category_keyword_map.dart # Category keyword mappings
│       ├── fallback_parser_service.dart # Regex-based fallback parser
│       ├── mlkit_parser_service.dart # Legacy facade (deprecated)
│       └── strategies/          # Parsing strategy implementations
│           ├── learned_association_strategy.dart # Fast learned pattern matching
│           ├── mlkit_strategy.dart # ML Kit entity extraction with Trust-but-Verify
│           └── fallback_regex_strategy.dart # Regex-based safety net
├── screens/                     # UI screens and main interfaces
│   ├── chat_screen.dart         # Main chat interface for transaction entry
│   ├── categories_screen.dart   # Category management interface
│   ├── settings_screen.dart     # Application settings and preferences
│   └── statistics_screen.dart   # Financial statistics and data visualization
├── widgets/                     # Reusable UI components
│   ├── transaction_message.dart # Chat message display for transactions
│   ├── category_picker_dialog.dart # Category selection dialog
│   ├── quick_reply_widget.dart  # Interactive buttons for ambiguity resolution
│   ├── transaction_edit_dialog.dart # Transaction editing interface
│   └── error_widgets.dart       # Error display components
├── navigation/                  # Navigation and routing
│   └── app_navigation.dart      # Main navigation controller with bottom tabs
├── utils/                       # Utility functions and helpers
│   ├── currency_utils.dart      # Currency formatting and handling
│   ├── amount_utils.dart        # Amount parsing and validation utilities
│   ├── raw_number_finder.dart   # Numeric value extraction for Trust-but-Verify
│   └── startup_timer.dart       # Performance monitoring for app startup
├── audio_recorder.dart          # Audio recording functionality
├── video_recorder.dart          # Video recording and playback
├── image_upload.dart            # Image capture and selection
└── file_upload.dart             # General file upload utilities
```

## Key Components

### Models (`lib/models/`)

- **transaction_model.dart**: Contains the core data models: `Transaction`, `Category`, `TransactionType`, `ChatMessage`, and `TransactionProvider`.
- **parse_result.dart**: A data transfer object from the parser service to the UI, indicating the outcome of a parsing attempt (`ParseStatus`).
- **pending_transaction_state.dart**: Encapsulates the state of an ambiguous transaction that requires user input (a "soft fail").
- **amount_candidate.dart**: Represents a potential amount found during parsing.

### Services (`lib/services/`)

- **storage_service.dart**: Manages local data persistence using `shared_preferences`.
- **localization_service.dart**: Loads and provides localization data from JSON files for the `FallbackRegexStrategy`.
- **parser/transaction_parsing_service.dart**: The primary parsing service. It orchestrates the parsing pipeline by executing a series of strategies (`LearnedAssociationStrategy`, `MlKitStrategy`, `FallbackRegexStrategy`).
- **parser/strategies/mlkit_strategy.dart**: A parsing strategy that uses a "Trust but Verify" system, combining on-device ML Kit entity extraction with a `RawNumberFinder` to handle ambiguity.
- **parser/strategies/fallback_regex_strategy.dart**: A parsing strategy that uses a localizable, regex-based parser as a safety net.
- **parser/learned_association_service.dart**: The app's learning system. Stores and retrieves user corrections to improve parsing speed and accuracy over time.
- **parser/mlkit_parser_service.dart**: A deprecated façade that points to the new `TransactionParsingService` for backward compatibility.

### Utilities (`lib/utils/`)

- **currency_utils.dart**: Provides helper functions for currency formatting and handling.
- **amount_utils.dart**: Contains utilities for amount parsing and validation.
- **raw_number_finder.dart**: A utility that extracts all raw numeric values from text, a key part of the "Trust but Verify" strategy.
- **startup_timer.dart**: A utility for measuring app startup performance.

### Screens (`lib/screens/`)

- **chat_screen.dart**: The main chat interface for entering transactions. It manages the "soft fail" conversation flow using the `PendingTransactionState` model.
- **categories_screen.dart**: Allows users to manage transaction categories.
- **settings_screen.dart**: Allows users to configure app settings.
- **statistics_screen.dart**: Displays financial statistics and data visualizations.

### Widgets (`lib/widgets/`)

- **transaction_message.dart**: A widget to display a saved transaction in the chat.
- **category_picker_dialog.dart**: A dialog for manually selecting a category.
- **quick_reply_widget.dart**: A widget that displays interactive buttons in a chat message for ambiguity resolution.
- **transaction_edit_dialog.dart**: A dialog for editing existing transactions.

### Multimedia Support (`lib/`)

- **audio_recorder.dart**: Handles audio recording functionality.
- **file_upload.dart**: Provides file upload utilities.
- **image_upload.dart**: Manages image capture and selection.
- **video_recorder.dart**: Handles video recording and playback.