import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

/// Defines the type of transaction.
enum TransactionType {
  expense,
  income,
  loan,
}

/// A category for classifying transactions.
///
/// Categories help organize transactions into logical groups for analysis
/// and reporting purposes. Each category has a name, icon, color, and type.
class Category {
  /// Unique identifier for the category (e.g., 'food', 'salary').
  final String id;

  /// Display name of the category (e.g., 'Food & Drink').
  final String name;

  /// A string representing the icon, often an emoji.
  final String icon;

  /// The integer value of the color for UI representation.
  final int colorValue;

  /// The type of transaction this category applies to.
  final TransactionType type;

  Category({
    required this.id,
    required this.name,
    required this.icon,
    required this.colorValue,
    required this.type,
  });
}

/// Represents a financial transaction in the application.
///
/// A transaction records a financial event, including details such as amount,
/// currency, date, category, and a description.
class Transaction {
  /// Unique identifier for the transaction.
  final String id;

  /// The monetary amount of the transaction.
  final double amount;

  /// The type of the transaction (expense, income, or loan).
  final TransactionType type;

  /// The ID of the category this transaction belongs to.
  final String categoryId;

  /// The date and time when the transaction occurred.
  final DateTime date;

  /// A text description of the transaction.
  final String description;

  /// A list of tags for custom filtering and organization.
  final List<String> tags;

  /// The ISO 4217 currency code (e.g., 'USD', 'EUR', 'JPY').
  final String currencyCode;

  /// Creates a new transaction.
  ///
  /// [id] will be automatically generated if not provided.
  /// [amount] is required.
  /// [type] is required.
  /// [categoryId] is required.
  /// [date] defaults to the current date if not specified.
  /// [description] is required.
  /// [tags] is an optional list of strings.
  /// [currencyCode] defaults to 'USD'.
  Transaction({
    String? id,
    required this.amount,
    required this.type,
    required this.categoryId,
    DateTime? date,
    required this.description,
    this.tags = const [],
    this.currencyCode = 'USD',
  })  : id = id ?? const Uuid().v4(),
        date = date ?? DateTime.now();

  /// Creates a copy of this transaction with specified attributes replaced with new values.
  Transaction copyWith({
    double? amount,
    TransactionType? type,
    String? categoryId,
    DateTime? date,
    String? description,
    List<String>? tags,
    String? currencyCode,
  }) {
    return Transaction(
      id: id,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      categoryId: categoryId ?? this.categoryId,
      date: date ?? this.date,
      description: description ?? this.description,
      tags: tags ?? this.tags,
      currencyCode: currencyCode ?? this.currencyCode,
    );
  }
}

/// Manages transaction data and provides methods for transaction operations.
///
/// This provider serves as the central store for all transaction data in the app.
/// It provides methods to add, update, and remove transactions, as well as
/// query transactions based on various criteria.
class TransactionProvider extends ChangeNotifier {
  /// Service for persisting transaction data.
  final StorageService _storageService;

  /// Internal list of all transactions.
  List<Transaction> _transactions = [];

  /// Default categories available in the app.
  List<Category> _categories = [];

  /// Creates a new TransactionProvider with the specified storage service.
  TransactionProvider(this._storageService) {
    _loadData();
  }

  /// Loads transaction and category data from persistent storage.
  Future<void> _loadData() async {
    // In a real app, this would load from StorageService
    // _transactions = await _storageService.getTransactions();
    // _categories = await _storageService.getCategories();
    notifyListeners();
  }

  /// Returns an unmodifiable list of all transactions.
  List<Transaction> get transactions => List.unmodifiable(_transactions);

  /// Returns an unmodifiable list of all categories.
  List<Category> get categories => List.unmodifiable(_categories);

  /// Adds a new transaction to the data store.
  Future<void> addTransaction(Transaction transaction) async {
    _transactions.add(transaction);
    // await _storageService.saveTransactions(_transactions);
    notifyListeners();
  }

  /// Updates an existing transaction.
  Future<bool> updateTransaction(Transaction transaction) async {
    final index = _transactions.indexWhere((t) => t.id == transaction.id);
    if (index < 0) return false;

    _transactions[index] = transaction;
    // await _storageService.saveTransactions(_transactions);
    notifyListeners();
    return true;
  }

  /// Removes a transaction by its ID.
  Future<bool> removeTransaction(String id) async {
    final initialLength = _transactions.length;
    _transactions.removeWhere((t) => t.id == id);
    final removed = initialLength > _transactions.length;
    
    if (removed) {
      // await _storageService.saveTransactions(_transactions);
      notifyListeners();
    }
    return removed;
  }
}

/// A mock class representing the storage service.
/// This is just for the example and would be implemented in a real service.
class StorageService {
  Future<List<Transaction>> getTransactions() async => [];
  Future<void> saveTransactions(List<Transaction> transactions) async {}
  Future<List<Category>> getCategories() async => [];
  Future<void> saveCategories(List<Category> categories) async {}
}
 