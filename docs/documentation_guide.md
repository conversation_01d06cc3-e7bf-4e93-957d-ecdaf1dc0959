# Documentation Guide

This document provides guidelines for maintaining and updating documentation in the Money Lover Chat project.

## Documentation Philosophy

Our documentation follows these core principles:

1. **Documentation as Code**: Documentation is treated as a first-class citizen alongside code, stored in Git, and maintained through the same workflows.
2. **Single Source of Truth**: Information should only be documented once and cross-referenced, not duplicated.
3. **Audience Awareness**: Documentation should be written with future developers (including your future self) in mind.
4. **Up-to-Date**: Documentation must remain current, with updates being part of every feature or bug fix.
5. **Discoverability**: Information should be easy to find through clear organization and cross-referencing.

## Documentation Types

### High-Level Documentation

Located in the `docs/` directory, high-level documentation provides a broad overview of the application, architecture, and processes.

#### Updating High-Level Docs

1. **When to update**: When adding new features, changing architecture, or modifying project structure.
2. **Process**:
   - Edit relevant Markdown files in the `docs/` directory
   - For new features, consider adding a new guide in `docs/feature_guides/`
   - Update diagrams when component relationships change
   - Review and update the README.md if the project description or setup process changes

### Code Documentation

Code documentation is embedded directly in the source code using DartDoc comments.

#### DartDoc Guidelines

1. **What to document**:
   - All public classes, methods, and properties
   - Complex private methods
   - Non-obvious code or workarounds

2. **Format**:
   ```dart
   /// Brief description of the class/method/property.
   ///
   /// More detailed explanation if necessary.
   /// 
   /// Parameters, return values, and exceptions should be documented using tags.
   ///
   /// @param paramName Description of the parameter
   /// @return Description of the return value
   /// @throws ExceptionType Description of when the exception is thrown
   ```

3. **Examples**:
   - See `docs/code_examples/transaction_model_example.dart` for a complete example

4. **Documentation Tools**:
   Generate DartDoc HTML documentation with:
   ```bash
   dart doc
   ```

### README

The project's `README.md` is the entry point for newcomers to the project. It should provide:

1. Brief project description
2. Core features
3. Setup instructions
4. Links to more detailed documentation

#### Updating the README

1. **When to update**: When core features change, setup process changes, or key documentation is added or moved.
2. **Process**: Edit the `README.md` file in the project root.

## Creating Diagrams

Use Mermaid syntax for diagrams in Markdown files:

1. **Flow Diagrams**:
   ```
   ```mermaid
   graph TD;
       A[Start] --> B[Process]
       B --> C[End]
   ```
   ```

2. **Sequence Diagrams**:
   ```
   ```mermaid
   sequenceDiagram
       participant A
       participant B
       A->>B: Message
       B-->>A: Response
   ```
   ```

## Templates

Use the following templates for new code:

1. **Screen Template**: `docs/code_templates/screen_template.dart`
2. **Service Template**: `docs/code_templates/service_template.dart`

## Documentation Review Process

When reviewing code, also review the associated documentation:

1. Ensure DartDoc comments are accurate and complete
2. Verify that changes to architecture or features are reflected in high-level docs
3. Check that new public APIs are properly documented

## Tools and Resources

1. **Markdown Editor**: VS Code with Markdown Preview or another Markdown editor
2. **DartDoc**: [Official Documentation](https://dart.dev/tools/dartdoc)
3. **Mermaid**: [Mermaid Syntax Guide](https://mermaid-js.github.io/mermaid/#/)

## Best Practices

1. **Write documentation as you code**: Don't leave it for later.
2. **Be concise but complete**: Include necessary details without verbosity.
3. **Use examples**: Code examples help clarify complex concepts.
4. **Keep document scope focused**: Each file should cover a specific topic.
5. **Use templates**: Maintain consistency by using documentation templates.
6. **Cross-reference**: Link between related documentation rather than duplicating.
7. **Use diagrams**: Visual representations for complex relationships or flows.
8. **Test your documentation**: Have someone unfamiliar with the code follow your docs.

## Documentation Maintenance

Periodically review and update documentation:

1. **Documentation Audit**: Quarterly review of high-level documentation
2. **Code Documentation Check**: Automated linting for missing documentation
3. **Dead Documentation Removal**: Remove or update outdated documentation
4. **Link Verification**: Ensure all internal and external links are valid 