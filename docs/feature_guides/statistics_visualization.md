# Statistics and Data Visualization

This document explains the statistics and data visualization features of the DreamFlow application.

**Feature Status**: Fully Implemented
**Primary Screen**: `StatisticsScreen`
**Chart Library**: `fl_chart` v0.68.0

## Overview

The statistics features provide users with visual insights into their financial data, helping them understand spending patterns, track budget progress, and make informed financial decisions.

## User Flow

```mermaid
graph TD
    A[User opens Statistics Screen] --> B[App loads transaction data]
    B --> C[App calculates statistics]
    C --> D[App renders charts and graphs]
    D --> E[User selects time period]
    E --> F[App updates visualization]
    D --> G[User selects category filter]
    G --> H[App updates visualization]
```

## Components Involved

### UI Components
- `StatisticsScreen`: Main interface for data visualization with animated transitions
- **Summary Cards**: Display balance, income, expenses, and loans with color-coded icons
- **Chart Toggle**: Switch between chart view and transaction list view
- **Time Period Selector**: Filter data by different time ranges
- **Category Spending Charts**: Visual breakdown of spending by category using `fl_chart`

### Business Logic
- `TransactionProvider`: Provides transaction data and filtering methods
- **Statistical Methods**:
  - `getTotalByTypeInRange()`: Calculate totals by transaction type within date ranges
  - `getTransactionsInDateRange()`: Filter transactions by date
  - `getCategorySpending()`: Aggregate spending by category
- **Animation Controller**: Manages fade transitions for smooth UI updates

### Libraries
- `fl_chart`: Used for rendering charts and graphs

## Implementation Details

### Data Aggregation

The statistical calculations involve several aggregation operations:

1. **Time-based grouping**: Grouping transactions by day, week, month, etc.
2. **Category grouping**: Aggregating spending by category
3. **Calculation of metrics**: Computing totals, averages, percentages, etc.

```mermaid
sequenceDiagram
    participant SS as StatisticsScreen
    participant TP as TransactionProvider
    participant Chart as ChartWidgets
    
    SS->>TP: getTransactionsForPeriod(startDate, endDate)
    TP->>SS: List<Transaction>
    SS->>SS: aggregateByCategory(transactions)
    SS->>SS: calculateTrends(transactions)
    SS->>Chart: render(aggregatedData)
    Chart->>SS: rendered visualization
```

### Chart Types and Their Uses

1. **Pie Chart**: 
   - Purpose: Show proportion of spending across categories
   - Implementation: Uses `PieChart` from `fl_chart`
   - Data: Percentage of total spending by category

2. **Line Chart**:
   - Purpose: Display spending trends over time
   - Implementation: Uses `LineChart` from `fl_chart`
   - Data: Daily/weekly/monthly total spending

3. **Bar Chart**:
   - Purpose: Compare spending across time periods or categories
   - Implementation: Uses `BarChart` from `fl_chart`
   - Data: Spending amounts grouped by selected dimension

4. **Statistical Cards**:
   - Purpose: Highlight key metrics (total spending, savings, etc.)
   - Implementation: Custom widgets
   - Data: Calculated financial metrics

## Code Example

Here's a simplified example of how category aggregation might be implemented:

```dart
// In StatisticsScreen
Map<Category, double> aggregateByCategory(List<Transaction> transactions) {
  final Map<Category, double> result = {};
  
  for (final transaction in transactions) {
    if (transaction.amount < 0) { // Only expenses
      final category = transaction.category;
      final currentAmount = result[category] ?? 0;
      result[category] = currentAmount + transaction.amount.abs();
    }
  }
  
  return result;
}

// Rendering a pie chart with the aggregated data
Widget buildCategoryPieChart(Map<Category, double> categoryData) {
  final total = categoryData.values.fold(0.0, (sum, amount) => sum + amount);
  final sections = categoryData.entries.map((entry) {
    final percentage = total > 0 ? (entry.value / total) * 100 : 0;
    return PieChartSectionData(
      value: entry.value,
      title: '${percentage.toStringAsFixed(1)}%',
      color: entry.key.color,
      radius: 60,
    );
  }).toList();
  
  return PieChart(
    PieChartData(
      sections: sections,
      centerSpaceRadius: 40,
    ),
  );
}
```

## User Experience Considerations

1. **Interactivity**: Allow users to interact with charts (tap for details, zoom, etc.)
2. **Filters**: Provide options to filter by date ranges, categories, etc.
3. **Responsiveness**: Ensure visualizations work well on different screen sizes
4. **Accessibility**: Consider color choices for color-blind users
5. **Performance**: Optimize rendering for large datasets

## Configuration Options

Users can customize their statistics view with several options:

1. **Time Period**: Daily, weekly, monthly, yearly, or custom date range
2. **Chart Types**: Switch between different visualization types
3. **Categories**: Include/exclude specific categories
4. **Income vs. Expense**: Focus on income, expenses, or both

## Future Enhancements

1. **Predictive Analytics**: Forecast future spending based on historical data
2. **Budget Tracking**: Visual comparison of spending against budget
3. **Export Capabilities**: Allow users to export charts and data
4. **Benchmarking**: Compare spending to averages or targets
5. **Anomaly Detection**: Highlight unusual spending patterns

## Testing Strategy

1. **Unit Tests**: Verify calculation logic
2. **Widget Tests**: Test chart rendering with sample data
3. **Integration Tests**: Validate data flow from transactions to visualizations
4. **Performance Tests**: Ensure smooth rendering with large datasets 