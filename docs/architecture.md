# Architecture Documentation

This document describes the architecture of the DreamFlow application, including its components, data flow, and key design patterns.

**Project**: DreamFlow (Money Lover Chat)
**Architecture Pattern**: Layered Architecture with Provider State Management
**Primary Language**: Dart/Flutter

## Architectural Overview

The application follows a layered architecture pattern with Provider for state management:

```mermaid
graph TD
    UI[UI Layer - Screens & Widgets]
    BL[Business Logic Layer - Providers]
    SL[Service Layer]
    DL[Data Layer - Models & Storage]
    
    UI --> BL
    BL --> SL
    SL --> DL
    BL --> DL
```

## Component Diagram

The major components of the application and their interactions, reflecting the refactored parsing module which now uses a Strategy Pattern.

```mermaid
graph TD
    subgraph "Presentation Layer"
        CS[Categories Screen]
        ChS[Chat Screen]
        SS[Settings Screen]
        StS[Statistics Screen]
        AN[App Navigation]
    end
    
    subgraph "Business Logic Layer"
        TP[Transaction Provider]
        ThP[Theme Provider]
    end
    
    subgraph "Service Layer"
        subgraph "Parsing Module (Strategy Pattern)"
            TPS[TransactionParsingService]
            LAS[LearnedAssociationStrategy]
            MKS[MlKitStrategy]
            FRS[FallbackRegexStrategy]
        end
        LS[LocalizationService]
        STS[Storage Service]
        CUtils[CurrencyUtils]
        AR[Audio Recorder]
        FU[File Upload]
        IU[Image Upload]
        VR[Video Recorder]
        AU[Amount Utils]
        RNF[Raw Number Finder]
        ST[Startup Timer]
    end
    
    subgraph "Data Layer"
        TM[Transaction Model]
        PR[ParseResult Model]
        LD[LocalizationData Model]
        AC[AmountCandidate Model]
        CSG[CategorySuggestion Model]
        PState[PendingTransactionState Model]
        SP[Shared Preferences]
    end
    
    ChS --> TPS
    ChS --> TP
    
    TPS --> LAS
    TPS --> MKS
    TPS --> FRS

    MKS --> RNF
    FRS --> LS

    LAS --> STS
    LS --> LD

    CS --> TP
    SS --> ThP
    SS --> STS
    StS --> TP
    
    TP --> STS
    TP --> TM
    ThP --> SP
    
    STS --> SP
```

## State Management

The application uses the Provider pattern for state management. This provides a simple and efficient way to manage and propagate state changes throughout the application. The "soft fail" UI state is now encapsulated in the `PendingTransactionState` model.

```mermaid
graph TD
    subgraph "Widget Tree"
        RW[Root Widget - MultiProvider]
        ML[MoneyLoverChatApp]
        AN[AppNavigation]
        SC[Screen Components]
    end
    
    subgraph "Providers"
        TP[TransactionProvider]
        ThP[ThemeProvider]
        TPS_P[TransactionParsingService Provider]
    end

    subgraph "Services (Singletons)"
        LAS_S[LearnedAssociationService]
        LS_S[LocalizationService]
        CFS[CategoryFinderService]
    end
    
    subgraph "Data Sources"
        STS[Storage Service]
        SP[Shared Preferences]
    end
    
    RW --> TP
    RW --> ThP
    RW --> TPS_P
    RW --> LAS_S
    RW --> LS_S
    RW --> ML
    ML --> AN
    AN --> SC
    
    SC -- Consumer --> TP
    SC -- Consumer --> ThP
    SC -- Consumer --> TPS_P
    
    TP --> STS
    STS --> SP
    ThP --> SP
```

## Data Flow - Unified Learning & Transaction Processing

The sequence of operations when a user enters a transaction now follows a clean Strategy Pattern. The `TransactionParsingService` iterates through strategies, starting with the fastest and most specific (`LearnedAssociationStrategy`).

```mermaid
sequenceDiagram
    participant User
    participant ChatScreen
    participant TransactionParsingService
    participant LearnedAssociationStrategy
    participant MlKitStrategy
    participant TransactionProvider

    User->>ChatScreen: Enters text: "Dinner at The Local Bistro 50"
    ChatScreen->>TransactionParsingService: parseTransaction(text)

    TransactionParsingService->>LearnedAssociationStrategy: execute(context)

    alt Learned Association Found
        LearnedAssociationStrategy-->>TransactionParsingService: Return ParseResult(status=success)
        Note over TransactionParsingService: Bypasses other strategies.
        TransactionParsingService-->>ChatScreen: Return ParseResult(status=success)
        ChatScreen->>TransactionProvider: addTransaction(transaction)
        TransactionProvider-->>ChatScreen: Update State
        ChatScreen->>User: "✅ Transaction Saved"
    else No Association Found
        LearnedAssociationStrategy-->>TransactionParsingService: Return null
        TransactionParsingService->>MlKitStrategy: execute(context)
        Note over MlKitStrategy: Proceeds with ML Kit / RawNumberFinder flow.
        MlKitStrategy-->>TransactionParsingService: Return ParseResult(status=needsCategory)
        TransactionParsingService-->>ChatScreen: Return ParseResult(status=needsCategory)
        ChatScreen->>User: Show "soft fail" UI (e.g., category picker)
        User->>ChatScreen: Selects "Food" category
        ChatScreen->>TransactionParsingService: learn(text, categoryId: "food")
        ChatScreen->>TransactionProvider: addTransaction(updatedTransaction)
        TransactionProvider-->>ChatScreen: Update State
        ChatScreen->>User: "✅ Transaction Saved & Learned"
    end
```

## Data Flow - Amount Ambiguity Resolution

The "Trust but Verify" strategy is now encapsulated within the `MlKitStrategy`.

```mermaid
sequenceDiagram
    participant User
    participant ChatScreen
    participant TransactionParsingService
    participant MlKitStrategy
    participant RawNumberFinder
    participant LearnedAssociationService

    User->>ChatScreen: Enters text: "Dinner at Lux68 50"
    ChatScreen->>TransactionParsingService: parseTransaction(text)
    TransactionParsingService->>MlKitStrategy: execute(context)

    par Parallel Extraction within MlKitStrategy
        MlKitStrategy->>MlKitStrategy: Use ML Kit to find entities
        MlKitStrategy->>RawNumberFinder: Find all raw numbers
    end

    MlKitStrategy->>MlKitStrategy: Consolidate candidates: [68, 50]
    Note over MlKitStrategy: Ambiguity detected.

    MlKitStrategy-->>TransactionParsingService: Return ParseResult(status=needsAmountConfirmation, candidates=[68, 50])
    TransactionParsingService-->>ChatScreen: Return ParseResult(status=needsAmountConfirmation, candidates=[68, 50])
    ChatScreen->>User: "Which of these is the amount?"
    ChatScreen->>User: Show Quick Replies: ["50"], ["68"]

    User->>ChatScreen: Taps ["50"]

    ChatScreen->>TransactionParsingService: completeTransaction(originalText, confirmedAmount=50)
    TransactionParsingService->>LearnedAssociationService: learn(text="Dinner at Lux68 50", confirmedAmount=50)
    Note over LearnedAssociationService: Learns that for this pattern, "50" is the amount.
    
    TransactionParsingService-->>ChatScreen: Return ParseResult(status=success)
    ChatScreen->>User: "✅ Transaction Saved & Learned"
```

## File Structure and Responsibility

| Component | Primary File(s) | Responsibility |
|-----------|-----------------|----------------|
| UI Layer | `screens/*.dart`, `widgets/*.dart` | User interface and interaction |
| Navigation | `navigation/app_navigation.dart` | Screen routing and navigation |
| State Management | `models/transaction_model.dart`, `theme.dart`, `models/pending_transaction_state.dart` | App-wide state management, including "soft fail" UI state. |
| Services | `services/*.dart` | Business logic implementation |
| **Localization** | **`services/localization_service.dart`** | **Loads locale-specific keywords for parsing.** |
| Parsing Module | `services/parser/transaction_parsing_service.dart` | Orchestrates the parsing pipeline using a strategy pattern. |
| Parsing Strategies | `services/parser/strategies/*.dart` | Implements specific parsing steps (learning, ML, regex). |
| **Learning** | **`services/parser/learned_association_service.dart`** | **Stores and retrieves user corrections for future parsing.** |
| Utilities | `utils/*.dart` | Helper functions (e.g., currency, startup timing) |
| Data Models | `models/*.dart` | Data structure definitions |
| Storage | `services/storage_service.dart` | Data persistence |

## Design Patterns

- **Provider Pattern**: Used for state management across the application.
- **Service Layer Pattern**: Used to separate business logic from UI components.
- **Singleton Pattern**: Used for managing service instances.
- **Strategy Pattern**: The `TransactionParsingService` uses a list of `ParsingStrategy` objects (`LearnedAssociationStrategy`, `MlKitStrategy`, `FallbackRegexStrategy`) to process transactions in a configurable pipeline.
- **Observer Pattern**: Implemented via Provider for reactive UI updates.
