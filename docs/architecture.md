# Architecture Documentation

This document describes the architecture of the DreamFlow application, including its components, data flow, and key design patterns.

**Project**: DreamFlow (Money Lover Chat)
**Architecture Pattern**: Layered Architecture with Provider State Management
**Primary Language**: Dart/Flutter
**Key Technologies**: Google ML Kit, Firebase Crashlytics, FL Chart, Supabase (optional)
**State Management**: Provider Pattern with ValueNotifier
**Parsing Strategy**: Hybrid ML + Learning System with Strategy Pattern

## Architectural Overview

The application follows a layered architecture pattern with Provider for state management:

```mermaid
graph TD
    UI[UI Layer - Screens & Widgets]
    BL[Business Logic Layer - Providers]
    SL[Service Layer]
    DL[Data Layer - Models & Storage]
    
    UI --> BL
    BL --> SL
    SL --> DL
    BL --> DL
```

## Component Diagram

The major components of the application and their interactions, reflecting the refactored parsing module which now uses a Strategy Pattern.

```mermaid
graph TD
    subgraph "Presentation Layer"
        CS[Categories Screen]
        ChS[Chat Screen]
        SS[Settings Screen]
        StS[Statistics Screen]
        AN[App Navigation]
    end
    
    subgraph "Business Logic Layer"
        TP[Transaction Provider]
        ThP[Theme Provider]
    end
    
    subgraph "Service Layer"
        subgraph "Core Services"
            STS[Storage Service]
            LS[Localization Service]
            LogS[Logging Service]
            EHS[Error Handling Service]
            ENS[Error Notification Service]
            NS[Network Service]
        end

        subgraph "Parsing Module (Strategy Pattern)"
            TPS[TransactionParsingService]
            LAS[LearnedAssociationStrategy]
            MKS[MlKitStrategy]
            FRS[FallbackRegexStrategy]
            LEAS[Learned Association Service]
            CFS[Category Finder Service]
            EEB[Entity Extractor Base]
            REE[Real Entity Extractor]
            MKEH[ML Kit Error Handler]
        end

        subgraph "Utilities"
            CUtils[Currency Utils]
            AU[Amount Utils]
            RNF[Raw Number Finder]
            ST[Startup Timer]
        end

        subgraph "Multimedia Services"
            AR[Audio Recorder]
            FU[File Upload]
            IU[Image Upload]
            VR[Video Recorder]
        end
    end
    
    subgraph "Data Layer"
        subgraph "Core Models"
            TM[Transaction Model]
            PR[ParseResult Model]
            PState[PendingTransactionState Model]
        end

        subgraph "Parsing Models"
            AC[AmountCandidate Model]
            CSG[CategorySuggestion Model]
            LD[LocalizationData Model]
        end

        subgraph "Storage"
            SP[Shared Preferences]
        end
    end
    
    ChS --> TPS
    ChS --> TP
    AN --> ChS
    AN --> CS
    AN --> SS
    AN --> StS

    TPS --> LAS
    TPS --> MKS
    TPS --> FRS
    TPS --> LEAS
    TPS --> CFS

    MKS --> RNF
    MKS --> REE
    MKS --> MKEH
    FRS --> LS
    LAS --> LEAS
    CFS --> LEAS

    LEAS --> STS
    LS --> LD
    EHS --> LogS
    EHS --> ENS

    CS --> TP
    SS --> ThP
    SS --> STS
    StS --> TP

    TP --> STS
    TP --> TM
    ThP --> SP

    STS --> SP
    LogS --> SP
```

## State Management

The application uses the Provider pattern for state management with ValueNotifier for reactive updates. This provides a simple and efficient way to manage and propagate state changes throughout the application. The "soft fail" UI state is encapsulated in the `PendingTransactionState` model, and ML Kit services are lazily initialized for optimal startup performance.

```mermaid
graph TD
    subgraph "Widget Tree"
        RW[Root Widget - MultiProvider]
        ML[MoneyLoverChatApp]
        AN[AppNavigation]
        SC[Screen Components]
    end
    
    subgraph "Providers"
        TP[TransactionProvider]
        ThP[ThemeProvider]
        TPS_P[TransactionParsingService ValueNotifier]
    end

    subgraph "Services (Singletons)"
        LAS_S[LearnedAssociationService]
        LS_S[LocalizationService]
        LogS_S[LoggingService]
        EHS_S[ErrorHandlingService]
    end
    
    subgraph "Data Sources"
        STS[Storage Service]
        SP[Shared Preferences]
    end
    
    RW --> TP
    RW --> ThP
    RW --> TPS_P
    RW --> LAS_S
    RW --> LS_S
    RW --> LogS_S
    RW --> EHS_S
    RW --> ML
    ML --> AN
    AN --> SC

    SC -- Consumer --> TP
    SC -- Consumer --> ThP
    SC -- Consumer --> TPS_P

    TP --> STS
    STS --> SP
    ThP --> SP
    LogS_S --> SP
    EHS_S --> LogS_S
```

## Data Flow - Unified Learning & Transaction Processing

The sequence of operations when a user enters a transaction now follows a clean Strategy Pattern. The `TransactionParsingService` iterates through strategies, starting with the fastest and most specific (`LearnedAssociationStrategy`).

```mermaid
sequenceDiagram
    participant User
    participant ChatScreen
    participant TransactionParsingService
    participant LearnedAssociationStrategy
    participant MlKitStrategy
    participant TransactionProvider

    User->>ChatScreen: Enters text: "Dinner at The Local Bistro 50"
    ChatScreen->>TransactionParsingService: parseTransaction(text)

    TransactionParsingService->>LearnedAssociationStrategy: execute(context)

    alt Learned Association Found
        LearnedAssociationStrategy-->>TransactionParsingService: Return ParseResult(status=success)
        Note over TransactionParsingService: Bypasses other strategies.
        TransactionParsingService-->>ChatScreen: Return ParseResult(status=success)
        ChatScreen->>TransactionProvider: addTransaction(transaction)
        TransactionProvider-->>ChatScreen: Update State
        ChatScreen->>User: "✅ Transaction Saved"
    else No Association Found
        LearnedAssociationStrategy-->>TransactionParsingService: Return null
        TransactionParsingService->>MlKitStrategy: execute(context)
        Note over MlKitStrategy: Proceeds with ML Kit / RawNumberFinder flow.
        MlKitStrategy-->>TransactionParsingService: Return ParseResult(status=needsCategory)
        TransactionParsingService-->>ChatScreen: Return ParseResult(status=needsCategory)
        ChatScreen->>User: Show "soft fail" UI (e.g., category picker)
        User->>ChatScreen: Selects "Food" category
        ChatScreen->>TransactionParsingService: learn(text, categoryId: "food")
        ChatScreen->>TransactionProvider: addTransaction(updatedTransaction)
        TransactionProvider-->>ChatScreen: Update State
        ChatScreen->>User: "✅ Transaction Saved & Learned"
    end
```

## Data Flow - Amount Ambiguity Resolution

The "Trust but Verify" strategy is now encapsulated within the `MlKitStrategy`.

```mermaid
sequenceDiagram
    participant User
    participant ChatScreen
    participant TransactionParsingService
    participant MlKitStrategy
    participant RawNumberFinder
    participant LearnedAssociationService

    User->>ChatScreen: Enters text: "Dinner at Lux68 50"
    ChatScreen->>TransactionParsingService: parseTransaction(text)
    TransactionParsingService->>MlKitStrategy: execute(context)

    par Parallel Extraction within MlKitStrategy
        MlKitStrategy->>MlKitStrategy: Use ML Kit to find entities
        MlKitStrategy->>RawNumberFinder: Find all raw numbers
    end

    MlKitStrategy->>MlKitStrategy: Consolidate candidates: [68, 50]
    Note over MlKitStrategy: Ambiguity detected.

    MlKitStrategy-->>TransactionParsingService: Return ParseResult(status=needsAmountConfirmation, candidates=[68, 50])
    TransactionParsingService-->>ChatScreen: Return ParseResult(status=needsAmountConfirmation, candidates=[68, 50])
    ChatScreen->>User: "Which of these is the amount?"
    ChatScreen->>User: Show Quick Replies: ["50"], ["68"]

    User->>ChatScreen: Taps ["50"]

    ChatScreen->>TransactionParsingService: completeTransaction(originalText, confirmedAmount=50)
    TransactionParsingService->>LearnedAssociationService: learn(text="Dinner at Lux68 50", confirmedAmount=50)
    Note over LearnedAssociationService: Learns that for this pattern, "50" is the amount.
    
    TransactionParsingService-->>ChatScreen: Return ParseResult(status=success)
    ChatScreen->>User: "✅ Transaction Saved & Learned"
```

## File Structure and Responsibility

| Component | Primary File(s) | Responsibility |
|-----------|-----------------|----------------|
| **Application Entry** | **`main.dart`** | **App initialization, provider setup, startup optimization** |
| **UI Layer** | **`screens/*.dart`, `widgets/*.dart`** | **User interface, interaction, and visual components** |
| **Navigation** | **`navigation/app_navigation.dart`** | **Bottom tab navigation, routing, deep linking** |
| **State Management** | **`models/transaction_model.dart`, `theme.dart`** | **Provider-based state management with reactive updates** |
| **Core Services** | **`services/storage_service.dart`, `services/logging_service.dart`** | **Data persistence, logging, error handling** |
| **Parsing Module** | **`services/parser/transaction_parsing_service.dart`** | **ML-powered parsing orchestration with strategy pattern** |
| **Parsing Strategies** | **`services/parser/strategies/*.dart`** | **Learned associations, ML Kit extraction, regex fallback** |
| **Learning System** | **`services/parser/learned_association_service.dart`** | **User correction learning and pattern storage** |
| **ML Integration** | **`services/parser/real_entity_extractor.dart`** | **Google ML Kit entity extraction and error handling** |
| **Category Intelligence** | **`services/parser/category_finder_service.dart`** | **Smart category detection and suggestion** |
| **Localization** | **`services/localization_service.dart`** | **Multi-language parsing keyword support** |
| **Utilities** | **`utils/*.dart`** | **Currency formatting, amount parsing, performance monitoring** |
| **Data Models** | **`models/*.dart`** | **Transaction, parsing result, and state data structures** |
| **Multimedia** | **`audio_recorder.dart`, `image_upload.dart`, etc.** | **Audio, video, image, and file handling** |
| **Theme System** | **`theme.dart`** | **Material 3 theming with dark/light mode support** |
| **Error Handling** | **`services/error_handling_service.dart`** | **Global error management and Firebase Crashlytics** |

## Design Patterns

- **Provider Pattern**: Used for state management across the application with reactive UI updates.
- **Strategy Pattern**: The `TransactionParsingService` uses a configurable pipeline of `ParsingStrategy` objects (`LearnedAssociationStrategy`, `MlKitStrategy`, `FallbackRegexStrategy`) for flexible parsing approaches.
- **Service Layer Pattern**: Clear separation of business logic from UI components with dedicated service classes.
- **Singleton Pattern**: Used for managing service instances like `LoggingService`, `ErrorHandlingService`, and parsing services.
- **Observer Pattern**: Implemented via Provider and ValueNotifier for reactive UI updates and state propagation.
- **Factory Pattern**: Used in parsing strategies for creating appropriate parsers based on context.
- **Facade Pattern**: Legacy `mlkit_parser_service.dart` provides a simplified interface to the new parsing system.
- **Template Method Pattern**: Abstract `ParsingStrategy` defines the parsing workflow with concrete implementations.
- **Dependency Injection**: Services are injected through Provider for testability and modularity.

## Performance Optimizations

- **Lazy Initialization**: ML Kit services are initialized in the background after UI rendering to improve startup time.
- **Startup Performance Monitoring**: `StartupTimer` tracks initialization phases for performance optimization.
- **Strategy Short-Circuiting**: Learned associations bypass expensive ML processing for known patterns.
- **Resource Management**: Proper disposal of ML Kit resources and service cleanup.
- **Efficient State Updates**: Minimal rebuilds through targeted Provider consumers and ValueNotifier usage.
