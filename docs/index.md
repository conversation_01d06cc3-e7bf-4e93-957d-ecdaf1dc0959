# DreamFlow Documentation

Welcome to the DreamFlow documentation! This index provides links to all documentation resources available for the project.

**Note**: This project was previously referred to as "Money Lover Chat" in documentation, but the actual project name is "DreamFlow" as defined in the project configuration.

## Core Documentation

- [README](../README.md) - Project overview and quick start guide
- [Current Implementation Status](current_implementation_status.md) - Current feature status, known issues, and project naming clarification
- [Codebase Structure](codebase.md) - Detailed explanation of project organization
- [Architecture](architecture.md) - Application architecture and design patterns
- [Setup Guide](setup_guide.md) - Detailed environment setup instructions
- [Documentation Guide](documentation_guide.md) - Guidelines for maintaining documentation
- [Architectural Decisions](decisions.md) - Record of key architectural decisions

## Feature Guides

- [Chat-Based Transaction Entry](feature_guides/chat_transactions.md) - Guide to the chat interface for transaction entry
- [Statistics and Data Visualization](feature_guides/statistics_visualization.md) - Guide to financial data visualization features

## Code Templates and Examples

- [Screen Template](code_templates/screen_template.dart) - Template for creating new screens
- [Service Template](code_templates/service_template.dart) - Template for creating new services
- [Transaction Model Example](code_examples/transaction_model_example.dart) - Example of well-documented model classes

## Contributing

- [Contributing Guidelines](../CONTRIBUTING.md) - How to contribute to the project

## Additional Resources

- [Flutter Documentation](https://flutter.dev/docs) - Official Flutter documentation
- [Dart Documentation](https://dart.dev/guides) - Official Dart language guides
- [Provider Package](https://pub.dev/packages/provider) - Documentation for the Provider state management library 