# PRD: Parsing Vendor Names with Numbers
**Version:** 1.0
**Status:** Proposed
**Author:** Gemini
**Date:** 2025-07-27

## 1. Overview

This document outlines the requirements for an enhancement to the transaction parsing system to correctly handle cases where the vendor name or description contains numbers (e.g., "Lunch at **Lux68** 50k"). This is a common edge case that currently causes ambiguity between the vendor name and the transaction amount.

The proposed solution is a multi-layered strategy that combines smarter initial parsing logic with a new "soft fail" conversational flow to resolve ambiguity directly with the user, ensuring the system learns from the interaction.

## 2. Problem Statement

The current parsing system, both the ML Kit and regex-based parsers, struggles when a non-amount number appears in the transaction string. This creates a conflict between two key entities: the **vendor/description** and the **amount**.

For an input like `"Dinner at Lux68 50k"`:
-   The parser identifies two numbers: `68` and `50000`.
-   It lacks the context to know that `68` is part of the vendor name and `50000` is the amount.
-   This can lead to incorrect parsing, such as identifying the vendor as "Lux" and either discarding "68" or misinterpreting it as a second potential amount.

This ambiguity leads to a poor user experience, requiring manual correction and undermining trust in the parser's intelligence.

## 3. Goals

-   **Accurately Parse Descriptions with Numbers**: Correctly identify and extract vendor names or descriptions that contain numbers (e.g., "7-Eleven", "Lux68", "Shop 24/7").
-   **Resolve Ambiguity Gracefully**: When the parser cannot confidently distinguish between the amount and another number, it should ask the user for clarification rather than making an incorrect guess.
-   **Leverage the Learning System**: Use the user's clarification as a high-quality signal to train the `LearnedAssociationService`, ensuring the same ambiguity does not occur again for that user.
-   **Avoid Over-Prompting**: The system should only ask the user for help when ambiguity is high and cannot be resolved by internal heuristics.

## 4. Proposed Solution

We will implement a two-layer strategy to solve this problem.

### Layer 1: Smarter Initial Parsing Logic

The parsing services (`MlKitParserService` and `FallbackParserService`) will be updated to follow a more robust sequence:

1.  **Prioritize Finding the Amount**: The parser will first aggressively search for the most likely transaction amount. It will use strong heuristics, such as the presence of a currency symbol (`$`, `€`), currency code (`USD`, `VND`), or its position as the final number in the string.
2.  **Isolate and Remove the Amount**: Once the amount is confidently identified, it is logically removed from the input string.
3.  **The Remainder is the Description**: The rest of the string, including any other numbers, is treated as the transaction's description. This prevents other numbers from competing with the amount.

This initial step will solve many simple cases without requiring user interaction.

### Layer 2: The "Multiple Number Soft Fail" Strategy

If the smarter parsing logic (Layer 1) still results in ambiguity (i.e., it finds two or more numbers that could plausibly be the amount), a new soft fail flow will be triggered.

```mermaid
sequenceDiagram
    participant User
    participant ChatScreen
    participant MlKitParserService
    participant LearnedAssociationService

    User->>ChatScreen: "Invoice 1040 for 250 dollars"
    ChatScreen->>MlKitParserService: parseTransaction(text)
    
    MlKitParserService->>MlKitParserService: 1. Identify all numbers: [1040, 250]
    MlKitParserService->>MlKitParserService: 2. Apply heuristics (finds "dollars" near 250)
    
    alt Heuristics provide a confident answer
        Note over MlKitParserService: Logic proceeds, no prompt needed.
    else Ambiguity remains (e.g., "Lux68 dinner 50")
        MlKitParserService-->>ChatScreen: Return ParseResult(status=needsAmountConfirmation, candidates=[68, 50])
        ChatScreen->>User: "Which of these is the amount?"
        ChatScreen->>User: Show Quick Replies: ["50"], ["68"]
        
        User->>ChatScreen: Taps ["50"]
        
        ChatScreen->>LearnedAssociationService: learn(text="Lux68 dinner 50", confirmedAmount=50)
        Note over LearnedAssociationService: Learns that for this pattern, "50" is the amount and "Lux68 dinner" is the description.
        
        ChatScreen->>MlKitParserService: completeTransaction(originalText, confirmedAmount=50)
        MlKitParserService-->>ChatScreen: Return ParseResult(status=success)
        ChatScreen->>User: "✅ Transaction Saved & Learned"
    end
```

## 5. Impact on the Learning System

This new flow enhances the `LearnedAssociationService` significantly. When a user resolves the amount ambiguity:
- The service learns not just the correct amount but, more importantly, it learns that the *other number(s)* are part of the description.
- This creates a robust, permanent memory. The next time the user enters a similar phrase, the `LearnedAssociationService` will provide a perfect match from the start, and no soft fail will be necessary.

## 6. Success Criteria

-   Unit tests for vendor names with numbers (e.g., "Lux68", "7-Eleven", "Shop 24/7") pass successfully.
-   When the parser is presented with an ambiguous input like `"Lux70 dinner 25"`, the app correctly displays a quick-reply prompt asking the user to choose between "25" and "70".
-   After the user selects the correct amount, the transaction is saved correctly.
-   On a subsequent entry of the same phrase, the transaction is parsed instantly and correctly without a prompt.
-   The prompt is *not* triggered for unambiguous cases, such as `"Lunch at Lux68 for $50"`, where the currency symbol provides enough context.
