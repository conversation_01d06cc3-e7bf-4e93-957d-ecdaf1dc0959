# PRD: Hybrid ML Kit Transaction Parser
**Version:** 1.0
**Status:** Proposed
**Author:** Gemini
**Date:** 2025-07-16

## 1. Overview

This document outlines the requirements for replacing the current regex-based transaction parser with a new, intelligent Hybrid Parsing System. This system will leverage on-device Machine Learning via Google's ML Kit for robust entity extraction and a self-improving keyword model for category detection.

The primary goals are to introduce multilingual support, significantly improve parsing accuracy, and enhance the user experience by making transaction entry smarter and more intuitive, while minimizing disruption to the existing application architecture.

## 2. Background & Problem Statement

The current transaction parser relies on a set of English-specific regular expressions. This approach has several critical limitations:
- **Single-Language Support:** It is fundamentally incapable of parsing transactions in other languages like Spanish or Vietnamese.
- **Brittleness:** It can easily fail if the user's input deviates slightly from the expected sentence structure.
- **Poor Scalability:** Expanding to new languages or more complex phrases requires writing and maintaining increasingly complex and fragile regex patterns.

This limitation is the single largest blocker to expanding the app's user base and providing a truly natural language interface.

## 3. Proposed Solution: The Hybrid Parsing System

We will implement a new, modular parsing system that combines the strengths of a general ML model with application-specific logic. The system will process user input in a multi-stage pipeline.

### 3.1. High-Level Architecture

The new system will be encapsulated within a new `MlKitParserService`, which orchestrates the parsing flow.

```mermaid
graph TD
    A[User Input Text] --> B{MlKitParserService};
    B --> C[1. ML Kit Entity Extraction];
    C --> |Money & DateTime Entities| D;
    C --> |Remaining Text| D{2. CategoryFinderService};
    D --> E[2a. Check Learned History];
    E -- Found --> G[Transaction Object];
    E -- Not Found --> F[2b. Keyword Search];
    F -- Found --> G;
    F -- Not Found --> H{Uncategorized};
    H --> I[Prompt User to Select Category];
    I --> J[Save to Learned History];
    J --> G;
```

### 3.2. Component 1: ML Kit Entity Extraction

This component forms the foundation of the new parser.
- **Technology:** `google_mlkit_entity_extraction` Flutter package.
- **Responsibility:** To accurately identify and extract universal entities from the input string, regardless of language.
    - **`Money`:** Extracts the numerical amount and currency.
    - **`DateTime`:** Extracts the date and time, converting it to a standard format.
- **Key Attributes:**
    - **On-Device:** Processing happens locally, ensuring user privacy and full offline functionality.
    - **No Monetary Cost:** ML Kit is free for mobile developers.
    - **Language-Aware:** It supports multiple languages out-of-the-box, automatically handling different currency formats, date syntaxes, and decimal separators.

### 3.3. Component 2: The CategoryFinderService

This service is responsible for the application-specific logic of determining the transaction's category from the text remaining after ML Kit has extracted the money and date.

It will use a two-step "Keyword & Learn" strategy:

1.  **Learned History (User-Specific):** The service will first check a local cache (stored via `SharedPreferences`) to see if the user has previously categorized a transaction with a similar description or vendor name. This provides a highly accurate, personalized experience.
2.  **General Keyword Search (Fallback):** If no match is found in the user's history, the service will fall back to searching the text for a predefined list of keywords associated with each category (e.g., "lunch," "gas," "shopping"). This list will be designed for internationalization.

### 3.4. The Learning Workflow

When the `CategoryFinderService` cannot determine a category (i.e., both learned history and keyword searches fail), the system will not fail. Instead:
1. The transaction will be temporarily assigned an "Uncategorized" status.
2. The UI will prompt the user to manually select a category for this transaction.
3. Upon selection, the `CategoryFinderService` will **save this association** (e.g., mapping the vendor "Starbucks" to the "Food & Drink" category), improving the accuracy of future predictions.

## 4. Modular Implementation Plan

To ensure isolation and maintainability, the new parsing system will be built within a dedicated module. This minimizes changes to existing files until the final integration.

**New Directory Structure:**
```
lib/
└── services/
    ├── parser/
    │   ├── mlkit_parser_service.dart      # Main service, orchestrates the process
    │   ├── category_finder_service.dart   # Implements the "Keyword & Learn" logic
    │   ├── category_keyword_map.dart      # Defines the general keywords for categories
    │   └── learned_category_storage.dart  # Manages saving/loading user choices
    │
    ├── storage_service.dart               # (Existing)
    └── transaction_parser_service.dart    # (Old regex parser, to be deprecated)
```

This structure keeps all new logic self-contained and easy to manage.

## 5. Integration Strategy

1.  **Service Initialization:** The new services (`MlKitParserService`, `CategoryFinderService`, etc.) will be initialized at the application's root, likely within a `MultiProvider` setup, making them available throughout the app.
2.  **Chat Screen Modification:** The `ChatScreen` will be the primary integration point. The call to the old `transaction_parser_service.dart` will be replaced with a call to the new `MlKitParserService`.
3.  **UI for Learning:** A new `CategoryPickerDialog` widget will be created. This dialog will be triggered from the `ChatScreen` when a transaction is returned without a category.

## 6. User Experience Flow

```mermaid
sequenceDiagram
    participant User
    participant ChatScreen
    participant MlKitParserService
    participant CategoryFinderService

    User->>ChatScreen: Enters text: "Spent 25€ on lunch at a cafe"
    ChatScreen->>MlKitParserService: parseTransaction("...")
    MlKitParserService->>MlKitParserService: Use ML Kit to extract Money (25€) & DateTime (today)
    MlKitParserService->>CategoryFinderService: findCategory("lunch at a cafe")

    CategoryFinderService->>CategoryFinderService: 1. Check Learned History (no match)
    CategoryFinderService->>CategoryFinderService: 2. Search Keywords (finds "lunch")
    CategoryFinderService-->>MlKitParserService: Return 'Food & Drink' Category

    MlKitParserService-->>ChatScreen: Return Transaction Object (with category)
    ChatScreen->>User: Display confirmed transaction message

    %% Learning Flow
    User->>ChatScreen: Enters text: "Dinner at 'The Local Bistro'"
    ChatScreen->>MlKitParserService: parseTransaction("...")
    MlKitParserService->>CategoryFinderService: findCategory("Dinner at 'The Local Bistro'")
    CategoryFinderService-->>MlKitParserService: Return null (no category found)
    MlKitParserService-->>ChatScreen: Return Transaction Object (category is null)

    ChatScreen->>User: Show CategoryPickerDialog
    User->>ChatScreen: Selects 'Food & Drink'
    ChatScreen->>CategoryFinderService: learnCategory("The Local Bistro", "food_drink_id")
    Note over CategoryFinderService: Saves association for next time
```

## 7. Requirements & Success Criteria

### Functional Requirements:
- The system must successfully parse amounts and dates from supported languages.
- The system must assign a category based on the "Keyword & Learn" logic.
- The system must prompt the user to categorize transactions when a category cannot be determined.
- The user's choice must be saved and used for future suggestions.

### Non-Functional Requirements:
- **Performance:** Parsing must be fast and feel instantaneous to the user (<500ms).
- **Offline:** The entire parsing flow must work without an internet connection.
- **Maintainability:** The new modules must be well-documented and easy to update.

### Success Criteria:
- The new parser can successfully handle transaction entries in at least 3 languages (e.g., English, Spanish, German).
- A user's manual categorization rate should decrease over their first 10 transactions with a recurring vendor.
- The old `transaction_parser_service.dart` is fully deprecated and removed.
