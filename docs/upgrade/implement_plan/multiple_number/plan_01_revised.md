I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've analyzed the codebase and identified the core issue: the current implementation doesn't truly implement the "Trust but Verify" approach described in the PRD. The `_extractNonVendorAmount()` method filters out embedded numbers and returns only one candidate, while the consolidation logic is complex and may bypass ambiguity detection. The system needs to independently find ALL numbers using raw number finding, then consolidate with ML Kit results before applying ambiguity detection.

### Approach

The solution implements the PRD's 4-step approach: (1) Call ML Kit service for money entities, (2) Independently run raw number finder on original text, (3) Consolidate both lists into comprehensive candidates, (4) Apply ambiguity detection to consolidated list. This requires creating a new `AmountCandidate` data class, a raw number finder utility, refactoring the consolidation logic in `MlKitParserService`, and adding comprehensive unit tests for the new flow.

### Reasoning

I explored the repository structure and read the PRD document to understand the problem. I examined the current `MlKitParserService` implementation, particularly the `_parseWithMLKit()` method and its consolidation logic. I analyzed the `AmountUtils` class and existing test files to understand expected behavior. I also reviewed the `ParseResult` model and other parser services to understand the complete system architecture.

## Mermaid Diagram

sequenceDiagram
    participant UI as Chat UI
    participant MLP as MlKitParserService
    participant MLK as ML Kit Service
    participant RNF as RawNumberFinder
    participant AMB as Ambiguity Detection

    UI->>MLP: parseTransaction("lux69 100")
    
    Note over MLP: Step 1: ML Kit Path
    MLP->>MLK: annotateText("lux69 100")
    MLK-->>MLP: [Money("69"), Money("100")]
    MLP->>MLP: Convert to AmountCandidate[]
    
    Note over MLP: Step 2: Raw Number Path (Independent)
    MLP->>RNF: findAllNumbers("lux69 100")
    RNF-->>MLP: [AmountCandidate(69), AmountCandidate(100)]
    
    Note over MLP: Step 3: Consolidation
    MLP->>MLP: _consolidateCandidates(mlKitCandidates, rawCandidates)
    MLP->>MLP: Remove duplicates, merge lists
    
    Note over MLP: Step 4: Ambiguity Detection
    MLP->>AMB: _detectAmountAmbiguity(consolidatedCandidates)
    AMB-->>MLP: Multiple candidates detected
    
    MLP-->>UI: ParseResult.needsAmountConfirmation([69, 100])

## Proposed File Changes

### lib/models/amount_candidate.dart(NEW)

Create a new data class `AmountCandidate` to represent a potential amount found in text. Include properties: `double amount`, `String? currency`, `int start`, `int end`, `String sourceText`, and `AmountSource source` (enum with values: mlKit, rawNumberFinder). Add methods for equality comparison, toString(), and conversion to/from Map for compatibility with existing code. This class will standardize how we represent amount candidates from different sources.

### lib/utils/raw_number_finder.dart(NEW)

References: 

- lib/utils/amount_utils.dart

Create a new utility class `RawNumberFinder` that implements the independent raw number finding logic required by the PRD. The main method `findAllNumbers(String text)` should return `List<AmountCandidate>` containing ALL numeric values found in the text, without any filtering for embedded numbers. Use comprehensive regex patterns to find: basic numbers (123, 45.67), abbreviated numbers (100k, 2.5M, 1.2B), numbers with thousands separators (1,500), and numbers with currency symbols. For each match, create an `AmountCandidate` with accurate start/end positions, parsed amount using `AmountUtils.parseAbbreviatedNumber()`, and detected currency using `AmountUtils` methods. This finder should be completely independent and not filter out any numbers.

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/models/amount_candidate.dart(NEW)
- lib/utils/raw_number_finder.dart(NEW)
- lib/utils/amount_utils.dart

Implement the PRD's 4-step "Trust but Verify" approach by completely refactoring the `_parseWithMLKit()` method. Replace the current complex logic with: (1) Extract ML Kit entities and convert to `List<AmountCandidate>` with source=mlKit, (2) Independently call `RawNumberFinder.findAllNumbers(text)` to get raw candidates with source=rawNumberFinder, (3) Create new method `_consolidateCandidates()` that merges both lists, removes duplicates (same amount within tolerance), and returns comprehensive candidate list, (4) Pass consolidated list to updated `_detectAmountAmbiguity()` method. Remove the current `_extractNonVendorAmount()` method and replace with the new raw number finder. Update `_detectAmountAmbiguity()` to work with `AmountCandidate` objects instead of mixed data structures. Ensure the embedded number detection logic (`_isEmbeddedInVendorName()`) is applied during ambiguity detection, not during candidate collection. Update `_selectBestAmount()` to work with the new consolidated candidate structure.

### test/models/amount_candidate_test.dart(NEW)

References: 

- lib/models/amount_candidate.dart(NEW)

Create comprehensive unit tests for the `AmountCandidate` class. Test object creation with all properties, equality comparison between candidates, toString() method output, conversion to/from Map for backward compatibility, and edge cases like null currency or invalid positions. Include tests for different `AmountSource` values and ensure the class behaves correctly in collections (List, Set operations).

### test/utils/raw_number_finder_test.dart(NEW)

References: 

- lib/utils/raw_number_finder.dart(NEW)
- lib/models/amount_candidate.dart(NEW)

Create comprehensive unit tests for the `RawNumberFinder` class. Test the `findAllNumbers()` method with various scenarios: basic numbers (123, 45.67), abbreviated numbers (100k, 2.5M, 1.2B), numbers with currency symbols ($100, €50), numbers with thousands separators (1,500), multiple numbers in one text, embedded numbers in vendor names (should NOT be filtered out), edge cases like empty text or text with no numbers, and complex real-world examples like "dinner at lux69 100k vnd". Verify that ALL numbers are found regardless of context, positions are accurate, amounts are correctly parsed, and currency detection works properly.

### test/services/parser/multiple_number_consolidation_test.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- test/mocks/mock_entity_extractor.dart
- lib/models/amount_candidate.dart(NEW)

Create unit tests specifically for the new consolidation logic in `MlKitParserService`. Test the core PRD scenarios: "lux69 100" should trigger amount confirmation with candidates [69, 100], "restaurant45 mall 200" should detect both embedded and standalone numbers, "cafe88 bill 150" should consolidate ML Kit and raw finder results. Test edge cases: ML Kit finds candidates but raw finder doesn't, raw finder finds candidates but ML Kit doesn't, both find the same numbers (duplicate removal), both find different numbers (true consolidation). Use `MockEntityExtractorFactory` to simulate various ML Kit responses and verify the consolidation produces the expected comprehensive candidate list. Ensure proper `MlKitParserService.reset()` calls in setUp/tearDown to avoid singleton state issues.

### test/services/parser/prd_v1_1_4_integration_test.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- test/mocks/mock_entity_extractor.dart
- lib/models/parse_result.dart

Create integration tests that verify the complete PRD v1.1.4 implementation works end-to-end. Test the exact scenarios mentioned in the PRD and existing debug tests: "an com tai lux69 100" should return `ParseStatus.needsAmountConfirmation` with candidates [69, 100], "restaurant45 mall 200" should detect ambiguity, "shop123 total 500" should trigger confirmation. Test the "Trust but Verify" policy: scenarios where ML Kit returns clean results but raw finder discovers additional candidates should still trigger ambiguity detection. Include performance tests to ensure the new consolidation logic doesn't significantly impact parsing speed. Test with various mock ML Kit responses to ensure robustness. Verify that the soft fail mechanism works correctly with the new consolidated candidate approach.