I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've analyzed the codebase and identified the exact problem described in the PRD. The `_parseWithMLKit` method in `MlKitParserService` has a logical flaw where it prematurely returns a success result when `mlKitCandidates.isEmpty` and `_extractNonVendorAmount` finds a valid amount (lines 199-234). This bypasses the ambiguity detection logic that starts at line 275, preventing users from being prompted to choose between multiple potential amounts like "69" and "100" in "lux69 100".

### Approach

**Consolidate-Then-Decide Strategy**: The solution involves restructuring the parsing flow to gather ALL potential amount candidates from both ML Kit and AmountUtils before making any decisions. Instead of having separate sequential extraction paths with early returns, we'll consolidate all candidates into a single list and then apply the existing ambiguity detection logic. This ensures the "Multiple Number Soft Fail" feature works as intended by never bypassing the ambiguity check. The fix maintains backward compatibility while enabling proper multi-candidate evaluation.

### Reasoning

I explored the repository structure to understand the Flutter project layout, read the PRD document to understand the specific problem with multiple number ambiguity detection, examined the current `MlKitParserService` implementation to identify the problematic early-return logic, analyzed the existing ambiguity detection methods and ParseResult model structure, reviewed existing test infrastructure including debug tests and mock factories, and identified the exact code sections that need modification to implement the consolidate-then-decide approach.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant MLKitParser
    participant MLKit
    participant AmountUtils
    participant AmbiguityDetection
    participant UI

    Note over User,UI: Fixed Flow - Consolidate Then Decide
    User->>MLKitParser: "lux69 100"
    MLKitParser->>MLKit: annotateText()
    MLKit-->>MLKitParser: [Money("69"), Money("100")]
    
    par Parallel Extraction
        MLKitParser->>MLKitParser: Process ML Kit entities
        Note over MLKitParser: Filter embedded: "69" marked as embedded
        MLKitParser->>MLKitParser: mlKitCandidates = [100]
    and
        MLKitParser->>AmountUtils: _extractNonVendorAmount()
        AmountUtils-->>MLKitParser: customAmountResult = {amount: 100}
    end
    
    MLKitParser->>MLKitParser: Consolidate all candidates
    Note over MLKitParser: Candidates: ML Kit=[100], AmountUtils=[100], Embedded=[69]
    
    MLKitParser->>AmbiguityDetection: _detectAmountAmbiguity(allCandidates)
    AmbiguityDetection->>AmbiguityDetection: Check if multiple unique amounts
    Note over AmbiguityDetection: Found: [69, 100] - ambiguity detected
    AmbiguityDetection-->>MLKitParser: ParseResult.needsAmountConfirmation
    
    MLKitParser-->>User: needsAmountConfirmation([69, 100])
    User->>UI: Select amount: 100
    UI->>MLKitParser: completeTransaction(100)
    MLKitParser-->>User: ParseResult.success(Transaction(amount: 100))

## Proposed File Changes

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/models/parse_result.dart
- lib/utils/amount_utils.dart

**Remove the premature early-return logic**: Delete the entire block from lines 197-234 that handles the `mlKitCandidates.isEmpty` case with immediate transaction creation and return. This block currently bypasses ambiguity detection when ML Kit candidates are filtered out.

**Restructure the parsing flow to consolidate candidates**: After the ML Kit entity processing loop (around line 190), ensure that `customAmountResult` from `_extractNonVendorAmount` (already called at line 147) is available for the decision logic.

**Modify the amount selection and ambiguity detection logic**: Update the section starting around line 240 to handle the case where `mlKitCandidates.isEmpty` but `customAmountResult` exists. Instead of the current logic that only runs when both exist, ensure the ambiguity detection at line 275 (`_detectAmountAmbiguity`) is called whenever there are potential candidates from either source.

**Ensure proper fallback handling**: Only fall back to the `_fallbackParser.parseTransaction(text)` call (currently at line 233) when both ML Kit candidates are empty AND `customAmountResult` is null, ensuring we've exhausted all parsing options before falling back.

**Update currency handling**: In the amount selection logic, ensure that when AmountUtils result is selected over ML Kit results, the currency from `customAmountResult['currency']` is properly preserved and used in the final transaction.

**Maintain existing success path**: Ensure that when there's only one clear candidate (no ambiguity), the existing transaction creation logic around lines 313-340 still works correctly for building the final transaction with proper category detection and status determination.

### test/services/parser/vendor_name_parsing_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- test/mocks/mock_entity_extractor.dart
- lib/models/parse_result.dart

**Add test group for multiple number ambiguity scenarios**: Create a new test group called 'Multiple Number Ambiguity Detection' that specifically tests the scenarios described in the PRD.

**Add test case for the exact PRD scenario**: Test case 'should trigger amount confirmation for lux69 100 scenario' that simulates ML Kit finding both '69' (embedded in 'lux69') and '100' (standalone), and verifies that the result status is `ParseStatus.needsAmountConfirmation` with `candidateAmounts` containing both [69.0, 100.0].

**Add test case for abbreviated amounts**: Test case 'should trigger amount confirmation for lux69 100k scenario' that verifies the system detects ambiguity between '69' and '100k' (parsed as 100000.0), ensuring abbreviated amounts are properly included in ambiguity detection.

**Add test case for single candidate scenarios**: Test case 'should not trigger ambiguity for single clear candidate' that verifies when only one valid amount exists (e.g., 'restaurant 100k' with no embedded numbers), the system returns `ParseStatus.success` without triggering ambiguity detection.

**Add test case for currency preservation**: Test case 'should preserve currency in ambiguity scenarios' that verifies when multiple amounts are detected with currency context (e.g., 'lux69 100k vnd'), the currency is properly preserved in the `ParseResult.needsAmountConfirmation` result.

**Use MockEntityExtractorFactory**: Utilize `MockEntityExtractorFactory.simulateMultipleMoneyEntities` to create realistic test scenarios that match actual ML Kit behavior, ensuring the mocked entities have correct start/end positions for embedded detection logic.

### test/integration/parsing_pipeline_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/models/transaction_model.dart
- lib/models/parse_result.dart

**Add integration test group for multiple number scenarios**: Create a new test group 'Multiple Number Ambiguity Integration' that tests the complete end-to-end flow from user input to ParseResult.

**Add comprehensive PRD scenario test**: Test case 'should handle complete flow for lux69 100 scenario' that tests the full parsing pipeline with the exact scenario from the PRD, verifying that the user gets prompted for amount confirmation and can complete the transaction with their chosen amount.

**Add test for transaction completion flow**: Test case 'should complete transaction after amount confirmation' that simulates the user selecting an amount from the ambiguity candidates and verifies the final transaction is created correctly with the chosen amount.

**Add test for currency detection in ambiguous scenarios**: Test case 'should detect and preserve currency in multi-amount scenarios' that verifies currency detection works correctly when multiple amounts are present, ensuring the final transaction has the correct currency code.

**Add performance regression test**: Test case 'should maintain parsing performance with ambiguity detection' that measures parsing time for complex scenarios to ensure the restructured flow doesn't significantly impact performance.

**Add edge case integration tests**: Test cases for boundary conditions like 'should handle exactly 20x threshold scenarios' and 'should handle multiple embedded numbers' to ensure the complete pipeline works correctly for edge cases.

### test/debug_multiple_numbers_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- test/mocks/mock_entity_extractor.dart

**Update existing debug tests to verify the fix**: Modify the existing test cases to ensure they now properly trigger amount confirmation instead of potentially bypassing it due to the early-return bug.

**Add detailed debugging output**: Enhance the debug output in existing test cases to show the complete flow: ML Kit candidates found, AmountUtils results, whether ambiguity detection was triggered, and the final ParseResult status and candidates.

**Add new debug test for the exact PRD scenario**: Create a specific debug test case 'Debug: Exact PRD scenario - lux69 100' that provides comprehensive debugging information for the scenario described in the PRD, showing step-by-step how the fix resolves the issue.

**Add performance comparison**: Add timing measurements to compare parsing performance before and after the fix to ensure the restructured flow doesn't introduce significant performance regression.

**Add test isolation verification**: Ensure that the debug tests properly reset the singleton state between tests to avoid test interference, particularly important for the MlKitParserService singleton pattern.

### test/services/parser/mlkit_parser_service_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- test/mocks/mock_entity_extractor.dart
- test/mocks/mock_storage_service.dart

**Create comprehensive unit tests for the MlKitParserService**: Create a new test file specifically for unit testing the MlKitParserService class methods that are affected by the fix.

**Add unit tests for the restructured parsing flow**: Test group 'Parsing Flow Logic' with test cases that verify the new consolidate-then-decide approach works correctly at the method level, including tests for `_parseWithMLKit` with various candidate combinations.

**Add unit tests for ambiguity detection**: Test group 'Ambiguity Detection Logic' with test cases that verify `_detectAmountAmbiguity` works correctly with different combinations of ML Kit candidates and AmountUtils results.

**Add unit tests for amount selection**: Test group 'Amount Selection Logic' with test cases that verify `_selectBestAmount` continues to work correctly with the new flow, including preference for abbreviated amounts and proper currency handling.

**Add regression tests**: Test group 'Regression Tests' with test cases that ensure existing functionality (simple amount parsing, currency detection, transaction type detection) continues to work correctly after the flow restructuring.

**Use dependency injection**: Utilize MockEntityExtractor and MockStorageService to create isolated unit tests that don't depend on external services or singleton state.