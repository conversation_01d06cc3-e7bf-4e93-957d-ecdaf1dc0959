I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've analyzed the codebase and found that the multiple number detection implementation is already complete and should be working. The backend correctly implements the PRD v1.1.4 "Trust but Verify" approach, and the UI has all the necessary handlers for amount confirmation. However, there's a potential null safety issue in the ChatScreen that could cause runtime exceptions, preventing the UI from displaying the amount confirmation dialog. The issue is likely in line 244 of `chat_screen.dart` where `candidateTexts!` is used without proper null checking.

### Approach

The plan focuses on debugging and testing the existing implementation rather than rewriting it. I'll create comprehensive logging, debugging tools, and test scenarios to verify that the multiple number detection works end-to-end. This includes adding debug logging throughout the flow, creating ADB log monitoring scripts, fixing the null safety issue in the UI, and creating specific test cases to validate the PRD scenarios work correctly.

### Reasoning

I examined the repository structure and read the key files including `chat_screen.dart`, `mlkit_parser_service.dart`, and `parse_result.dart`. I found that the implementation is already complete with proper ML Kit integration, raw number finding, candidate consolidation, and UI handling. The issue appears to be a runtime exception in the UI layer rather than missing functionality.

## Mermaid Diagram

sequenceDiagram
    participant Dev as Developer
    participant ADB as ADB Logs
    participant App as Flutter App
    participant MLP as MlKitParserService
    participant UI as ChatScreen
    participant QR as QuickReplyWidget

    Note over Dev: Debug Flow Setup
    Dev->>ADB: Start log monitoring script
    Dev->>App: Input test scenario "lux69 100"
    
    Note over App: Backend Processing (with debug logs)
    App->>MLP: parseTransaction("lux69 100")
    MLP->>ADB: LOG: "ML Kit found entities: [69]"
    MLP->>ADB: LOG: "Raw finder found: [69, 100]"
    MLP->>ADB: LOG: "Consolidated candidates: [69, 100]"
    MLP->>ADB: LOG: "Triggering amount confirmation"
    
    Note over App: UI Processing (with debug logs)
    MLP-->>UI: ParseResult.needsAmountConfirmation
    UI->>ADB: LOG: "Amount confirmation triggered"
    UI->>ADB: LOG: "candidateAmounts: [69.0, 100.0]"
    UI->>ADB: LOG: "candidateTexts: ['69', '100']"
    
    alt candidateTexts is null (BUG)
        UI->>ADB: LOG: "ERROR: candidateTexts is null"
        UI->>UI: Runtime Exception ❌
    else candidateTexts is valid (FIXED)
        UI->>QR: Display quick replies ["69", "100", "Cancel"]
        QR->>ADB: LOG: "Quick replies rendered successfully"
        
        Note over Dev: User Interaction Test
        Dev->>QR: Click "100"
        QR->>UI: onReplySelected("100")
        UI->>ADB: LOG: "User selected amount: 100"
        UI->>MLP: completeTransaction("lux69 100", 100.0)
        MLP->>ADB: LOG: "Transaction completed with amount: 100.0"
        UI->>ADB: LOG: "✅ Amount confirmation flow successful"
    end
    
    Note over Dev: Analysis
    Dev->>ADB: Review logs for issues
    Dev->>Dev: Identify root cause

## Proposed File Changes

### lib/screens/chat_screen.dart(MODIFY)

References: 

- lib/models/parse_result.dart

Fix the null safety issue in `_handleAmountConfirmation()` method that could cause runtime exceptions. Add comprehensive debug logging throughout the amount confirmation flow to track the exact point of failure. Replace line 244 with null-safe access: `quickReplies: [...(parseResult.candidateTexts ?? []), 'Cancel']`. Add debug prints in the switch statement (around line 186) to log when amount confirmation is triggered, what candidates are received, and whether the UI handler is called. Add error handling in `_handleAmountConfirmationResponse()` to catch and log any exceptions during amount processing. Include logging in `_onQuickReplySelected()` to verify the quick reply selection flow works correctly.

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/models/amount_candidate.dart
- lib/utils/raw_number_finder.dart

Add comprehensive debug logging throughout the multiple number detection flow to track each step of the PRD v1.1.4 implementation. Add logging in `_parseWithMLKit()` to show: ML Kit candidates found, raw number finder results, consolidated candidate list, and ambiguity detection results. Add detailed logging in `_detectAmountAmbiguityFromCandidates()` to show: total candidates, non-embedded candidates, unique candidates after deduplication, and the final decision to trigger amount confirmation. Add logging in `_consolidateCandidates()` to show the merge process. Include logging in `completeTransaction()` to track the user's confirmed amount selection. Add a debug flag that can be enabled for testing to increase verbosity of logs.

### scripts/debug_multiple_numbers.sh(NEW)

Create a comprehensive debugging script that monitors ADB logs for multiple number detection. The script should: filter Flutter logs for multiple number related messages, monitor ML Kit parser service logs, track amount confirmation UI events, and display real-time logs with timestamps. Include commands to clear logs, start monitoring, and save logs to files. Add grep patterns for key log messages like 'amount confirmation triggered', 'candidates found', 'consolidating candidates', and 'quick reply selected'. Include instructions for running the script and interpreting the output. Add options to filter by specific test scenarios and save logs for analysis.

### test/debug/multiple_number_debug_test.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- test/mocks/mock_entity_extractor.dart
- lib/models/parse_result.dart

Create a comprehensive debug test that validates the entire multiple number detection flow end-to-end. Test the exact PRD scenarios: 'lux69 100' should trigger amount confirmation with candidates [69, 100], 'restaurant45 mall 200' should detect both embedded and standalone numbers, 'cafe88 bill 150' should consolidate ML Kit and raw finder results. Use `MockEntityExtractorFactory` to simulate various ML Kit responses and verify each step of the consolidation process. Include tests for edge cases: ML Kit finds nothing but raw finder finds numbers, both find same numbers (deduplication), both find different numbers (true consolidation). Add assertions to verify `ParseResult.needsAmountConfirmation` is returned with correct `candidateAmounts` and `candidateTexts`. Include performance timing to ensure the new flow doesn't impact parsing speed. Add detailed logging of each test step for debugging purposes.

### test/debug/ui_amount_confirmation_test.dart(NEW)

References: 

- lib/screens/chat_screen.dart(MODIFY)
- lib/widgets/quick_reply_widget.dart
- lib/models/parse_result.dart

Create a focused UI test that specifically validates the amount confirmation dialog works correctly. Test the ChatScreen's handling of `ParseStatus.needsAmountConfirmation` by creating mock `ParseResult` objects with various candidate configurations. Verify that `_handleAmountConfirmation()` creates the correct system message with quick replies, that the QuickReplyWidget renders the candidate amounts as buttons, and that `_onQuickReplySelected()` correctly routes to `_handleAmountConfirmationResponse()`. Test edge cases: null candidateTexts (should not crash), empty candidateTexts (should show error), single candidate (should not trigger confirmation), multiple candidates (should show all options). Use widget testing to verify the UI components are rendered correctly and that user interactions work as expected. Include tests for the 'Cancel' option and verify proper cleanup of pending state.

### test/debug/integration_prd_scenarios_test.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/screens/chat_screen.dart(MODIFY)
- test/mocks/mock_entity_extractor.dart

Create integration tests that validate the complete PRD v1.1.4 scenarios work end-to-end from user input to UI response. Test the exact scenarios mentioned in the PRD: 'an com tai lux69 100' should return `ParseStatus.needsAmountConfirmation` with candidates [69, 100], 'dinner at restaurant45 total 200' should detect ambiguity between embedded and standalone numbers, 'shopping at mall123 spent 500' should trigger confirmation. Use real `MlKitParserService` with mock entity extractor to simulate various ML Kit responses. Verify the complete flow: user types message → parser detects multiple numbers → UI shows confirmation dialog → user selects amount → transaction is completed. Include timing tests to ensure the 'Trust but Verify' approach doesn't significantly impact performance. Test the soft fail mechanism works correctly with the new consolidated candidate approach. Add comprehensive assertions for each step of the flow and detailed logging for debugging failures.

### debug_multiple_numbers_manual.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/utils/raw_number_finder.dart
- lib/models/amount_candidate.dart

Create a standalone manual testing script that can be run to test multiple number detection scenarios interactively. The script should initialize the `MlKitParserService` with debug logging enabled, present a menu of test scenarios (PRD examples, edge cases, custom input), and show detailed output for each parsing attempt. Include scenarios: 'lux69 100', 'restaurant45 mall 200', 'cafe88 bill 150', 'shop123 total 500', and allow custom input. For each test, display: original text, ML Kit entities found, raw number finder results, consolidated candidates, ambiguity detection result, and final ParseResult. Include timing information and memory usage. Add options to test with different mock ML Kit responses and to save results to files for analysis. This script will help manually verify the implementation works correctly and identify any issues in the parsing pipeline.

### test/debug/adb_log_monitor_test.dart(NEW)

References: 

- scripts/debug_multiple_numbers.sh(NEW)

Create a test that can be run while monitoring ADB logs to validate the multiple number detection flow produces the expected log output. The test should trigger various parsing scenarios and include specific log markers that can be easily identified in ADB output. Include test cases for: successful multiple number detection, UI amount confirmation display, user selection processing, and error handling. Each test should print distinctive log messages with timestamps and test identifiers. Add instructions for running the test while monitoring ADB logs using the debug script. Include expected log patterns and how to interpret the output. This test will help verify that the logging implementation works correctly and that the flow can be debugged effectively using ADB logs.