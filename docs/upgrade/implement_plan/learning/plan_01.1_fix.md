I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

After analyzing the learning system implementation, I've identified the root causes of the three bugs:

1. **Random categories instead of soft-fail**: The parsing services default to 'other' category when no category is found, instead of returning `needsCategory` status
2. **First edit learning failure**: The `LearnedAssociationService` initialization is async fire-and-forget, so it's often null during the first manual edit
3. **Learning only works from 2nd edit**: Related to the initialization timing issue above

The codebase has excellent test infrastructure with MockStorageService, TestHelpers, and comprehensive test patterns that I can leverage for investigation and validation.

### Approach

I'll create focused unit tests to reproduce each bug, then implement targeted fixes:

1. **Investigation Phase**: Write specific unit tests that reproduce the exact bug scenarios described by the user
2. **Fix Implementation**: Make minimal, targeted changes to fix the root causes without over-engineering
3. **Validation**: Ensure fixes work through integration tests and don't break existing functionality

The approach prioritizes surgical fixes over architectural changes, leveraging the existing robust test infrastructure to ensure reliability.

### Reasoning

I explored the learning system implementation by examining the LearnedAssociationService, MlKitParserService, ChatScreen, TransactionProvider, and related test files. I identified the specific code locations causing the bugs and understood the existing test patterns and infrastructure available for investigation and validation.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant Tests
    participant MlKitParser
    participant TransactionProvider
    participant LearnedService
    participant CategoryFinder
    
    Note over Tests: Bug Investigation Phase
    
    rect rgb(255, 240, 240)
        Note over Tests: Bug 1: Random Categories
        Tests->>CategoryFinder: findCategory("unknown transaction")
        CategoryFinder-->>Tests: null (no match found)
        Tests->>MlKitParser: parseTransaction("unknown transaction")
        Note over MlKitParser: BEFORE FIX: returns success with 'other'
        Note over MlKitParser: AFTER FIX: returns needsCategory
        MlKitParser-->>Tests: ParseResult.needsCategory()
    end
    
    rect rgb(240, 255, 240)
        Note over Tests: Bug 2 & 3: First Edit Learning
        Tests->>TransactionProvider: updateTransaction(banana->food)
        Note over TransactionProvider: BEFORE FIX: service might be null
        TransactionProvider->>TransactionProvider: waitForLearnedAssociationService()
        TransactionProvider->>LearnedService: learn("banana", categoryId: "food")
        LearnedService-->>TransactionProvider: success
        
        Tests->>MlKitParser: parseTransaction("banana 50")
        MlKitParser->>LearnedService: getAssociation("banana 50")
        LearnedService-->>MlKitParser: association(categoryId: "food")
        MlKitParser-->>Tests: ParseResult.success(food category)
    end
    
    rect rgb(240, 240, 255)
        Note over Tests: Integration Validation
        Tests->>User: Simulate complete workflow
        User->>MlKitParser: "Banana 100"
        MlKitParser-->>User: needsCategory
        User->>TransactionProvider: edit category to Food
        TransactionProvider->>LearnedService: learn association
        User->>MlKitParser: "Banana 50"
        MlKitParser-->>User: success with Food category
    end

## Proposed File Changes

### test/services/parser/learning_bug_investigation_test.dart(NEW)

References: 

- test/services/parser/learned_association_service_test.dart
- test/integration/learning_integration_test.dart(MODIFY)
- test/mocks/mock_storage_service.dart
- test/helpers/test_helpers.dart

Create focused unit tests to reproduce the three specific bugs reported by the user:

**Bug 1 - Random categories instead of soft-fail**: Test that when `CategoryFinderService.findCategory()` returns null, the parser should return `ParseResult.needsCategory()` instead of defaulting to 'other' category. Use MockStorageService to ensure no learned associations exist, then verify parsing returns needsCategory status.

**Bug 2 - First edit learning failure**: Test that when a user edits a transaction for the first time, the learning is triggered even if the LearnedAssociationService wasn't fully initialized. Create a TransactionProvider, immediately call updateTransaction with changed category, then verify the association was learned.

**Bug 3 - Learning only works from 2nd edit**: Test the complete scenario: parse "Banana 100" (should need category), user selects Food, then parse "Banana 50" (should auto-categorize as Food). This integration test will reveal if vendor name extraction or timing is the issue.

Use existing test patterns from `learned_association_service_test.dart` and `learning_integration_test.dart`. Include edge cases like transactions with numbers in descriptions and timing-sensitive scenarios.

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/services/parser/category_finder_service.dart
- lib/models/parse_result.dart

Fix the soft-fail logic to properly return `needsCategory` status instead of defaulting to 'other' category:

**Primary Fix**: In the `_parseWithMLKit()` method around line 204, change the transaction creation logic. Instead of `categoryId: categoryId ?? 'other'`, check if categoryId is null and return `ParseResult.needsCategory(transaction)` where transaction uses the null categoryId.

**Secondary Fix**: In the `_buildTransactionFromAssociation()` method around line 476, ensure the fallback category is 'other' only for learned associations, not for regular parsing.

**Validation**: The change should ensure that when `CategoryFinderService.findCategory()` returns null (meaning no learned association, no legacy category, and no keyword match), the parser requests user input instead of auto-assigning 'other'.

This fix addresses Bug 1 by ensuring proper soft-fail behavior when transactions don't match any existing patterns.

### lib/services/parser/fallback_parser_service.dart(MODIFY)

References: 

- lib/services/parser/category_finder_service.dart
- lib/models/parse_result.dart

Apply the same soft-fail logic fix as in MlKitParserService:

**Primary Fix**: In the `parseTransaction()` method around line 78, change the transaction creation logic. Instead of `categoryId: categoryId ?? 'other'`, check if categoryId is null and return `ParseResult.needsCategory(transaction)` where transaction uses the null categoryId.

**Consistency**: Ensure the fallback parser behaves identically to the ML Kit parser when no category can be determined, maintaining consistent user experience regardless of which parsing path is used.

This ensures Bug 1 is fixed across both parsing services, providing consistent soft-fail behavior.

### lib/models/transaction_model.dart(MODIFY)

References: 

- lib/services/parser/learned_association_service.dart(MODIFY)

Fix the initialization timing issue in TransactionProvider to ensure learning works on the first edit:

**Primary Fix**: In the `updateTransaction()` method around line 355, before checking if `_learnedAssociationService != null`, add a call to ensure the service is initialized: `await waitForLearnedAssociationService()`. This ensures the service is ready before attempting to learn.

**Initialization Fix**: Modify `_initializeLearnedAssociationService()` to be properly awaited. Consider making it return a Future and ensuring it's completed before any learning operations.

**Timing Solution**: The `waitForLearnedAssociationService()` method already exists (line 285) but isn't used in updateTransaction. Call it before the learning logic to ensure the service is ready.

This fix addresses Bugs 2 and 3 by ensuring the learning service is always initialized before manual edits trigger learning, making first-edit learning reliable.

### lib/services/parser/learned_association_service.dart(MODIFY)

Enhance the vendor name extraction to handle transactions with trailing numbers more reliably:

**Investigation First**: The existing `_extractVendorName()` method may not handle cases like "Banana 100" and "Banana 50" consistently. Review the logic around lines 270-322.

**Potential Fix**: If tests reveal that trailing numbers cause matching issues, modify the vendor extraction to:
1. Strip trailing numbers from extracted vendor names
2. Ensure "banana 100" and "banana 50" both extract to "banana"
3. Improve word-level matching in `_isPartialMatch()` to be more forgiving of numeric variations

**Fallback Enhancement**: Ensure the partial matching logic in `getAssociation()` (lines 174-182) properly handles cases where vendor extraction differs slightly between learning and lookup.

Only implement changes if the investigation tests reveal this as a root cause. The goal is to make vendor name extraction more consistent for similar transactions with different amounts.

### test/integration/learning_integration_test.dart(MODIFY)

References: 

- test/mocks/mock_storage_service.dart
- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/models/transaction_model.dart(MODIFY)

Add comprehensive integration tests for the specific bug scenarios reported by the user:

**End-to-End Bug Reproduction**: Add a test group "Bug Reproduction and Validation" that includes:

1. **First Edit Learning Test**: Create a transaction, immediately edit its category, verify learning occurred, then parse similar text and confirm it uses the learned category.

2. **Soft-Fail Validation Test**: Parse text that should not match any existing patterns, verify it returns `needsCategory` instead of defaulting to 'other'.

3. **Sequential Learning Test**: Simulate the exact "Banana 100" → edit to Food → "Banana 50" scenario to validate the complete workflow.

4. **Timing Stress Test**: Test rapid edit operations to ensure the learning service initialization doesn't cause race conditions.

Use the existing test infrastructure patterns from the file, including MockStorageService setup and proper service initialization. These tests should fail before the fixes and pass after, providing clear validation of the bug resolution.

### test/models/transaction_model_test.dart(MODIFY)

References: 

- test/helpers/test_helpers.dart
- test/mocks/mock_storage_service.dart
- lib/services/parser/learned_association_service.dart(MODIFY)

Add specific tests for the manual edit learning functionality to validate the initialization timing fix:

**New Test Group**: "Manual Edit Learning Timing" that includes:

1. **Immediate Edit Learning Test**: Create a TransactionProvider, immediately call updateTransaction with a category change, verify the learning was triggered even if the service wasn't pre-initialized.

2. **Service Initialization Race Test**: Test that multiple rapid edits don't cause initialization race conditions or missed learning opportunities.

3. **First Edit Confidence Test**: Verify that the first manual edit properly sets confidence to 1, and subsequent edits increment it correctly.

4. **Learning Persistence Test**: After a manual edit, create a new provider instance and verify the learned association persists and is accessible.

Use the existing test patterns from the file, including MockStorageService and TestHelpers. Focus on the timing aspects that were causing the original bugs, ensuring the fixes make learning reliable from the very first edit.

### test/services/parser/mlkit_parser_service_test.dart(MODIFY)

References: 

- lib/services/parser/category_finder_service.dart
- lib/models/parse_result.dart
- test/mocks/mock_storage_service.dart

Add tests to validate the soft-fail logic fix in MlKitParserService:

**New Test Group**: "Soft-Fail Behavior Validation" that includes:

1. **No Category Found Test**: Mock CategoryFinderService to return null, verify parseTransaction returns `ParseResult.needsCategory()` instead of success with 'other' category.

2. **Learning Bypass vs Soft-Fail Test**: Verify that learned associations still return success, but unknown transactions properly soft-fail.

3. **Category Assignment Priority Test**: Test the priority order: learned associations → legacy storage → keyword matching → soft-fail (not 'other' default).

4. **Fallback Consistency Test**: Ensure ML Kit and fallback parsers behave identically when no category can be determined.

Use existing test patterns and MockStorageService to control the category finding behavior. These tests should validate that the parser properly requests user input when it cannot determine a category, rather than making assumptions.