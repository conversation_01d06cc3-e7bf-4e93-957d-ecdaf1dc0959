I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

Based on my exploration of the codebase, I can see the current ParseResult implementation and its usage patterns:

1. **Current Structure**: The `ParseResult` class has 5 ParseStatus enum values and uses factory constructors for different scenarios. The `_determineAmbiguity()` method in `MlKitParserService` already detects ambiguity types but this information isn't stored in ParseResult.

2. **Usage Patterns**: ParseResult is used extensively across parser services, UI components, and tests. The factory constructors are the primary way to create ParseResult instances, and the helper methods are used for state checking.

3. **Test Coverage**: Comprehensive tests exist that validate the current 5 enum values and all existing functionality. Tests expect exactly 5 ParseStatus values and validate state consistency.

4. **Integration Points**: The `_determineAmbiguity()` method returns String? values like 'missing_amount', 'ambiguous_amount', 'ambiguous_type', and 'ambiguous_category', but this information is currently only used for logging and flow control.

### Approach

I'll extend the `ParseResult` class to capture ambiguity information while maintaining full backward compatibility. The approach will:

1. **Add New ParseStatus Values**: Add `missingAmount` and `ambiguousAmount` to the enum, updating from 5 to 7 total values.

2. **Add Ambiguity Field**: Add an optional `ambiguityType` field to store the detected ambiguity kind from the 4-priority decision tree.

3. **Extend Factory Constructors**: Add new factory constructors for the new statuses and update existing ones to optionally accept ambiguity type information.

4. **Maintain Compatibility**: Ensure all existing factory constructors work unchanged, all helper methods continue to work, and existing ParseResult usage patterns remain valid.

5. **Update Helper Methods**: Add new helper methods for the new statuses and update `requiresUserInput` logic to include the new statuses appropriately.

### Reasoning

I explored the repository structure and identified the key files. I read the current `ParseResult` implementation and the `MlKitParserService` to understand how the new `_determineAmbiguity()` method works. I examined the comprehensive test suite to understand usage patterns and compatibility requirements. I searched for all ParseResult usages across the codebase to identify integration points and ensure my changes won't break existing functionality.

## Mermaid Diagram

sequenceDiagram
    participant Parser as MlKitParserService
    participant AmbiguityDetector as _determineAmbiguity()
    participant ParseResult as ParseResult (Enhanced)
    participant UI as Future UI Components
    
    Parser->>AmbiguityDetector: Analyze consolidated candidates
    AmbiguityDetector-->>Parser: Return ambiguity type string
    
    alt ambiguity type is "missing_amount"
        Parser->>ParseResult: Create ParseResult.missingAmount(ambiguityType)
        ParseResult-->>UI: Status: missingAmount + ambiguity context
    else ambiguity type is "ambiguous_amount"
        Parser->>ParseResult: Create ParseResult.ambiguousAmount(ambiguityType)
        ParseResult-->>UI: Status: ambiguousAmount + ambiguity context
    else ambiguity type is "ambiguous_type"
        Parser->>ParseResult: Create ParseResult.needsType(ambiguityType)
        ParseResult-->>UI: Status: needsType + ambiguity context
    else ambiguity type is "ambiguous_category"
        Parser->>ParseResult: Create ParseResult.needsCategory(ambiguityType)
        ParseResult-->>UI: Status: needsCategory + ambiguity context
    else no ambiguity detected
        Parser->>ParseResult: Create existing ParseResult (no ambiguity type)
        ParseResult-->>UI: Existing behavior preserved
    end
    
    Note over ParseResult: New ambiguityType field stores<br/>detected ambiguity for future phases
    Note over UI: Future phases can access<br/>ambiguityType for contextual feedback

## Proposed File Changes

### lib/models/parse_result.dart(MODIFY)

Extend the `ParseStatus` enum to include two new values: `missingAmount` and `ambiguousAmount`. This increases the total enum values from 5 to 7.

Add a new optional `ambiguityType` field to the `ParseResult` class to store the detected ambiguity kind from the 4-priority decision tree (e.g., 'missing_amount', 'ambiguous_amount', 'ambiguous_type', 'ambiguous_category').

Update the main constructor to accept the new `ambiguityType` parameter as optional.

Add two new factory constructors:
- `ParseResult.missingAmount()` for when no valid amount candidates are found
- `ParseResult.ambiguousAmount()` for when multiple amount candidates exist and need user confirmation

Update existing factory constructors to optionally accept `ambiguityType` parameter while maintaining backward compatibility by making it optional with null default.

Add new helper methods:
- `needsMissingAmountHandling` getter to check if status is `missingAmount`
- `needsAmbiguousAmountHandling` getter to check if status is `ambiguousAmount`

Update the `requiresUserInput` getter to include the new `missingAmount` and `ambiguousAmount` statuses as they both require user intervention.

Update the `toString()` method to include the new `ambiguityType` field in the string representation.

Ensure all changes maintain full backward compatibility - existing code using ParseResult should continue to work without modifications.

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/models/parse_result.dart(MODIFY)

Update the `_parseWithMLKit()` method to utilize the new ParseResult ambiguity information. Specifically:

Modify the logic around lines 248-262 where `_determineAmbiguity()` is called. Instead of only using the ambiguity type for logging and flow control, create appropriate ParseResult objects with the detected ambiguity type:

1. When `ambiguityType == 'missing_amount'`, return `ParseResult.missingAmount()` with the ambiguity type information
2. When `ambiguityType == 'ambiguous_amount'` and amount confirmation is triggered, update the existing `ParseResult.needsAmountConfirmation()` call to include the ambiguity type
3. For other ambiguity types ('ambiguous_type', 'ambiguous_category'), continue with existing flow but pass the ambiguity type to the appropriate ParseResult factory constructors

Update the `ParseResult.needsAmountConfirmation()` call around line 898 to include the detected ambiguity type information.

Ensure that when no ambiguity is detected (ambiguityType is null), existing behavior is preserved and no ambiguity type is passed to ParseResult constructors.

Maintain all existing logic and flow - the changes should only add the ambiguity type information to ParseResult objects without changing the parsing behavior or decision tree logic.

### test/models/parse_result_test.dart(MODIFY)

References: 

- lib/models/parse_result.dart(MODIFY)

Update the test suite to cover the new ParseResult functionality while maintaining all existing test coverage:

1. **Update Enum Count Test**: Change the expected enum count from 5 to 7 in the test around line 372 to account for the new `missingAmount` and `ambiguousAmount` values.

2. **Add New Factory Constructor Tests**: Add test cases for the new factory constructors:
   - Test `ParseResult.missingAmount()` creates correct status and state
   - Test `ParseResult.ambiguousAmount()` creates correct status and state
   - Verify both new constructors properly handle ambiguity type information

3. **Add New Helper Method Tests**: Add test cases for the new helper methods:
   - Test `needsMissingAmountHandling` returns correct values for all statuses
   - Test `needsAmbiguousAmountHandling` returns correct values for all statuses

4. **Update Existing Tests**: 
   - Update `requiresUserInput` tests to include the new statuses that require user input
   - Update enum transition tests to include the new status values
   - Update toString tests to verify ambiguity type is included when present

5. **Add Ambiguity Type Tests**: Add comprehensive tests for the new `ambiguityType` field:
   - Test that ambiguity type is properly stored and retrieved
   - Test that existing constructors work with null ambiguity type (backward compatibility)
   - Test that new constructors properly handle ambiguity type information

6. **Backward Compatibility Tests**: Add specific tests to ensure all existing ParseResult usage patterns continue to work unchanged.

Ensure all existing test assertions remain valid and continue to pass.