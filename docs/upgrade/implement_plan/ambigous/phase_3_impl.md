I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

Based on my exploration of the codebase, I can see the current ChatScreen implementation and its interaction patterns:

1. **Current Ambiguity Handling**: The ChatScreen already handles the new `missingAmount` and `ambiguousAmount` statuses with basic feedback messages, but doesn't utilize the `ambiguityType` field for contextual help.

2. **Quick Reply System**: The existing `QuickReplyWidget` is well-structured and reusable, with specialized variants for transaction types and categories. The ChatScreen uses this for type selection and amount confirmation.

3. **Message Flow**: The ChatScreen creates `ChatMessage.systemWithQuickReplies` objects to display interactive messages with quick reply buttons.

4. **Available Data**: The `ParseResult` now includes the `ambiguityType` field with constants defined in `AmbiguityType` class, providing context about what type of ambiguity was detected.

5. **Current Patterns**: The existing code shows patterns for contextual messages and smart suggestions, but they're not leveraging the new ambiguity type information.

### Approach

I'll enhance the `ChatScreen` to provide contextual feedback and smart suggestions based on the `ambiguityType` field from `ParseResult`. The approach will:

1. **Create Contextual Feedback Messages**: Replace generic messages with specific, helpful feedback based on the detected ambiguity type (missing_amount, ambiguous_amount, ambiguous_type, ambiguous_category).

2. **Add Smart Quick Reply Suggestions**: Generate contextual quick reply options based on the ambiguity type and available transaction data.

3. **Enhance Message Generation**: Create helper methods to generate appropriate system messages with contextual help text for each ambiguity scenario.

4. **Maintain Backward Compatibility**: Ensure fallback to existing UI patterns when ambiguity type is unknown or null.

5. **Improve User Experience**: Provide actionable suggestions and clear guidance to help users resolve ambiguities quickly.

### Reasoning

I explored the repository structure and identified the key files. I read the current `ChatScreen` implementation to understand how it handles different ParseStatus values and uses the QuickReplyWidget. I examined the `ParseResult` model to see how the new `ambiguityType` field is structured. I reviewed the `TransactionModel` to understand available data for generating contextual suggestions. I analyzed the existing message flow and quick reply patterns to understand how to enhance them with ambiguity-aware features.

## Mermaid Diagram

sequenceDiagram
    participant User as User
    participant ChatScreen as ChatScreen
    participant ParseResult as ParseResult (Enhanced)
    participant ContextualHelper as Contextual Message Generator
    participant SmartReplies as Smart Quick Reply Generator
    participant QuickReplyWidget as QuickReplyWidget (Enhanced)
    
    User->>ChatScreen: Send transaction message
    ChatScreen->>ParseResult: Receive ParseResult with ambiguityType
    
    alt ambiguityType is "missing_amount"
        ChatScreen->>ContextualHelper: Generate missing amount message
        ContextualHelper-->>ChatScreen: "I couldn't detect an amount. Try formats like..."
        ChatScreen->>SmartReplies: Generate amount suggestions
        SmartReplies-->>ChatScreen: ["$10", "$25", "$50", "Add Amount"]
        ChatScreen->>QuickReplyWidget: Display contextual quick replies
        QuickReplyWidget-->>User: Show smart amount suggestions
    
    else ambiguityType is "ambiguous_amount"
        ChatScreen->>ContextualHelper: Generate ambiguous amount message
        ContextualHelper-->>ChatScreen: "I found multiple amounts. Which did you mean?"
        ChatScreen->>SmartReplies: Generate enhanced amount options
        SmartReplies-->>ChatScreen: Enhanced candidate list with context
        ChatScreen->>QuickReplyWidget: Display amount confirmation with help
        QuickReplyWidget-->>User: Show amounts with contextual hints
    
    else ambiguityType is "ambiguous_type"
        ChatScreen->>ContextualHelper: Generate type ambiguity message
        ContextualHelper-->>ChatScreen: "I couldn't determine transaction type because..."
        ChatScreen->>SmartReplies: Generate smart type suggestions
        SmartReplies-->>ChatScreen: Type options with contextual hints
        ChatScreen->>QuickReplyWidget: Display enhanced type selection
        QuickReplyWidget-->>User: Show types with context
    
    else ambiguityType is "ambiguous_category"
        ChatScreen->>ContextualHelper: Generate category ambiguity message
        ContextualHelper-->>ChatScreen: "Category detection was unclear..."
        ChatScreen->>SmartReplies: Generate category suggestions
        SmartReplies-->>ChatScreen: Smart category options based on description
        ChatScreen->>QuickReplyWidget: Display category suggestions
        QuickReplyWidget-->>User: Show relevant categories
    
    else ambiguityType is null/unknown
        ChatScreen->>ChatScreen: Fallback to existing UI patterns
        Note over ChatScreen: Backward compatibility maintained
    end
    
    User->>QuickReplyWidget: Select option
    QuickReplyWidget->>ChatScreen: Handle selection with context
    ChatScreen->>ChatScreen: Process selection and continue transaction flow

## Proposed File Changes

### lib/screens/chat_screen.dart(MODIFY)

References: 

- lib/models/parse_result.dart
- lib/widgets/quick_reply_widget.dart(MODIFY)
- lib/models/transaction_model.dart

Enhance the ChatScreen to handle new ambiguity types with specific feedback messages and smart quick reply suggestions:

1. **Add Contextual Message Generation**: Create a new method `_generateContextualMessage()` that takes a `ParseResult` and generates appropriate feedback messages based on the `ambiguityType` field. This method will provide specific guidance for each ambiguity type:
   - `missing_amount`: Suggest common amount formats and provide examples
   - `ambiguous_amount`: Explain why multiple amounts were detected
   - `ambiguous_type`: Provide context about transaction type detection
   - `ambiguous_category`: Explain category detection challenges

2. **Create Smart Quick Reply Generator**: Add a new method `_generateSmartQuickReplies()` that creates contextual quick reply suggestions based on ambiguity type:
   - For `missing_amount`: Suggest common amount ranges or "Add Amount" option
   - For `ambiguous_type`: Provide transaction type options with context
   - For `ambiguous_category`: Suggest likely categories based on transaction description
   - For `ambiguous_amount`: Enhanced amount selection with context

3. **Update ParseStatus Handlers**: Modify the existing switch statement in `_sendMessage()` to use the new contextual message generation:
   - Update `ParseStatus.missingAmount` handler to use `parseResult.ambiguityType`
   - Update `ParseStatus.ambiguousAmount` handler to use contextual feedback
   - Enhance other status handlers to leverage ambiguity type information when available

4. **Add Contextual Help Methods**: Create helper methods:
   - `_getAmountSuggestions()`: Generate smart amount suggestions based on transaction description
   - `_getCategorySuggestions()`: Generate category suggestions based on transaction context
   - `_getContextualHelpText()`: Provide specific help text for each ambiguity type

5. **Enhance Amount Confirmation**: Update `_handleAmountConfirmation()` to include contextual help text explaining why multiple amounts were detected and provide guidance on selection.

6. **Improve Type Selection**: Update `_handleTypeSelection()` to include contextual information about why type detection failed and provide smart suggestions.

7. **Add Fallback Handling**: Ensure that when `ambiguityType` is null or unknown, the system falls back to existing UI patterns without breaking functionality.

8. **Update Message Templates**: Create message templates for each ambiguity type that provide clear, actionable guidance to users.

All changes will maintain backward compatibility and existing functionality while adding the new contextual features.

### lib/widgets/quick_reply_widget.dart(MODIFY)

References: 

- lib/models/parse_result.dart
- lib/models/transaction_model.dart

Enhance the QuickReplyWidget to support contextual quick replies based on ambiguity types:

1. **Add Contextual Quick Reply Variants**: Create new specialized quick reply widgets for different ambiguity scenarios:
   - `AmbiguityQuickReply`: A general widget that adapts its options based on ambiguity type
   - `AmountSuggestionQuickReply`: For missing amount scenarios with smart amount suggestions
   - `CategorySuggestionQuickReply`: Enhanced category suggestions with confidence indicators
   - `TypeContextQuickReply`: Transaction type selection with contextual hints

2. **Add Visual Enhancements**: Enhance the base `QuickReplyWidget` to support:
   - Optional icons for quick reply buttons to make them more intuitive
   - Different button styles based on suggestion confidence or type
   - Subtle visual indicators for smart suggestions vs. standard options

3. **Create Smart Suggestion Builders**: Add factory constructors and helper methods:
   - `QuickReplyWidget.forAmbiguityType()`: Factory that creates appropriate quick replies based on ambiguity type
   - `QuickReplyWidget.withIcons()`: Enhanced version that supports icons alongside text
   - Helper methods to generate contextual options based on transaction data

4. **Add Accessibility Improvements**: Enhance accessibility features:
   - Better semantic labels that include context about the ambiguity
   - Screen reader friendly descriptions for each quick reply option
   - Improved focus management for keyboard navigation

5. **Support Grouped Options**: Add support for grouping related quick reply options:
   - Primary suggestions (high confidence)
   - Secondary suggestions (lower confidence)
   - Standard options (Cancel, Other, etc.)

6. **Maintain Backward Compatibility**: Ensure all existing QuickReplyWidget usage continues to work unchanged while adding the new contextual features.

The enhancements will make the quick reply system more intelligent and user-friendly while maintaining the existing simple interface for basic use cases.