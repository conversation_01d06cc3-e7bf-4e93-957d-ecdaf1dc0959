I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

Based on my exploration of the codebase, I now understand the complete picture:

1. **Current Architecture**: `MlKitParserService` already has a 4-step parsing approach with candidate consolidation and ambiguity detection via `_detectAmountAmbiguityFromCandidates()`.

2. **Existing Components**: 
   - `CategoryFinderService` returns a single category or null (no multiple matches exposed)
   - `_detectTransactionType()` returns a single type or null (no ambiguity detection)
   - `AmountUtils.extractAmountFromText()` returns null when no amount found
   - `RawNumberFinder.findAllNumbers()` returns empty list when no numbers found

3. **Test Patterns**: Tests show expectations for "missing amount" scenarios to fall back to regex parser, and existing ambiguity detection only handles multiple amount candidates.

4. **Missing Pieces**: There's no existing 4-priority decision tree or ambiguity type classification system. The user wants to add this as a new layer.

### Approach

I'll implement a `_determineAmbiguity()` method that analyzes consolidated parsing data and returns ambiguity type strings based on a 4-priority decision tree. The method will be integrated into `_parseWithMLKit()` after candidate consolidation but before the existing amount ambiguity logic. This approach maintains backward compatibility while adding the new classification system for future phases.

The 4-priority decision tree will be:
1. **missing_amount** (highest priority) - no valid amount candidates found
2. **ambiguous_amount** - multiple valid amount candidates found  
3. **ambiguous_type** - transaction type cannot be determined
4. **ambiguous_category** (lowest priority) - category cannot be determined

The method will return String? for the ambiguity type and be used for logging/future integration without changing current ParseResult behavior.

### Reasoning

I explored the codebase structure and found the main parsing service, models, and utility classes. I examined the current ambiguity detection logic, category finding service, and amount parsing utilities. I reviewed test files to understand expected behaviors and found documentation about the "Trust but Verify" approach. I searched for existing 4-priority decision tree implementations but found none, confirming this is a new feature to be added.

## Mermaid Diagram

sequenceDiagram
    participant Parser as MlKitParserService
    participant MLKit as ML Kit Service
    participant RawFinder as RawNumberFinder
    participant AmbiguityDetector as _determineAmbiguity()
    participant ExistingLogic as _detectAmountAmbiguityFromCandidates()
    
    Parser->>MLKit: Extract entities from text
    Parser->>RawFinder: Find all numbers independently
    Parser->>Parser: Consolidate candidates from both sources
    
    Note over Parser: NEW: 4-Priority Decision Tree
    Parser->>AmbiguityDetector: Analyze consolidated data
    AmbiguityDetector->>AmbiguityDetector: 1. Check missing_amount
    AmbiguityDetector->>AmbiguityDetector: 2. Check ambiguous_amount  
    AmbiguityDetector->>AmbiguityDetector: 3. Check ambiguous_type
    AmbiguityDetector->>AmbiguityDetector: 4. Check ambiguous_category
    AmbiguityDetector-->>Parser: Return ambiguity type string or null
    
    alt ambiguity type is "ambiguous_amount"
        Parser->>ExistingLogic: Trigger amount confirmation
        ExistingLogic-->>Parser: Return needsAmountConfirmation
    else no amount ambiguity
        Parser->>Parser: Continue with best candidate selection
    end

## Proposed File Changes

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/models/parse_result.dart
- lib/models/amount_candidate.dart

Add a new private method `_determineAmbiguity()` that implements the 4-priority decision tree for ambiguity detection. The method will take consolidated amount candidates, detected transaction type, and full text as parameters, returning a String? indicating the ambiguity type.

The 4-priority decision tree logic will be:
1. **missing_amount** - Check if consolidated candidates list is empty
2. **ambiguous_amount** - Check if multiple unique amount candidates exist (similar to existing logic but return string instead of ParseResult)
3. **ambiguous_type** - Check if `_detectTransactionType()` returns null
4. **ambiguous_category** - Check if category detection would fail (simulate category finding without actually calling it)

Integrate this method into `_parseWithMLKit()` after Step 3 (candidate consolidation) and before Step 4 (existing ambiguity detection). Add debug logging to print the detected ambiguity type.

Modify the existing ambiguity detection flow to only trigger `_detectAmountAmbiguityFromCandidates()` when `_determineAmbiguity()` returns 'ambiguous_amount', maintaining the current amount confirmation behavior.

Ensure all existing ParseResult statuses and behaviors remain unchanged for backward compatibility.