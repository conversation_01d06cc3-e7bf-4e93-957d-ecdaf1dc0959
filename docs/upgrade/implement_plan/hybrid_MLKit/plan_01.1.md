# Implementation Plan v1.1: Enhanced Currency Handling & Logging

## 1. Overview

This document outlines the implementation plan to address key weaknesses in the v1.0 Hybrid ML Kit Parser. The primary goals are to introduce robust, multi-currency support, handle currency-less user input gracefully via a user-configurable default, and improve logging for better diagnostics.

## 2. Problem Statement & Gaps

The current implementation has three main gaps:

1.  **Incomplete Currency Parsing**: The ML Kit service identifies money entities but discards the currency type (e.g., EUR, VND). The fallback parser uses a brittle regex that doesn't reliably handle international formats.
2.  **No Default Currency**: The system cannot handle inputs where the user omits a currency symbol (e.g., "spent 25 on lunch"). It should apply a default currency in such cases.
3.  **Missing Success Logs**: The parser logs failures and fallbacks, but a successful parse with ML Kit is not explicitly logged, making it difficult to confirm it's working as intended.

## 3. Proposed Implementation Changes

### Task 1: Evolve the Data Model

**File**: `lib/models/transaction_model.dart`

-   **Action**: Add a `currencyCode` field to the `Transaction` class.
    ```dart
    final String currencyCode; // e.g., "USD", "EUR"
    ```
-   **Details**:
    -   Update the constructor to accept `currencyCode`, defaulting to `'USD'`.
    -   Update the `copyWith` method to include `currencyCode`.

### Task 2: Implement User-Configurable Default Currency

**Files**:
-   `lib/screens/settings_screen.dart`
-   `lib/services/storage_service.dart`
-   A new provider/manager for settings.

-   **Actions**:
    1.  **Storage**: In `StorageService`, add methods `Future<void> saveDefaultCurrency(String currencyCode)` and `Future<String> getDefaultCurrency()`.
    2.  **Settings UI**: In `SettingsScreen`, add a UI element (e.g., a dropdown menu) that allows users to select their default currency from a predefined list (e.g., USD, EUR, JPY, VND).
    3.  **State Management**: The selected value will be saved to storage via the `StorageService`. The app will read this value on startup.

### Task 3: Enhance Parser Services

**Files**:
-   `lib/services/parser/mlkit_parser_service.dart`
-   `lib/services/parser/fallback_parser_service.dart`

-   **Actions**:
    1.  **ML Kit Parser**:
        -   In `_parseWithMLKit`, when a `MoneyEntity` is found, extract its `currency` property and assign it to `transaction.currencyCode`.
        -   If ML Kit does not return a currency, call `getDefaultCurrency()` and apply it.
        -   Add the log: `print('Parsing transaction with ML Kit...');` at the start of the `_parseWithMLKit` method.
    2.  **Fallback Parser**:
        -   Modify the amount-extraction regex to be more robust.
        -   If the regex does not capture a currency symbol, apply the default currency from storage.

### Task 4: Update UI to be Currency-Aware

**Files**:
-   `lib/widgets/transaction_message.dart`
-   `lib/screens/statistics_screen.dart`
-   Any other widget displaying transaction amounts.

-   **Action**: Modify all UI components that display transaction amounts to include the `currencyCode` or a corresponding symbol.
-   **Example**: Instead of displaying `25.50`, it should display `$25.50` or `25.50 USD`. A helper function for currency formatting should be created.

## 4. Summary of Changes

| Task # | Description | Files to Modify |
| :--- | :--- | :--- |
| 1 | Add `currencyCode` to `Transaction` model | `lib/models/transaction_model.dart` |
| 2 | Implement default currency setting | `lib/screens/settings_screen.dart`, `lib/services/storage_service.dart` |
| 3 | Enhance parsers for currency handling | `lib/services/parser/mlkit_parser_service.dart`, `lib/services/parser/fallback_parser_service.dart` |
| 4 | Update UI to display currency | `lib/widgets/transaction_message.dart`, `lib/screens/statistics_screen.dart` |
