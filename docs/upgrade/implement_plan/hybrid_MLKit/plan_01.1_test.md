I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I can see this is a Flutter project with MLKit implementation for parsing expense amounts and currencies. The project has:

1. **MLKit Parser Services**: Two versions (`mlkit_parser_service.dart` and `mlkit_parser_service_fixed.dart`) that use Google MLKit Entity Extraction to parse money amounts and currencies from text
2. **Fallback Parser**: A regex-based parser that works when MLKit is unavailable
3. **Supporting Services**: Category finder, currency utilities, and learned category storage
4. **Models**: Transaction, ParseResult, and Category models
5. **Current Testing**: Only basic test files at root level, no proper test folder structure

The MLKit implementation handles:
- Money entity extraction (amounts and currencies)
- Transaction type detection (expense/income/loan)
- Category finding with learning capabilities
- Fallback to regex parsing when MLKit fails
- Currency symbol/code conversion

### Approach

I'll create comprehensive unit tests for the MLKit parsing logic in a proper `test` folder structure. The tests will cover:

1. **MLKit Parser Service Tests**: Test both successful MLKit parsing and fallback scenarios
2. **Fallback Parser Tests**: Verify regex-based parsing works correctly
3. **Currency Utils Tests**: Test currency symbol/code conversions and formatting
4. **Category Finder Tests**: Test keyword matching and learned category storage
5. **Parse Result Tests**: Test the different result states (success, needs category, failed)
6. **Integration Tests**: Test the complete parsing pipeline

I'll use Flutter's built-in testing framework with proper mocking for MLKit dependencies to ensure tests are reliable and don't depend on external ML models.

### Reasoning

I analyzed the project structure and found it's a Flutter app with MLKit implementation for parsing transaction amounts and currencies. I examined the main parser services, models, and utilities to understand the parsing logic. I also checked existing test files and found only basic scripts at the root level, indicating the need for proper unit tests in a dedicated test folder.

## Mermaid Diagram

sequenceDiagram
    participant Test as Unit Tests
    participant MLKit as MLKitParserService
    participant Fallback as FallbackParserService
    participant Category as CategoryFinderService
    participant Storage as StorageService
    participant Utils as CurrencyUtils

    Test->>MLKit: parseTransaction(text)
    MLKit->>MLKit: _initialize() MLKit models
    alt MLKit Available
        MLKit->>MLKit: _parseWithMLKit(text)
        MLKit->>MLKit: Extract money entities
        MLKit->>Utils: Extract currency info
        MLKit->>Category: Find category
        MLKit->>Test: Return ParseResult
    else MLKit Unavailable
        MLKit->>Fallback: parseTransaction(text)
        Fallback->>Fallback: Extract amount with regex
        Fallback->>Utils: Extract currency info
        Fallback->>Category: Find category
        Fallback->>MLKit: Return ParseResult
        MLKit->>Test: Return ParseResult
    end

    Test->>Category: Test category finding
    Category->>Storage: Check learned categories
    Category->>Category: Match keywords
    Category->>Test: Return category ID

    Test->>Utils: Test currency operations
    Utils->>Utils: Convert symbols/codes
    Utils->>Utils: Format amounts
    Utils->>Test: Return formatted result

## Proposed File Changes

### test(NEW)

Create the main test directory following Flutter testing conventions.

### test/services(NEW)

Create services test directory to mirror the lib/services structure.

### test/services/parser(NEW)

Create parser test directory to mirror the lib/services/parser structure.

### test/models(NEW)

Create models test directory to mirror the lib/models structure.

### test/utils(NEW)

Create utils test directory to mirror the lib/utils structure.

### test/mocks(NEW)

Create mocks directory for mock objects used in testing.

### test/mocks/mock_storage_service.dart(NEW)

References: 

- lib/services/storage_service.dart

Create a mock implementation of `StorageService` for testing. This mock will simulate storage operations without requiring actual shared preferences, allowing tests to run in isolation. It will implement all methods from `lib/services/storage_service.dart` with in-memory storage for testing purposes.

### test/mocks/mock_mlkit_entities.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart

Create mock implementations of MLKit entity classes for testing. This will include mock `EntityAnnotation`, `EntityExtractor`, and `EntityExtractorModelManager` classes that simulate MLKit behavior without requiring the actual ML models. These mocks will allow testing of both successful entity extraction and failure scenarios.

### test/utils/currency_utils_test.dart(NEW)

References: 

- lib/utils/currency_utils.dart

Create comprehensive tests for `CurrencyUtils` class from `lib/utils/currency_utils.dart`. Tests will cover:
- Currency symbol to code conversion
- Currency code to symbol conversion
- Currency amount formatting with different currencies
- Decimal digit handling for different currencies (e.g., JPY with 0 decimals)
- Supported currency validation
- Edge cases with invalid currency codes
- Currency name retrieval

### test/models/parse_result_test.dart(NEW)

References: 

- lib/models/parse_result.dart
- lib/models/transaction_model.dart

Create tests for `ParseResult` model from `lib/models/parse_result.dart`. Tests will cover:
- Factory constructors (success, needsCategory, failed)
- Helper methods (isSuccess, requiresUserInput, hasError)
- toString method
- Edge cases with null values
- Proper state validation for different result types

### test/models/transaction_model_test.dart(NEW)

References: 

- lib/models/transaction_model.dart

Create tests for `Transaction` and `Category` models from `lib/models/transaction_model.dart`. Tests will cover:
- Transaction creation and validation
- JSON serialization/deserialization
- copyWith method functionality
- Category creation and validation
- TransactionType enum handling
- Edge cases with invalid data
- Default values handling

### test/services/parser/fallback_parser_service_test.dart(NEW)

References: 

- lib/services/parser/fallback_parser_service.dart
- lib/services/parser/category_finder_service.dart

Create comprehensive tests for `FallbackParserService` from `lib/services/parser/fallback_parser_service.dart`. Tests will cover:
- Amount extraction with various currency formats
- Transaction type detection for expense/income/loan patterns
- Currency extraction from text (symbols, codes, names)
- Tag extraction from hashtags
- Description creation
- Error handling for invalid inputs
- Integration with CategoryFinderService
- Edge cases with malformed text

### test/services/parser/category_finder_service_test.dart(NEW)

References: 

- lib/services/parser/category_finder_service.dart
- lib/services/parser/learned_category_storage.dart

Create tests for `CategoryFinderService` from `lib/services/parser/category_finder_service.dart`. Tests will cover:
- Category finding by keywords
- Learned category storage and retrieval
- Integration with LearnedCategoryStorage
- Category learning functionality
- Edge cases with empty text
- Keyword matching accuracy
- Data export/import functionality

### test/services/parser/mlkit_parser_service_test.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart
- lib/services/parser/fallback_parser_service.dart

Create comprehensive tests for `MlKitParserService` from `lib/services/parser/mlkit_parser_service.dart`. Tests will cover:
- MLKit initialization success and failure scenarios
- Money entity parsing with various formats
- DateTime entity parsing
- Fallback to regex when MLKit fails
- Currency extraction and validation
- Transaction type detection
- Category finding integration
- Error handling and graceful degradation
- Singleton pattern behavior
- Resource disposal
- Integration between MLKit and fallback parsing

### test/services/parser/learned_category_storage_test.dart(NEW)

References: 

- lib/services/parser/learned_category_storage.dart

Create tests for `LearnedCategoryStorage` from `lib/services/parser/learned_category_storage.dart`. Tests will cover:
- Category learning and retrieval
- Data persistence simulation
- Text normalization for learning
- Data export/import functionality
- Clear data functionality
- Edge cases with special characters
- Storage capacity limits

### test/integration(NEW)

Create integration test directory for end-to-end parsing tests.

### test/integration/parsing_pipeline_test.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart
- lib/services/parser/fallback_parser_service.dart

Create integration tests that test the complete parsing pipeline from text input to final transaction creation. Tests will cover:
- End-to-end parsing with MLKit available
- End-to-end parsing with MLKit unavailable (fallback mode)
- Real-world transaction text examples
- Performance testing with various input sizes
- Memory usage validation
- Error recovery scenarios
- Category learning persistence across parsing sessions

### test/test_data(NEW)

Create test data directory for sample transaction texts and expected results.

### test/test_data/sample_transactions.dart(NEW)

Create a comprehensive collection of sample transaction texts with expected parsing results. This will include:
- Various currency formats (symbols, codes, names)
- Different transaction types (expense, income, loan)
- Edge cases and malformed inputs
- Multi-language currency names
- Complex transaction descriptions
- Real-world examples from different regions
- Test cases for category detection
- Boundary conditions for amount parsing

### test/helpers(NEW)

Create test helpers directory for utility functions used across tests.

### test/helpers/test_helpers.dart(NEW)

References: 

- lib/models/transaction_model.dart
- lib/models/parse_result.dart

Create helper functions for testing including:
- Transaction builder functions for creating test transactions
- Assertion helpers for comparing ParseResult objects
- Mock data generators for various test scenarios
- Utility functions for setting up test environments
- Common test setup and teardown functions
- Matchers for custom assertions