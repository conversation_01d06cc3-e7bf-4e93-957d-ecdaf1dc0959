# Test Plan for Hybrid ML Kit Parser

## 1. Overview

This document outlines the unit and integration testing strategy for the Hybrid ML Kit Transaction Parser. The goal is to ensure the parser is accurate, reliable, and robust by verifying its core components, fallback mechanisms, and handling of various edge cases, with a strong focus on multi-currency and category-learning functionality.

## 2. Testing Scope

-   **Unit Tests**: Isolate and test each new service (`MlKitParserService`, `FallbackParserService`, `CategoryFinderService`, `LearnedCategoryStorage`).
-   **Integration Tests**: Test the interaction between the services and the `ChatScreen` UI.

## 3. Test Cases

### 3.1. `mlkit_parser_service_test.dart`

-   **Setup**: Mock `EntityExtractor`, `CategoryFinderService`, and `FallbackParserService`.

-   **Test Cases**:
    -   **Successful Parse (ML Kit)**:
        -   **Input**: `"spent $25.50 on lunch"`
        -   **Assert**: `_parseWithMLKit` is called, returns `ParseResult` with amount `25.50`, currency `USD`, and category `food`.
    -   **Multi-currency Parse (ML Kit)**:
        -   **Input**: `"I paid 3,000¥ for ramen"`
        -   **Assert**: Returns `ParseResult` with amount `3000`, currency `JPY`.
        -   **Input**: `"25,50€ for dinner in Berlin"`
        -   **Assert**: Returns `ParseResult` with amount `25.50`, currency `EUR`.
    -   **Currency-less Input (ML Kit)**:
        -   **Input**: `"spent 30 on snacks"`
        -   **Assert**: Returns `ParseResult` with amount `30` and `currencyCode` matching the default currency from settings.
    -   **Date/Time Extraction**:
        -   **Input**: `"paid electricity bill yesterday"`
        -   **Assert**: `transaction.date` is yesterday's date.
    -   **Fallback Trigger (Runtime Failure)**:
        -   **Setup**: Make the mocked `_entityExtractor.annotateText()` throw an exception.
        -   **Input**: `"any transaction"`
        -   **Assert**: `_fallbackParser.parseTransaction` is called.
    -   **Fallback Trigger (Initialization Failure)**:
        -   **Setup**: Simulate ML Kit model download failure during service initialization.
        -   **Input**: `"any transaction"`
        -   **Assert**: `_fallbackParser.parseTransaction` is called directly, `_parseWithMLKit` is not.

### 3.2. `fallback_parser_service_test.dart`

-   **Setup**: Mock `CategoryFinderService`.

-   **Test Cases**:
    -   **Amount & Currency Extraction**:
        -   `"$10"` -> amount: 10, currency: USD
        -   `"10$"` -> amount: 10, currency: USD
        -   `"€25.50"` -> amount: 25.50, currency: EUR
        -   `"2,500 JPY"` -> amount: 2500, currency: JPY
    -   **Currency-less Input**:
        -   `"paid 50 for ticket"` -> amount: 50, currency: [Default Currency]
    -   **Transaction Type Detection**:
        -   `"spent 100"` -> type: `expense`
        -   `"received 1000"` -> type: `income`
        -   `"borrowed 500"` -> type: `loan`

### 3.3. `category_finder_service_test.dart`

-   **Setup**: Mock `LearnedCategoryStorage` and `CategoryKeywordMap`.

-   **Test Cases**:
    -   **Keyword-Based Detection**:
        -   **Input**: `"lunch at a cafe"`
        -   **Assert**: Returns category `food`.
    -   **Learned History Detection**:
        -   **Setup**: Mock `getLearnedCategory("the local bistro")` to return `food`.
        -   **Input**: `"dinner at The Local Bistro"`
        -   **Assert**: Returns category `food`.
    -   **Learning Flow**:
        -   **Action**: Call `learnCategory("new vendor", "shopping")`.
        -   **Assert**: `saveLearnedCategory("new vendor", "shopping")` is called on the storage mock.
    -   **No Match**:
        -   **Input**: `"some random text"`
        -   **Assert**: Returns `null`.

### 3.4. Integration Tests (`chat_screen_test.dart`)

-   **Setup**: Use `Provider` to supply mocked versions of the services.

-   **Test Cases**:
    -   **End-to-End Success**:
        -   Simulate user typing `"spent $10 on coffee"`.
        -   Assert a `TransactionMessage` appears with the correct details.
    -   **Category Learning Flow**:
        -   Simulate parser returning `ParseResult(needsCategorySelection: true)`.
        -   Assert that `CategoryPickerDialog` is displayed.
        -   Simulate user selecting a category from the dialog.
        -   Assert `learnCategory` is called on the service and the transaction is updated.
    -   **Parser Failure**:
        -   Simulate parser returning `ParseResult.failed()`.
        -   Assert a system message with an error is displayed in the chat.
