I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've analyzed the current MlKitParserService implementation and identified the core problem: tight coupling where the service creates its own EntityExtractor internally, making it impossible to inject mocks for testing. The current tests are integration tests that fail because they try to initialize real ML Kit in test environments. I found that the service is used in two main production files (main.dart and chat_screen.dart) with a simple `getInstance(storageService)` call pattern, and there's an existing but unused mock structure in place.

### Approach

The solution involves implementing dependency injection by creating an abstraction layer around EntityExtractor, allowing the service to accept an optional extractor parameter while maintaining backward compatibility. This will enable proper unit testing with mocks while preserving the existing singleton pattern and production usage. The approach follows the dependency injection patterns already used by other services in the codebase like FallbackParserService and CategoryFinderService.

### Reasoning

I examined the current MlKitParserService implementation and found it creates EntityExtractor internally, making testing impossible. I analyzed the existing test file to understand the current integration test approach and found comprehensive test coverage that's failing due to ML Kit initialization. I reviewed the existing mock structure and found it's not being used due to tight coupling. I searched the codebase to understand how the service is instantiated in production and found it's used in main.dart and chat_screen.dart with simple getInstance calls. I also examined other services to understand the dependency injection patterns used in this codebase.

## Mermaid Diagram

sequenceDiagram
    participant Test as Unit Test
    participant Mock as MockEntityExtractor
    participant Service as MlKitParserService
    participant Fallback as FallbackParserService
    
    Test->>Mock: Configure mock responses
    Test->>Service: getInstance(storage, entityExtractor: mock)
    Service->>Service: Initialize with injected mock
    Test->>Service: parseTransaction(text)
    Service->>Mock: annotateText(text)
    Mock->>Service: Return mock entities
    Service->>Service: Process mock entities
    alt Mock returns empty entities
        Service->>Fallback: parseTransaction(text)
        Fallback->>Service: Return fallback result
    else Mock returns valid entities
        Service->>Service: Extract amount/type/category
        Service->>Service: Create transaction
    end
    Service->>Test: Return ParseResult
    Test->>Test: Assert expected behavior

## Proposed File Changes

### lib/services/parser/entity_extractor_base.dart(NEW)

Create an abstract base class `EntityExtractorBase` that defines the interface for entity extraction. This will include methods `annotateText(String text)` returning `Future<List<EntityAnnotationBase>>` and `close()` returning `Future<void>`. Also create an abstract `EntityAnnotationBase` class with properties `text`, `start`, `end`, and `entityType` to represent the minimal interface needed by the parser service.

### lib/services/parser/real_entity_extractor.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)

Create a concrete implementation `RealEntityExtractor` that implements `EntityExtractorBase` and wraps the actual Google ML Kit `EntityExtractor`. This adapter will handle model downloading, initialization, and provide a thin wrapper around the real ML Kit functionality. Include a concrete `RealEntityAnnotation` class that implements `EntityAnnotationBase` and wraps the actual ML Kit `EntityAnnotation` objects. This keeps the ML Kit dependency isolated and allows the main service to work with abstractions.

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/services/parser/entity_extractor_base.dart(NEW)
- lib/services/parser/real_entity_extractor.dart(NEW)

Refactor the MlKitParserService to accept dependency injection while maintaining backward compatibility. Update the `getInstance` method to accept an optional `EntityExtractorBase? entityExtractor` parameter. Modify the `_initialize` method to only create a `RealEntityExtractor` if no extractor was provided via dependency injection. Update all internal references to use `EntityAnnotationBase` instead of the concrete ML Kit types. Replace runtime type checking with proper entity type enumeration from the abstraction. Maintain the singleton pattern and all existing public methods to ensure no breaking changes for production code. Import the new abstraction files `entity_extractor_base.dart` and `real_entity_extractor.dart`.

### test/mocks/mock_entity_extractor.dart(NEW)

References: 

- lib/services/parser/entity_extractor_base.dart(NEW)
- test/mocks/mock_mlkit_entities.dart(DELETE)

Create a proper mock implementation `MockEntityExtractor` that implements `EntityExtractorBase` for testing purposes. This mock should allow configuring return values for `annotateText` calls and support simulating various scenarios like empty results, parsing errors, and different entity types. Include a `MockEntityAnnotation` class that implements `EntityAnnotationBase` with configurable properties. Provide factory methods for creating common test scenarios like money entities, datetime entities, and edge cases. This replaces the existing mock structure in `mock_mlkit_entities.dart` with a properly integrated mock that follows the new abstraction.

### test/services/parser/mlkit_parser_service_test.dart(MODIFY)

References: 

- test/mocks/mock_entity_extractor.dart(NEW)
- lib/services/parser/mlkit_parser_service.dart(MODIFY)

Convert the integration tests to proper unit tests by using the new `MockEntityExtractor`. Update all test cases to create a mock extractor with predefined responses and pass it to `getInstance(mockStorage, entityExtractor: mockExtractor)`. Replace the current integration test approach with isolated unit tests that verify the service logic without depending on real ML Kit initialization. Add specific test cases for different mock scenarios like successful parsing, empty entity results, parsing errors, and various entity types. Update the test that checks ML Kit initialization failure to properly simulate this using the mock. Remove any dependencies on real ML Kit behavior and focus on testing the service's parsing logic, fallback behavior, and error handling.

### test/mocks/mock_mlkit_entities.dart(DELETE)

Remove the old mock file since it's being replaced by the new properly integrated `MockEntityExtractor` that follows the abstraction pattern.

### lib/services/parser/mlkit_parser_service_fixed.dart(DELETE)

Remove the fixed version file since the main service file is being properly refactored with dependency injection. This eliminates code duplication and ensures there's only one authoritative implementation.

### test/integration/parsing_pipeline_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)

Update integration tests to use the new dependency injection pattern where appropriate. For tests that specifically need to test the real ML Kit integration, continue using `getInstance(mockStorage)` without the extractor parameter. For tests that focus on parsing logic rather than ML Kit integration, consider using the mock extractor to make tests more reliable and faster. Update any imports if needed to reference the refactored service.

### test/performance/learning_performance_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)

Update performance tests to use mock extractors where appropriate to ensure consistent and reliable performance measurements. This will eliminate variability from real ML Kit initialization and focus on measuring the actual parsing logic performance. Update any imports if needed to reference the refactored service.

### test/integration/learning_integration_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)

Update learning integration tests to use the new dependency injection pattern. For tests focusing on learning logic rather than ML Kit integration, use mock extractors to make tests more reliable. Update any imports if needed to reference the refactored service.

### test/services/parser/learning_bug_investigation_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)

Update the learning bug investigation test to use mock extractors for more reliable and isolated testing. This will help identify learning-related bugs without interference from ML Kit initialization issues. Update any imports if needed to reference the refactored service.