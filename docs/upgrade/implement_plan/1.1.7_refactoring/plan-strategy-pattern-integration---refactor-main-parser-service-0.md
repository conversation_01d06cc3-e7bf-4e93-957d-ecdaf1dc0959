import 'package:logger/logger.dart';
import 'package:uuid/uuid.dart';

/// Utility class for structured logging with unique correlation ID generation.
/// 
/// This class provides a centralized logging interface for the parsing pipeline
/// that generates unique correlation IDs for each parse operation. All log messages
/// are prefixed with the correlation ID to enable easy filtering and tracking
/// of related log entries across the entire parsing flow.
/// 
/// Usage:
/// ```dart
/// // Start a new parse operation and get correlation ID
/// final parseId = ParseLogger.start("buy coffee 5$");
/// 
/// // Log messages with correlation ID
/// ParseLogger.i(parseId, "Trying strategy: MlKitStrategy");
/// ParseLogger.d(parseId, "ML Kit found 2 entities");
/// ParseLogger.w(parseId, "Ambiguous amount detected", error);
/// ParseLogger.e(parseId, "Strategy failed", error, stackTrace);
///
```
/// 
/// All log messages are formatted as: `[parse:<id>] <message>`
/// This allows easy filtering in logs: `grep "parse:a1b2c3d4" logs.txt`
class ParseLogger {
  static Logger _logger = Logger(
    printer: PrettyPrinter(
      methodCount: 0,
      errorMethodCount: 3,
      lineLength: 120,
      colors: true,
      printEmojis: false,
    ),
  );

  static const Uuid _uuid = Uuid();

  /// Starts a new parse operation and generates a unique correlation ID.
  /// 
  /// This method should be called at the beginning of each parse operation
  /// to generate a unique 8-character correlation ID and log the start message.
  /// 
  /// [text] The input text being parsed
  /// 
  /// Returns the generated correlation ID that should be used for all
  /// subsequent log messages related to this parse operation.
  static String start(String text) {
    final id = _uuid.v4().substring(0, 8);
    _logger.i('[parse:$id] START: "$text"');
    return id;
  }

  /// Logs a debug message with the specified correlation ID.
  /// 
  /// Debug messages are used for detailed tracing of the parsing flow
  /// and are typically only visible in debug builds or when debug
  /// logging is explicitly enabled.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The debug message to log
  static void d(String id, String message) {
    _logger.d('[parse:$id] $message');
  }

  /// Logs an info message with the specified correlation ID.
  /// 
  /// Info messages are used for important events in the parsing flow
  /// such as strategy selection, successful parsing, etc.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The info message to log
  static void i(String id, String message) {
    _logger.i('[parse:$id] $message');
  }

  /// Logs a warning message with the specified correlation ID.
  /// 
  /// Warning messages are used for recoverable errors or unexpected
  /// conditions that don't prevent parsing from continuing.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The warning message to log
  /// [error] Optional error object to include in the log
  static void w(String id, String message, [Object? error]) {
    _logger.w('[parse:$id] $message', error: error);
  }

  /// Logs an error message with the specified correlation ID.
  /// 
  /// Error messages are used for serious errors that may prevent
  /// parsing from completing successfully.
  /// 
  /// [id] The correlation ID from the start() method
  /// [message] The error message to log
  /// [error] Optional error object to include in the log
  /// [stackTrace] Optional stack trace to include in the log
  static void e(String id, String message, [Object? error, StackTrace? stackTrace]) {
    _logger.e('[parse:$id] $message', error: error, stackTrace: stackTrace);
  }

  /// Sets a custom log output for testing purposes.
  ///
  /// This method allows injection of custom LogOutput implementations
  /// for testing, such as MemoryLogOutput to capture log records
  /// for verification in tests.
  ///
  /// [output] The LogOutput implementation to use
  static void setLogOutput(LogOutput output) {
    _logger = Logger(
      printer: PrettyPrinter(
        methodCount: 0,
        errorMethodCount: 3,
        lineLength: 120,
        colors: true,
        printEmojis: false,
      ),
      output: output,
    );
  }
}
}
  });
  }
}

Add deprecation annotation: `@Deprecated('Use TransactionParsingService instead. This class will be removed in a future version.')`

Strip down the implementation from 900+ lines to approximately 50 lines by removing all private methods and keeping only the public API surface.

Implement delegation pattern for all public methods:
- `getInstance()` creates and returns TransactionParsingService singleton, wrapping it in MlKitParserService façade
- `parseTransaction()` delegates to `_transactionParsingService.parseTransaction()`
- `learnCategory()` delegates to `_transactionParsingService.learnCategory()`
- `completeTransaction()` delegates to `_transactionParsingService.completeTransaction()`
- `isReady` getter delegates to `_transactionParsingService.isReady`
- `categoryFinder` getter delegates to `_transactionParsingService.categoryFinder`
- `dispose()` delegates to `_transactionParsingService.dispose()`
- `resetInstance()` calls `TransactionParsingService.resetInstance()`

Maintain exact same method signatures, return types, and error handling to ensure no behavioral changes during the transition period.

Keep all necessary import statements that existing code and tests depend on.

Add internal reference to TransactionParsingService singleton and manage its lifecycle.

### lib/main.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the Provider pattern integration to use TransactionParsingService instead of MlKitParserService.

Update import statement to import TransactionParsingService instead of MlKitParserService.

Change the ValueNotifier type from `ValueNotifier<MlKitParserService?>` to `ValueNotifier<TransactionParsingService?>` on line 25.

Update the ChangeNotifierProvider type annotation on line 32 to use the new service type.

Modify the `_initializeMlKitInBackground` function signature on line 48 to accept `ValueNotifier<TransactionParsingService?>` instead of the old type.

Change the service initialization call on line 55 from `MlKitParserService.getInstance()` to `TransactionParsingService.getInstance()`.

Preserve all other aspects of the initialization flow:
- Keep the same background initialization timing and pattern
- Maintain the same error handling for ML Kit failures
- Preserve the performance measurement and logging
- Keep the same Provider setup for other services (ThemeProvider, TransactionProvider)
- Maintain the same app startup sequence and UI rendering

### lib/screens/chat_screen.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update ChatScreen to use TransactionParsingService instead of MlKitParserService while preserving all existing UI workflows.

Update import statement to import TransactionParsingService.

Change the service reference on line 25 from `MlKitParserService? _parserService;` to `TransactionParsingService? _parserService;`.

Update the Provider access in `_initializeParserService()` on line 84 to use `ValueNotifier<TransactionParsingService?>` instead of the old type.

Update the Provider access in `_onMlKitServiceChanged()` on line 112 to use the new service type.

Preserve all existing functionality:
- Keep the same listener pattern for service availability changes
- Maintain the same `parseTransaction()` call and ParseResult handling logic
- Preserve all state management for pending transactions (type, category, amount confirmation)
- Keep all UI workflows for handling different ParseResult types (success, needsCategory, needsType, needsAmountConfirmation, missingAmount, ambiguousAmount, failed)
- Maintain the same error handling and fallback message patterns
- Keep the same disposal and cleanup logic for removing listeners
- Preserve all quick reply handling and transaction completion flows

### test/integration/parsing_pipeline_test.dart(MODIFY)

References: 

- test/services/parser/mlkit_parser_service_test.dart(MODIFY)
- test/helpers/test_helpers.dart
- test/mocks/mock_entity_extractor.dart
- test/mocks/mock_storage_service.dart
- test/mocks/mock_category_finder_service.dart

Create comprehensive integration tests for the new strategy pipeline to verify end-to-end parsing behavior.

Implement test scenarios covering the complete strategy chain:

**Association Hit Flow**: Configure MockLearnedAssociationService to return a learned association, verify that LearnedAssociationStrategy returns ParseResult.success immediately without calling other strategies.

**ML Kit Success Flow**: Configure association service to return null, set up MockEntityExtractor to find entities, verify MlKitStrategy processes ML Kit + raw finder candidates and returns success with proper transaction fields.

**Amount Ambiguity Flow**: Configure ML Kit and raw finder to find different amounts, verify MlKitStrategy returns needsAmountConfirmation with correct candidate lists and formatted display texts.

**ML Kit Unavailable Flow**: Set mlKitAvailable flag to false, verify MlKitStrategy falls back to raw finder only and still produces valid results.

**Full Fallback Flow**: Configure all strategies above to return null or throw errors, verify FallbackRegexStrategy handles the input and always returns a non-null ParseResult.

**Strategy Order Verification**: Test that strategies are called in the correct order (learned association → ML Kit → fallback) and that the first non-null result is returned.

**Currency and Type Detection**: Test currency extraction from text vs default currency fallback, and transaction type detection for expenses, income, and loans.

**Category Handling**: Test needsCategory result when CategoryFinderService returns null, and successful category assignment when found.

Use established mock patterns:
- Import and configure MockEntityExtractor, MockStorageService, MockCategoryFinderService, MockLearnedAssociationService
- Follow the same setUp/tearDown patterns with singleton reset
- Use TestHelpers for creating test data
- Assert on ParseResult types, transaction field values, and strategy execution behavior

### test/services/parser/mlkit_parser_service_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the comprehensive unit test suite to use TransactionParsingService instead of MlKitParserService through mechanical search-and-replace.

Replace all instances of `MlKitParserService` with `TransactionParsingService` throughout the file:
- Variable declarations: `late TransactionParsingService service;`
- getInstance() calls: `service = await TransactionParsingService.getInstance(...)`
- resetInstance() calls: `TransactionParsingService.resetInstance();`
- Type annotations in method signatures and comments
- Class references in test descriptions and documentation

Update the import statement to import TransactionParsingService instead of MlKitParserService.

Preserve all existing test structure and functionality:
- Keep the exact same test organization and grouping
- Maintain all mock injection patterns for EntityExtractorBase, StorageService, LocalizationService
- Preserve all setUp/tearDown patterns with singleton reset
- Keep all test scenarios covering initialization, parsing, fallback behavior, currency handling, category learning, edge cases, error handling, performance tests, and regression tests
- Maintain all assertions and expected behaviors
- Keep all debug print statements and manual validation helpers
- Preserve all mock configurations and test data
- Maintain all performance measurement patterns with Stopwatch

The tests should continue to validate the same functionality but now through the new TransactionParsingService interface.

### test/debug/integration_prd_scenarios_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the PRD v1.1.4 integration test suite to use TransactionParsingService instead of MlKitParserService.

Replace service references:
- Change `late MlKitParserService parserService;` to `late TransactionParsingService parserService;`
- Update `parserService = await MlKitParserService.getInstance(...)` to use `TransactionParsingService.getInstance(...)`
- Update import statement to import TransactionParsingService

Preserve all existing test functionality:
- Keep the exact same PRD v1.1.4 core scenarios testing Trust but Verify approach
- Maintain all integration test scenarios for amount confirmation, embedded vs standalone number detection, and candidate consolidation
- Preserve all edge case integration tests for ML Kit empty results, deduplication, and raw finder fallback
- Keep all performance integration tests with timing measurements and completion time limits
- Maintain all end-to-end flow validation from input to user confirmation and transaction completion
- Preserve all mock injection patterns and configurations
- Keep all debug print statements for manual validation and step-by-step tracing
- Maintain all setUp/tearDown patterns and singleton reset procedures
- Keep all performance measurement with Stopwatch and timing assertions

### test/performance/learning_performance_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the learning performance test suite to use TransactionParsingService instead of MlKitParserService.

Replace service references:
- Change `MlKitParserService.resetInstance();` to `TransactionParsingService.resetInstance();`
- Update `final mlKitService = await MlKitParserService.getInstance(...)` to use `TransactionParsingService.getInstance(...)`
- Update all variable type declarations from MlKitParserService to TransactionParsingService
- Update import statement to import TransactionParsingService

Preserve all existing performance test functionality:
- Keep all learning performance tests measuring speed of learned association lookup vs ML Kit parsing
- Maintain all efficiency tests with large numbers of associations and text normalization
- Preserve all memory usage tests ensuring no memory leaks with repeated learn/clear cycles
- Keep all concurrent operation tests for thread safety and timing
- Maintain all storage performance tests measuring persistence and loading of large datasets
- Preserve all integration performance tests simulating end-to-end learning workflow
- Keep all timing measurements with Stopwatch and performance threshold assertions
- Maintain all mock setup patterns for storage and entity extraction
- Preserve all singleton reset patterns in tearDown methods
- Keep all test scenarios comparing learning bypass vs full ML Kit parsing performance

### test/debug/multiple_number_debug_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the multiple number detection debug test suite to use TransactionParsingService instead of MlKitParserService.

Replace all service references:
- Change `late MlKitParserService parserService;` to `late TransactionParsingService parserService;`
- Update `parserService = await MlKitParserService.getInstance(...)` to use `TransactionParsingService.getInstance(...)`
- Update import statement to import TransactionParsingService

Preserve all existing debug test functionality:
- Keep all multiple number detection scenarios with detailed logging
- Maintain all mock injection patterns for EntityExtractorBase and other dependencies
- Preserve all setUp/tearDown patterns with singleton reset
- Keep all debug print statements for step-by-step tracing and manual validation
- Maintain all test scenarios for embedded vs standalone number detection
- Preserve all candidate consolidation and ambiguity detection tests
- Keep all performance measurements and timing validations

### test/debug/adb_log_monitor_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the ADB log monitor test suite to use TransactionParsingService instead of MlKitParserService.

Replace all service references:
- Change `late MlKitParserService parserService;` to `late TransactionParsingService parserService;`
- Update `parserService = await MlKitParserService.getInstance(...)` to use `TransactionParsingService.getInstance(...)`
- Update import statement to import TransactionParsingService
- Update any log pattern expectations that reference the old class name

Preserve all existing ADB monitoring functionality:
- Keep all log monitoring and pattern validation tests
- Maintain all integration flow markers and debug output
- Preserve all mock injection patterns and test setup
- Keep all performance measurement and timing validation
- Maintain all setUp/tearDown patterns with singleton reset

### test/services/parser/prd_v1_1_4_integration_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the PRD v1.1.4 integration test suite to use TransactionParsingService instead of MlKitParserService.

Replace all service references:
- Change `late MlKitParserService service;` to `late TransactionParsingService service;`
- Update `MlKitParserService.resetInstance();` to `TransactionParsingService.resetInstance();`
- Update `service = await MlKitParserService.getInstance(...)` to use `TransactionParsingService.getInstance(...)`
- Update import statement to import TransactionParsingService

Preserve all existing integration test functionality:
- Keep all Trust but Verify core scenarios
- Maintain all candidate consolidation and ambiguity detection tests
- Preserve all mock injection patterns and configurations
- Keep all setUp/tearDown patterns with singleton reset
- Maintain all test assertions and expected behaviors

### test/services/parser/vendor_name_parsing_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the vendor name parsing test suite to use TransactionParsingService instead of MlKitParserService.

Replace all service references:
- Update `MlKitParserService.resetInstance();` to `TransactionParsingService.resetInstance();`
- Update `final service = await MlKitParserService.getInstance(...)` to use `TransactionParsingService.getInstance(...)`
- Update import statement to import TransactionParsingService

Preserve all existing vendor name parsing test functionality:
- Keep all embedded number detection tests
- Maintain all vendor name vs amount disambiguation scenarios
- Preserve all mock injection patterns and test setup
- Keep all setUp/tearDown patterns with singleton reset

### test/services/parser/multiple_number_consolidation_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the multiple number consolidation test suite to use TransactionParsingService instead of MlKitParserService.

Replace all service references:
- Change `late MlKitParserService service;` to `late TransactionParsingService service;`
- Update `MlKitParserService.resetInstance();` to `TransactionParsingService.resetInstance();`
- Update service initialization calls to use TransactionParsingService
- Update import statement to import TransactionParsingService

Preserve all existing consolidation test functionality:
- Keep all candidate consolidation logic tests
- Maintain all deduplication and merging scenarios
- Preserve all mock injection patterns and test setup
- Keep all setUp/tearDown patterns with singleton reset

### test/services/parser/learning_bug_investigation_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the learning bug investigation test suite to use TransactionParsingService instead of MlKitParserService.

Replace all service references:
- Change `late MlKitParserService mlkitService;` to `late TransactionParsingService mlkitService;`
- Update `MlKitParserService.resetInstance();` to `TransactionParsingService.resetInstance();`
- Update `mlkitService = await MlKitParserService.getInstance(...)` to use `TransactionParsingService.getInstance(...)`
- Update import statement to import TransactionParsingService

Preserve all existing bug investigation functionality:
- Keep all learning bug investigation scenarios
- Maintain all category learning and association tests
- Preserve all mock injection patterns and test setup
- Keep all setUp/tearDown patterns with singleton reset

### debug_multiple_numbers_manual.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the manual debug script to use TransactionParsingService instead of MlKitParserService.

Replace all service references:
- Update `final parserService = await MlKitParserService.getInstance(...)` to use `TransactionParsingService.getInstance(...)`
- Update function parameter types from `MlKitParserService` to `TransactionParsingService`
- Update import statement to import TransactionParsingService

Preserve all existing manual testing functionality:
- Keep all PRD scenario testing capabilities
- Maintain all custom input testing features
- Preserve all mock injection and configuration patterns
- Keep all debug output and manual validation helpers

### test/debug_multiple_numbers_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the debug multiple numbers test to use TransactionParsingService instead of MlKitParserService.

Replace all service references:
- Update `final service = await MlKitParserService.getInstance(...)` to use `TransactionParsingService.getInstance(...)`
- Update import statement to import TransactionParsingService

Preserve all existing debug test functionality:
- Keep all multiple number detection scenarios
- Maintain all performance measurement with Stopwatch
- Preserve all mock injection patterns and test setup

### test/debug_vendor_name_parsing.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the debug vendor name parsing script to use TransactionParsingService instead of MlKitParserService.

Replace all service references:
- Update `MlKitParserService.resetInstance();` to `TransactionParsingService.resetInstance();`
- Update `final service = await MlKitParserService.getInstance(...)` to use `TransactionParsingService.getInstance(...)`
- Update import statement to import TransactionParsingService

Preserve all existing debug functionality:
- Keep all vendor name parsing scenarios
- Maintain all performance measurement and iteration testing
- Preserve all mock injection patterns and test setup

### test/performance/startup_performance_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart(NEW)

Update the startup performance test to use TransactionParsingService instead of MlKitParserService.

Replace all service references:
- Update `MlKitParserService.resetInstance();` to `TransactionParsingService.resetInstance();`
- Update any service initialization calls to use TransactionParsingService
- Update import statement to import TransactionParsingService

Preserve all existing startup performance test functionality:
- Keep all performance measurement scenarios
- Maintain all timing assertions and thresholds
- Preserve all mock injection patterns and test setup