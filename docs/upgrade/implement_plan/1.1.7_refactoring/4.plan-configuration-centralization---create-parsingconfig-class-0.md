I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The codebase has several hardcoded constants scattered across parsing services and strategies that affect parsing behavior. The current strategy pattern architecture uses constructor dependency injection consistently, making it straightforward to add configuration as another dependency. The test infrastructure is well-established with comprehensive mock patterns and helper utilities. There's a discrepancy between embedded letter thresholds in different files (2 vs 3), suggesting the need for centralized configuration. The deprecated MlKitParserService façade needs to support configuration pass-through for backward compatibility.

### Approach

I'll create a centralized `ParsingConfig` class to extract hardcoded values from the parsing logic and implement dependency injection throughout the strategy pattern. The approach follows the established dependency injection patterns in the codebase, making configuration optional to maintain backward compatibility. The design prioritizes immutability and testability while providing sensible defaults for all configurable values. Configuration will be injected at the top level (TransactionParsingService) and passed down to strategies via constructor injection, following the same pattern used for other dependencies like StorageService and CategoryFinderService.

### Reasoning

I analyzed the parsing services and strategies to identify all hardcoded constants and magic numbers that should be configurable. I found embedded letter thresholds (2 and 3), amount formatting cutoffs (1000, 1000000, 1000000000), default currency ('USD'), and abbreviation patterns ('[kKmMbB]'). I examined the current dependency injection architecture in TransactionParsingService and strategies to understand how configuration should be threaded through the system. I studied the existing test infrastructure to understand testing patterns, mock usage, and how configuration tests should be structured following established conventions.

## Mermaid Diagram

sequenceDiagram
    participant Client as Client Code
    participant TPS as TransactionParsingService
    participant Config as ParsingConfig
    participant LAS as LearnedAssociationStrategy
    participant MKS as MlKitStrategy
    participant FRS as FallbackRegexStrategy
    participant Tests as Unit Tests

    Note over Client, Tests: Configuration Injection and Usage Flow

    Client->>Config: Create custom ParsingConfig
    Config->>Config: Set embeddedLetterThreshold=1, defaultCurrency='EUR'
    
    Client->>TPS: getInstance(storage, config=customConfig)
    TPS->>TPS: Store config in _config field
    TPS->>TPS: _initializeStrategies()
    
    TPS->>LAS: new LearnedAssociationStrategy(..., config)
    TPS->>MKS: new MlKitStrategy(..., config)
    TPS->>FRS: new FallbackRegexStrategy(..., config)
    
    Client->>TPS: parseTransaction("A2B coffee")
    TPS->>LAS: execute(context)
    LAS->>Config: Use config.defaultCurrency for fallback
    LAS-->>TPS: null (no association)
    
    TPS->>MKS: execute(context)
    MKS->>Config: Use config.strictEmbeddedLetterThreshold=1
    MKS->>MKS: Check if "2" is embedded (1 letter each side)
    MKS->>MKS: With threshold=1, "2" is embedded
    MKS-->>TPS: ParseResult with different behavior
    
    Note over Tests: Configuration Testing
    Tests->>Config: Test defaults, copyWith, equality
    Tests->>MKS: Test with custom config vs default
    Tests->>Tests: Verify behavior changes based on config

## Proposed File Changes

### lib/services/parser/parsing_config.dart(NEW)

Create an immutable configuration class that centralizes all hardcoded parsing constants.

Implement the following structure:
- Make the class immutable with `@immutable` annotation
- Define fields for all configurable constants:
  - `embeddedLetterThreshold` (default: 2) - threshold for basic embedded detection
  - `strictEmbeddedLetterThreshold` (default: 3) - threshold for strict embedded detection
  - `thousandCutoff` (default: 1000.0) - cutoff for thousand abbreviation in display
  - `millionCutoff` (default: 1000000.0) - cutoff for million abbreviation in display
  - `billionCutoff` (default: 1000000000.0) - cutoff for billion abbreviation in display
  - `defaultCurrency` (default: 'USD') - fallback currency when none detected
  - `abbreviationPattern` (default: '[kKmMbB]') - regex pattern for amount abbreviations

Provide a const constructor with named parameters and default values for all fields.

Include a static `defaults` constant for easy access to default configuration.

Implement `copyWith` method for creating modified configurations while preserving immutability.

Override `operator==` and `hashCode` for proper equality comparison and testing.

Add comprehensive documentation for each field explaining its purpose and impact on parsing behavior.

Import required dependencies: `package:flutter/foundation.dart` for `@immutable` annotation.

### lib/services/parser/transaction_parsing_service.dart(MODIFY)

References: 

- lib/services/parser/parsing_config.dart(NEW)

Update TransactionParsingService to support ParsingConfig dependency injection while maintaining backward compatibility.

Add a private `_config` field to store the configuration instance.

Modify the `getInstance` factory method to accept an optional `ParsingConfig? config` parameter. When null, use `ParsingConfig.defaults` to maintain backward compatibility.

Update the `_createInstance` helper method to accept and store the configuration parameter.

Modify the `_initializeStrategies` method to pass the configuration to each strategy constructor:
- Pass `_config` to `LearnedAssociationStrategy` constructor
- Pass `_config` to `MlKitStrategy` constructor  
- Pass `_config` to `FallbackRegexStrategy` constructor

Update the `initializeInBackground` static method to also accept the optional config parameter and pass it through to the main initialization flow.

Ensure all existing method signatures remain compatible by making the config parameter optional with a default value.

Add import statement for the new `ParsingConfig` class.

Maintain the same singleton pattern and initialization flow while threading configuration through to strategies.

### lib/services/parser/strategies/mlkit_strategy.dart(MODIFY)

References: 

- lib/services/parser/parsing_config.dart(NEW)

Update MlKitStrategy to use ParsingConfig instead of hardcoded constants.

Add a private `_config` field of type `ParsingConfig` to store the configuration.

Modify the constructor to accept `ParsingConfig config` as an additional required parameter and store it in the `_config` field.

Replace all hardcoded constants with configuration references:
- In `_isEmbeddedInVendorName` method: replace `beforeLetters >= 3 && afterLetters >= 3` with `beforeLetters >= _config.strictEmbeddedLetterThreshold && afterLetters >= _config.strictEmbeddedLetterThreshold`
- In `_hasAbbreviation` method: replace hardcoded `[kKmMbB]` regex pattern with `_config.abbreviationPattern`
- In `_createBasicTransaction` method: replace hardcoded `'USD'` with `_config.defaultCurrency`
- In any currency extraction methods: use `_config.abbreviationPattern` for abbreviation detection

Update the `_formatAmountForDisplay` method to use configuration cutoffs if the method needs to be enhanced to match the main service formatting (currently simplified).

Ensure all regex patterns that use the abbreviation pattern are updated to use the configurable pattern.

Add import statement for `ParsingConfig`.

Maintain all existing method signatures and behavior while making the underlying constants configurable.

### lib/services/parser/strategies/learned_association_strategy.dart(MODIFY)

References: 

- lib/services/parser/parsing_config.dart(NEW)

Update LearnedAssociationStrategy to use ParsingConfig for consistency with other strategies.

Add a private `_config` field of type `ParsingConfig` to store the configuration.

Modify the constructor to accept `ParsingConfig config` as an additional required parameter and store it in the `_config` field.

Replace any hardcoded constants with configuration references:
- In `_extractCurrencyFromText` method: if abbreviation patterns are used, replace with `_config.abbreviationPattern`
- Replace any default currency references with `_config.defaultCurrency`
- Ensure consistency with other strategies in terms of configuration usage

Note: This strategy has minimal hardcoded constants compared to MlKitStrategy, but updating it ensures consistency across the strategy pattern and provides a foundation for future configuration needs.

Add import statement for `ParsingConfig`.

Maintain all existing functionality while making the strategy configurable for consistency with the overall architecture.

### lib/services/parser/strategies/fallback_regex_strategy.dart(MODIFY)

References: 

- lib/services/parser/parsing_config.dart(NEW)

Update FallbackRegexStrategy to use ParsingConfig for default currency consistency.

Add a private `_config` field of type `ParsingConfig` to store the configuration.

Modify the constructor to accept `ParsingConfig config` as an additional required parameter and store it in the `_config` field.

Replace hardcoded constants with configuration references:
- In `_createBasicTransaction` method: replace hardcoded `'USD'` with `_config.defaultCurrency`

Note: This strategy primarily delegates to FallbackParserService, so configuration changes are minimal. The main benefit is ensuring consistent default currency usage across all strategies.

Add import statement for `ParsingConfig`.

Maintain the delegation pattern to FallbackParserService while ensuring configuration consistency for fallback scenarios.

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/services/parser/parsing_config.dart(NEW)
- lib/services/parser/transaction_parsing_service.dart(MODIFY)

Update the deprecated MlKitParserService façade to support ParsingConfig pass-through for backward compatibility.

Modify the `getInstance` static method to accept an optional `ParsingConfig? config` parameter.

Update the `initializeInBackground` static method to also accept the optional config parameter.

Pass the configuration parameter through to the underlying `TransactionParsingService.getInstance` and `TransactionParsingService.initializeInBackground` calls.

Ensure the façade maintains complete backward compatibility by making the config parameter optional with a default value.

This allows existing code using the deprecated service to optionally provide configuration while maintaining the same API surface.

Add import statement for `ParsingConfig`.

Maintain all existing delegation patterns while enabling configuration support for users who haven't migrated to the new TransactionParsingService yet.

### test/services/parser/parsing_config_test.dart(NEW)

References: 

- lib/services/parser/parsing_config.dart(NEW)

Create comprehensive unit tests for the ParsingConfig class following established testing patterns.

Implement test groups covering:

**Defaults Group**: Test that `ParsingConfig.defaults` produces all expected default values:
- `embeddedLetterThreshold` should be 2
- `strictEmbeddedLetterThreshold` should be 3
- `thousandCutoff` should be 1000.0
- `millionCutoff` should be 1000000.0
- `billionCutoff` should be 1000000000.0
- `defaultCurrency` should be 'USD'
- `abbreviationPattern` should be '[kKmMbB]'

**Constructor Group**: Test that the const constructor works with custom values and that all fields are properly set.

**CopyWith Group**: Test that `copyWith` method correctly:
- Overrides only specified fields while keeping others unchanged
- Handles single field changes
- Handles multiple field changes
- Returns a new instance (immutability)

**Equality Group**: Test that equality and hashCode work correctly:
- Two instances with same values should be equal and have same hashCode
- Two instances with different values should not be equal
- Test various combinations of field differences

**Immutability Group**: Verify that the class is properly immutable and cannot be modified after construction.

Use the established testing patterns from existing parser tests, including proper test organization with `group` and `test` blocks.

Import required dependencies: `flutter_test` and the `ParsingConfig` class.

### test/services/parser/strategies/mlkit_strategy_config_test.dart(NEW)

References: 

- lib/services/parser/strategies/mlkit_strategy.dart(MODIFY)
- lib/services/parser/parsing_config.dart(NEW)
- test/helpers/test_helpers.dart
- test/mocks/mock_storage_service.dart
- test/mocks/mock_category_finder_service.dart

Create configuration-specific tests for MlKitStrategy to verify that configuration changes affect parsing behavior.

Implement test groups following established patterns from existing strategy tests:

**Setup**: Use the same mock pattern as existing tests:
- `MockStorageService` for storage operations
- `MockCategoryFinderService` for category finding
- `MockEntityExtractor` for ML Kit entity extraction
- Standard `setUp` and `tearDown` methods for mock initialization

**Embedded Letter Threshold Tests**: Test that custom `strictEmbeddedLetterThreshold` values change embedded detection behavior:
- Create test scenarios with text containing numbers surrounded by different amounts of letters
- Test with threshold=1 vs default threshold=3
- Verify that the same input produces different results based on configuration
- Use text patterns like "A2B" (1 letter each side) vs "ABC123DEF" (3+ letters each side)

**Default Currency Tests**: Test that custom `defaultCurrency` values are used:
- Configure strategy with non-USD default currency
- Parse text without explicit currency
- Verify that the resulting transaction uses the configured default currency
- Test with various currency codes (EUR, GBP, etc.)

**Abbreviation Pattern Tests**: Test that custom `abbreviationPattern` affects abbreviation detection:
- Configure with different regex patterns
- Test parsing of amounts with various suffixes
- Verify that only configured patterns are recognized

**Integration Tests**: Test that configuration is properly passed through and used:
- Create strategy with custom configuration
- Verify that all configuration fields are accessible and used correctly
- Test error handling when configuration is invalid

Use existing test helpers like `TestHelpers.createTestTransaction()` and `ParseResultMatchers` for assertions.

Follow the same import patterns and test structure as existing strategy tests.

### test/services/parser/strategies/learned_association_strategy_config_test.dart(NEW)

References: 

- lib/services/parser/strategies/learned_association_strategy.dart(MODIFY)
- lib/services/parser/parsing_config.dart(NEW)
- test/mocks/mock_learned_association_service.dart
- test/mocks/mock_storage_service.dart

Create configuration tests for LearnedAssociationStrategy to ensure configuration consistency.

Implement test groups following the same patterns as other strategy configuration tests:

**Setup**: Use established mock patterns:
- `MockLearnedAssociationService` for association retrieval
- `MockStorageService` for storage operations
- Standard `setUp` and `tearDown` methods

**Default Currency Tests**: Test that custom `defaultCurrency` configuration is used:
- Configure strategy with non-USD default currency
- Create learned association without explicit currency
- Parse text and verify the resulting transaction uses configured default currency
- Test with various currency codes

**Configuration Consistency Tests**: Test that configuration is properly stored and accessible:
- Create strategy with custom configuration
- Verify that configuration fields are properly set
- Test that configuration doesn't interfere with learned association logic

**Integration Tests**: Test interaction between configuration and learned associations:
- Test scenarios where learned association has currency vs configuration default
- Verify that learned association data takes precedence over configuration defaults
- Test fallback to configuration when association data is incomplete

Note: Since LearnedAssociationStrategy has minimal configurable constants, these tests focus on ensuring consistency and proper configuration handling rather than extensive behavioral changes.

Use the same testing infrastructure and patterns as other strategy tests, including `TestHelpers` and established assertion patterns.

### test/services/parser/strategies/fallback_regex_strategy_config_test.dart(NEW)

References: 

- lib/services/parser/strategies/fallback_regex_strategy.dart(MODIFY)
- lib/services/parser/parsing_config.dart(NEW)
- lib/services/parser/fallback_parser_service.dart
- test/mocks/mock_storage_service.dart

Create configuration tests for FallbackRegexStrategy to ensure proper configuration handling.

Implement test groups following established patterns:

**Setup**: Use standard mock patterns:
- `MockStorageService` and `MockLocalizationService` for FallbackParserService dependencies
- Create `FallbackParserService` with mocks and inject into strategy
- Standard `setUp` and `tearDown` methods

**Default Currency Tests**: Test that custom `defaultCurrency` configuration is used in error scenarios:
- Configure strategy with non-USD default currency
- Trigger error conditions that cause fallback to `_createBasicTransaction`
- Verify that the basic transaction uses the configured default currency
- Test with various currency codes

**Configuration Consistency Tests**: Test that configuration is properly stored and accessible:
- Create strategy with custom configuration
- Verify that configuration doesn't interfere with delegation to FallbackParserService
- Test that configuration is used only in appropriate fallback scenarios

**Error Handling Tests**: Test that configuration is used correctly during error scenarios:
- Simulate FallbackParserService errors
- Verify that error fallback uses configured default currency
- Test that configuration provides consistent behavior across error cases

Note: Since FallbackRegexStrategy primarily delegates to FallbackParserService, these tests focus on ensuring configuration is used correctly in the limited scenarios where the strategy creates its own transactions.

Use the same testing infrastructure as other strategy tests, maintaining consistency with established patterns.

### test/integration/parsing_config_integration_test.dart(NEW)

References: 

- lib/services/parser/transaction_parsing_service.dart(MODIFY)
- lib/services/parser/parsing_config.dart(NEW)
- test/integration/parsing_pipeline_test.dart
- test/helpers/test_helpers.dart

Create integration tests to verify end-to-end configuration functionality across the entire parsing pipeline.

Implement comprehensive integration test scenarios:

**Setup**: Use established integration test patterns:
- Initialize `TransactionParsingService` with custom configuration
- Set up all required mocks (`MockStorageService`, `MockEntityExtractor`, etc.)
- Use standard `setUp` and `tearDown` methods with singleton reset

**End-to-End Configuration Tests**: Test that configuration affects the complete parsing pipeline:
- Create `TransactionParsingService` with custom `ParsingConfig`
- Parse various text inputs that would be affected by configuration changes
- Verify that results differ from default configuration behavior
- Test configuration propagation through the strategy chain

**Strategy Chain Configuration Tests**: Test that all strategies receive and use configuration:
- Configure with custom embedded letter thresholds
- Test text that would trigger different strategies (learned association, ML Kit, fallback)
- Verify that each strategy uses the custom configuration appropriately
- Test strategy selection and execution with configuration variations

**Backward Compatibility Tests**: Test that existing code continues to work:
- Initialize service without explicit configuration (should use defaults)
- Verify that default behavior is unchanged
- Test that optional configuration parameters work correctly

**Configuration Validation Tests**: Test edge cases and error scenarios:
- Test with extreme configuration values
- Verify that invalid configurations are handled gracefully
- Test configuration consistency across multiple parsing operations

**Performance Tests**: Ensure configuration doesn't impact performance:
- Compare parsing performance with default vs custom configuration
- Verify that configuration lookup doesn't add significant overhead
- Test memory usage with different configuration scenarios

Use existing integration test infrastructure and patterns, including performance measurement utilities and comprehensive mock setups.

Follow the same import and organization patterns as other integration tests.