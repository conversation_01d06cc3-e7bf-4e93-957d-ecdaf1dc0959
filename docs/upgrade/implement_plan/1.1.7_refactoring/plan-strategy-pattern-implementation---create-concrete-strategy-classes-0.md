I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current parsing services follow consistent async patterns but are implemented as monolithic classes with complex internal state management. The existing ParsingStrategy interface and ParsingContext are well-designed for the strategy pattern implementation. The codebase uses constructor dependency injection patterns in some places but relies on singletons in others, requiring careful handling of service initialization. The test infrastructure is comprehensive with established mock patterns, but lacks mocks for CategoryFinderService and LearnedAssociationService. Many helper methods in MlKitParserService are shared with other parsing flows, necessitating duplication rather than extraction to maintain backward compatibility. The entity extraction system is well-abstracted with proper interfaces for testing and real implementations.

### Approach

I'll implement three concrete parsing strategies that extract logic from existing services while maintaining complete backward compatibility. The approach uses constructor dependency injection for clean separation and testability, duplicates shared helper methods to avoid breaking existing code, and follows established testing patterns. Each strategy implements the existing ParsingStrategy interface and can work independently. The LearnedAssociationStrategy handles learned associations, MlKitStrategy implements the Trust but Verify ML Kit approach with candidate consolidation and ambiguity detection, and FallbackRegexStrategy wraps the existing regex parser. All strategies receive their dependencies via constructor injection rather than accessing singletons internally, ensuring clean architecture and easy testing.

### Reasoning

I analyzed the existing parsing architecture by examining MlKitParserService, FallbackParserService, and LearnedAssociationService to understand their dependencies, initialization patterns, and method interactions. I cataloged all helper methods used by the ML Kit parsing logic and identified which ones are shared with other parts of the codebase. I examined the test infrastructure to understand mocking patterns, test setup procedures, and assertion styles used throughout the project. I studied the entity extraction interfaces and ML Kit integration to understand how EntityExtractorBase works and how to handle optional dependencies. I identified missing mock implementations that need to be created and determined the exact constructor signatures needed for each strategy based on their dependencies.

## Mermaid Diagram

sequenceDiagram
    participant Test as Unit Tests
    participant LAS as LearnedAssociationStrategy
    participant MKS as MlKitStrategy  
    participant FRS as FallbackRegexStrategy
    participant Context as ParsingContext
    participant Services as Dependencies

    Note over Test, Services: Strategy Implementation & Testing Flow

    Test->>Context: Create ParsingContext with text & locale
    Test->>Services: Setup mock dependencies (StorageService, etc.)
    
    Test->>LAS: Create strategy with LearnedAssociationService
    Test->>LAS: Call execute(context)
    LAS->>Services: Check learned associations
    alt Association found
        LAS->>LAS: Build transaction from association
        LAS-->>Test: Return ParseResult.success()
    else No association
        LAS-->>Test: Return null (decline to handle)
    end

    Test->>MKS: Create strategy with EntityExtractor & services
    Test->>MKS: Call execute(context)
    MKS->>Services: Extract entities via ML Kit (if available)
    MKS->>MKS: Run RawNumberFinder independently
    MKS->>MKS: Consolidate ML Kit + Raw candidates
    MKS->>MKS: Detect amount ambiguity
    alt Multiple amounts
        MKS-->>Test: Return needsAmountConfirmation
    else Single amount
        MKS->>Services: Find category
        MKS-->>Test: Return success/needsCategory/needsType
    end

    Test->>FRS: Create strategy with FallbackParserService
    Test->>FRS: Call execute(context)
    FRS->>Services: Delegate to FallbackParserService
    FRS-->>Test: Return ParseResult (never null)

    Note over Test, Services: All strategies work independently with injected dependencies

## Proposed File Changes

### lib/services/parser/strategies/learned_association_strategy.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart
- lib/services/parser/parsing_strategy.dart
- lib/services/parser/parsing_context.dart

Create a new strategy that implements the ParsingStrategy interface to handle learned association checking.

Implement the following structure:
- Constructor accepting LearnedAssociationService, StorageService, and Uuid as required dependencies
- execute() method that calls learnedAssociationService.getAssociation(context.text) and returns null if no association found, otherwise builds a Transaction using the association data and returns ParseResult.success()
- name getter returning 'LearnedAssociationStrategy'

Copy the following helper methods from `/Users/<USER>/code/Projects/money_lover_chat/lib/services/parser/mlkit_parser_service.dart`:
- _buildTransactionFromAssociation() method (lines 669-700) for building transactions from learned associations
- _extractCurrencyFromText() method (lines 553-593) for currency detection
- _extractTags() method (lines 638-651) for hashtag extraction

Handle the asynchronous nature of LearnedAssociationService properly and use StorageService for default currency fallback when no currency is specified in the association or text. Generate unique transaction IDs using the provided Uuid instance.

Import required dependencies: dart:async, uuid package, transaction_model.dart, parse_result.dart, amount_utils.dart, currency_utils.dart, learned_association_service.dart, storage_service.dart, parsing_strategy.dart, parsing_context.dart

### lib/services/parser/strategies/mlkit_strategy.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart
- lib/services/parser/parsing_strategy.dart
- lib/services/parser/parsing_context.dart
- lib/utils/raw_number_finder.dart

Create a new strategy that implements the ParsingStrategy interface to handle ML Kit parsing with the Trust but Verify approach.

Implement the following structure:
- Constructor accepting optional EntityExtractorBase, required StorageService, CategoryFinderService, Uuid, and boolean mlKitAvailable flag
- execute() method that implements the complete _parseWithMLKit logic from `/Users/<USER>/code/Projects/money_lover_chat/lib/services/parser/mlkit_parser_service.dart` (lines 211-333)
- name getter returning 'MlKitStrategy'

Copy ALL helper methods used by the ML Kit parsing logic from `/Users/<USER>/code/Projects/money_lover_chat/lib/services/parser/mlkit_parser_service.dart`:
- _parseMoneyEntityToCandidate() (lines 704-755)
- _consolidateCandidates() (lines 759-797) 
- _detectAmountAmbiguityFromCandidates() (lines 801-889)
- _selectBestAmountFromCandidates() (lines 892-917)
- _isEmbeddedInVendorName() (lines 481-504)
- _hasAbbreviation() (lines 399-402)
- _getSearchPatternForAbbreviation() (lines 405-440)
- _countSurroundingLetters() (lines 443-479)
- _formatAmountForDisplay() (lines 513-526)
- _parseDateTimeEntity() (lines 529-537)
- _removeEntityFromText() (lines 540-548)
- _extractCurrencyFromText() (lines 553-593)
- _detectTransactionType() (lines 598-630)
- _createDescription() (lines 633-635)
- _extractTags() (lines 638-651)
- _createFallbackTransaction() (lines 654-666)

Handle the optional EntityExtractorBase dependency by checking isInitialized before use and gracefully falling back to RawNumberFinder-only processing when ML Kit is unavailable. Use the mlKitAvailable flag to determine whether to attempt entity extraction.

Implement the complete Trust but Verify approach: ML Kit entity extraction + independent RawNumberFinder + candidate consolidation + ambiguity detection + best candidate selection.

Import required dependencies: dart:async, flutter/foundation.dart, uuid package, transaction_model.dart, parse_result.dart, amount_candidate.dart, currency_utils.dart, amount_utils.dart, raw_number_finder.dart, storage_service.dart, category_finder_service.dart, entity_extractor_base.dart, parsing_strategy.dart, parsing_context.dart

### lib/services/parser/strategies/fallback_regex_strategy.dart(NEW)

References: 

- lib/services/parser/fallback_parser_service.dart
- lib/services/parser/parsing_strategy.dart
- lib/services/parser/parsing_context.dart

Create a new strategy that implements the ParsingStrategy interface to handle fallback regex parsing.

Implement the following structure:
- Constructor accepting FallbackParserService as a required dependency
- execute() method that delegates to _fallbackParser.parseTransaction(context.text, locale: context.locale) and returns the result
- name getter returning 'FallbackRegexStrategy'

This strategy serves as a simple wrapper around the existing FallbackParserService without any code duplication. It always returns a non-null ParseResult since the fallback parser is designed to handle all inputs and never decline.

Ensure that the locale from the ParsingContext is properly passed through to the FallbackParserService to maintain localization support.

Import required dependencies: dart:async, fallback_parser_service.dart, parsing_strategy.dart, parsing_context.dart, parse_result.dart

### test/mocks/mock_category_finder_service.dart(NEW)

References: 

- test/mocks/mock_storage_service.dart
- test/mocks/mock_localization_service.dart

Create a mock implementation of CategoryFinderService following the established mock patterns from `/Users/<USER>/code/Projects/money_lover_chat/test/mocks/mock_storage_service.dart` and `/Users/<USER>/code/Projects/money_lover_chat/test/mocks/mock_localization_service.dart`.

Implement the following structure:
- Mock the CategoryFinderService interface with findCategory(text, type) returning String? and learnCategory(text, categoryId) methods
- Configurable mock responses using a Map<String, String> for text-to-categoryId mappings
- Support for default category fallback when no specific mapping is found
- Error simulation capability with boolean flag
- Helper methods: setMockCategory(text, categoryId), setDefaultCategory(categoryId), simulateError(bool), reset()
- Constructor that doesn't require dependencies (pure mock)

The mock should return null for unknown categories unless a default category is configured, and should be able to simulate various CategoryFinderService behaviors for comprehensive testing.

Import required dependencies: transaction_model.dart for TransactionType enum

### test/mocks/mock_learned_association_service.dart(NEW)

References: 

- test/mocks/mock_storage_service.dart
- test/mocks/mock_localization_service.dart
- lib/services/parser/learned_association_service.dart

Create a mock implementation of LearnedAssociationService following the established mock patterns from `/Users/<USER>/code/Projects/money_lover_chat/test/mocks/mock_storage_service.dart` and `/Users/<USER>/code/Projects/money_lover_chat/test/mocks/mock_localization_service.dart`.

Implement the following structure:
- Mock the LearnedAssociationService interface with getAssociation(text) returning Future<LearnedAssociation?> and learn(text, {type, categoryId, confirmedAmount}) methods
- Configurable mock responses using a Map<String, LearnedAssociation> for text-to-association mappings
- Error simulation capability with boolean flag
- Async delay simulation for testing timing scenarios
- Helper methods: setMockAssociation(text, association), clearMockAssociations(), simulateError(bool), setDelay(Duration), reset()
- Constructor that doesn't require dependencies (pure mock)
- Support for partial text matching similar to the real service

The mock should return null for unknown text unless a mock association is configured, and should be able to simulate various LearnedAssociationService behaviors including errors and delays.

Import required dependencies: dart:async, learned_association_service.dart for LearnedAssociation class, transaction_model.dart for TransactionType enum

### test/services/parser/strategies/learned_association_strategy_test.dart(NEW)

References: 

- test/services/parser/parsing_strategy_interface_test.dart
- test/services/parser/fallback_parser_service_test.dart
- test/helpers/test_helpers.dart

Create comprehensive unit tests for LearnedAssociationStrategy following the testing patterns from `/Users/<USER>/code/Projects/money_lover_chat/test/services/parser/parsing_strategy_interface_test.dart` and `/Users/<USER>/code/Projects/money_lover_chat/test/services/parser/fallback_parser_service_test.dart`.

Implement the following test groups:
- **Strategy Interface Compliance**: Test that execute() returns ParseResult or null appropriately, and name getter returns correct value
- **Association Found Cases**: Mock LearnedAssociationService to return associations, verify ParseResult.success with correct transaction fields (amount, type, category, currency, description, tags)
- **No Association Cases**: Mock LearnedAssociationService to return null, verify execute() returns null (strategy declines to handle)
- **Currency Handling**: Test default currency fallback when no currency specified in association or text
- **Amount Extraction**: Test priority of confirmed amount from association vs amount extracted from text
- **Transaction Building**: Verify all transaction fields are properly set from association data and text parsing
- **Error Handling**: Test graceful handling of LearnedAssociationService errors

Use the established testing patterns:
- Import flutter_test, strategy class, models, TestHelpers from `/Users/<USER>/code/Projects/money_lover_chat/test/helpers/test_helpers.dart`, MockStorageService, and the new MockLearnedAssociationService
- Follow setUp() pattern with mock initialization and dependency injection
- Use TestHelpers.createTestTransaction(), TestHelpers.createSuccessParseResult() for test data creation
- Use standard expect() assertions and ParseResultMatchers for validation
- Test with various text inputs and association configurations

### test/services/parser/strategies/mlkit_strategy_test.dart(NEW)

References: 

- test/services/parser/mlkit_parser_service_test.dart
- test/services/parser/parsing_strategy_interface_test.dart
- test/helpers/test_helpers.dart
- test/mocks/mock_entity_extractor.dart

Create comprehensive unit tests for MlKitStrategy following the testing patterns from `/Users/<USER>/code/Projects/money_lover_chat/test/services/parser/mlkit_parser_service_test.dart` and `/Users/<USER>/code/Projects/money_lover_chat/test/services/parser/parsing_strategy_interface_test.dart`.

Implement the following test groups:
- **Strategy Interface Compliance**: Test that execute() returns ParseResult and name getter returns correct value
- **ML Kit Available Cases**: Test with MockEntityExtractor providing money/datetime entities, verify proper entity processing
- **ML Kit Unavailable Cases**: Test with null EntityExtractor or mlKitAvailable=false, verify fallback to RawNumberFinder-only processing
- **Candidate Consolidation**: Test ML Kit + RawNumberFinder candidate merging, deduplication, and position-based sorting
- **Ambiguity Detection**: Test multiple unique amounts trigger ParseResult.needsAmountConfirmation with proper candidate lists
- **Best Candidate Selection**: Test preference for non-embedded amounts, abbreviations, and currency preservation
- **Embedded Number Detection**: Test vendor name embedding logic with various text patterns
- **Currency Detection**: Test currency extraction from entities, text symbols, codes, and names
- **Transaction Type Detection**: Test expense/income/loan detection with negative amounts and keywords
- **Category Finding**: Test category lookup via CategoryFinderService and unknown category handling
- **Error Handling**: Test entity extraction errors, parsing failures, and graceful degradation

Use the established testing patterns:
- Import flutter_test, strategy class, models, TestHelpers from `/Users/<USER>/code/Projects/money_lover_chat/test/helpers/test_helpers.dart`, MockStorageService, MockEntityExtractor from `/Users/<USER>/code/Projects/money_lover_chat/test/mocks/mock_entity_extractor.dart`, and the new MockCategoryFinderService
- Use MockEntityExtractorFactory for creating various entity extraction scenarios
- Follow setUp() pattern with mock initialization and dependency injection
- Test with various text inputs including abbreviations (100k, 2.5M), multiple numbers, embedded numbers in vendor names
- Verify all ParseResult types: success, needsCategory, needsType, needsAmountConfirmation, failed, missingAmount, ambiguousAmount
- Test the complete Trust but Verify flow with realistic transaction texts

### test/services/parser/strategies/fallback_regex_strategy_test.dart(NEW)

References: 

- test/services/parser/fallback_parser_service_test.dart
- test/services/parser/parsing_strategy_interface_test.dart
- test/helpers/test_helpers.dart

Create unit tests for FallbackRegexStrategy following the testing patterns from `/Users/<USER>/code/Projects/money_lover_chat/test/services/parser/fallback_parser_service_test.dart` and `/Users/<USER>/code/Projects/money_lover_chat/test/services/parser/parsing_strategy_interface_test.dart`.

Implement the following test groups:
- **Strategy Interface Compliance**: Test that execute() returns ParseResult and name getter returns correct value
- **Delegation Behavior**: Test that strategy properly delegates to FallbackParserService and returns identical results
- **Locale Passing**: Test that context.locale is correctly passed through to FallbackParserService.parseTransaction()
- **Never Returns Null**: Test that strategy always returns ParseResult (never null) since fallback parser handles all inputs
- **Various Input Types**: Test expense, income, and loan transactions to ensure proper delegation
- **Error Cases**: Test malformed input handling and verify errors are properly propagated
- **Localization Support**: Test with different locales (en, es) to ensure locale context passing works correctly

Use the established testing patterns:
- Import flutter_test, strategy class, FallbackParserService, models, TestHelpers from `/Users/<USER>/code/Projects/money_lover_chat/test/helpers/test_helpers.dart`, MockStorageService, MockLocalizationService
- Create real FallbackParserService with mocks and inject into strategy for testing
- Follow setUp() pattern with mock initialization
- Use existing test patterns and sample data from fallback_parser_service_test.dart
- Verify that strategy results match direct FallbackParserService calls for the same inputs
- Test with various transaction texts and locales to ensure complete delegation behavior