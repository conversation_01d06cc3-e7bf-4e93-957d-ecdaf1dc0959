I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current ChatScreen uses 4 separate nullable fields to track pending transaction state, with `_pendingOriginalText` always paired with one of the 3 ParseResult fields. The ParseStatus enum has 5 values that require user input: `needsCategory`, `needsType`, `needsAmountConfirmation`, `missingAmount`, and `ambiguousAmount`. The existing test infrastructure is comprehensive with established patterns for testing model classes, factory constructors, enums, and validation logic. The codebase follows immutable design patterns with copyWith methods, manual equality implementation, and extensive validation. The subsequent phase will replace the 4 nullable fields with a single `PendingTransactionState?` field, making this purely additive work.

### Approach

I'll create a centralized `PendingTransactionState` class that encapsulates all pending transaction data currently scattered across 4 nullable fields in ChatScreen. The approach uses an immutable design with factory constructors for each pending type, validation to ensure ParseStatus matches the intended stage, and a ParseStatus extension for state management helpers. The design follows established patterns in the codebase for model classes with enums, factory constructors, and comprehensive validation. All existing functionality remains unchanged since this is purely additive - no existing code is modified, only new model infrastructure is added with complete unit test coverage.

### Reasoning

I analyzed the current state management pattern in ChatScreen and found 4 nullable fields (`_pendingTypeSelection`, `_pendingCategorySelection`, `_pendingAmountConfirmation`, `_pendingOriginalText`) that track incomplete transactions requiring user input. I examined the ParseResult class and ParseStatus enum to understand which statuses trigger pending states and what data is available. I studied the existing test infrastructure to understand the project's testing conventions for model classes, including factory constructors, immutability testing, equality validation, and error handling patterns. I identified that the `requiresUserInput` getter in ParseResult already identifies the 5 statuses that need pending state management.

## Mermaid Diagram

sequenceDiagram
    participant Test as Unit Tests
    participant PTS as PendingTransactionState
    participant PSE as ParseStatus Extension
    participant PR as ParseResult
    participant Stage as PendingStage Enum

    Note over Test, Stage: Model Creation and Validation Flow

    Test->>PR: Create ParseResult with needsCategory status
    Test->>PTS: Call forCategorySelection(text, parseResult)
    PTS->>PR: Validate parseResult.status == needsCategory
    PTS->>Stage: Set stage = PendingStage.categorySelection
    PTS-->>Test: Return PendingTransactionState instance

    Test->>PTS: Call isTypeSelection getter
    PTS->>Stage: Check stage == PendingStage.typeSelection
    PTS-->>Test: Return false

    Test->>PTS: Call isCategorySelection getter  
    PTS->>Stage: Check stage == PendingStage.categorySelection
    PTS-->>Test: Return true

    Note over Test, Stage: Extension Method Testing

    Test->>PSE: Call needsCategory.isSoftFail
    PSE-->>Test: Return true

    Test->>PSE: Call needsCategory.toPendingStage()
    PSE-->>Test: Return PendingStage.categorySelection

    Test->>PSE: Call success.isSoftFail
    PSE-->>Test: Return false

    Note over Test, Stage: Error Validation Testing

    Test->>PR: Create ParseResult with success status
    Test->>PTS: Call forCategorySelection(text, parseResult)
    PTS->>PR: Validate parseResult.status == needsCategory
    PTS-->>Test: Throw ArgumentError (status mismatch)

    Note over Test, Stage: Equality and Immutability Testing

    Test->>PTS: Create two identical instances
    Test->>PTS: Compare with operator==
    PTS-->>Test: Return true (equal)

    Test->>PTS: Call copyWith(originalText: "new text")
    PTS-->>Test: Return new instance with updated text

## Proposed File Changes

### lib/models/pending_transaction_state.dart(NEW)

References: 

- lib/models/parse_result.dart(MODIFY)
- lib/screens/chat_screen.dart

Create an immutable class that encapsulates all pending transaction state currently managed by 4 separate nullable fields in ChatScreen.

Implement the following structure:
- Import required dependencies: `package:flutter/foundation.dart` for `@immutable`, and `parse_result.dart` for ParseResult
- Define `PendingStage` enum with values: `typeSelection`, `categorySelection`, `amountConfirmation`, `missingAmount`
- Create `@immutable` class `PendingTransactionState` with final fields:
  - `ParseResult parseResult` (required) - the parse result containing transaction data
  - `String originalText` (required) - the original user input text
  - `PendingStage stage` (required) - discriminator for the type of pending state

Implement factory constructors with validation:
- `PendingTransactionState.forTypeSelection(String originalText, ParseResult parseResult)` - validates parseResult.status is `needsType`
- `PendingTransactionState.forCategorySelection(String originalText, ParseResult parseResult)` - validates parseResult.status is `needsCategory`
- `PendingTransactionState.forAmountConfirmation(String originalText, ParseResult parseResult)` - validates parseResult.status is `needsAmountConfirmation` or `ambiguousAmount`
- `PendingTransactionState.forMissingAmount(String originalText, ParseResult parseResult)` - validates parseResult.status is `missingAmount`

Each factory constructor should throw `ArgumentError` if the ParseStatus doesn't match the expected stage.

Implement convenience getters:
- `bool get isTypeSelection => stage == PendingStage.typeSelection`
- `bool get isCategorySelection => stage == PendingStage.categorySelection`
- `bool get isAmountConfirmation => stage == PendingStage.amountConfirmation`
- `bool get isMissingAmount => stage == PendingStage.missingAmount`
- `Transaction get transaction => parseResult.transaction`
- `List<double>? get candidateAmounts => parseResult.candidateAmounts`
- `List<String>? get candidateTexts => parseResult.candidateTexts`
- `String? get ambiguityType => parseResult.ambiguityType`

Implement `copyWith` method for immutability:
- Allow updating any field while preserving others
- Maintain validation constraints

Implement equality and hashCode:
- Override `operator==` to compare all fields
- Override `hashCode` using `Object.hash(parseResult, originalText, stage)`

Implement `toString()` method for debugging:
- Include all fields in a readable format

Add comprehensive documentation explaining the purpose, usage patterns, and relationship to ChatScreen state management.

### lib/models/parse_result.dart(MODIFY)

References: 

- lib/models/pending_transaction_state.dart(NEW)

Add an extension to ParseStatus enum to support pending state management.

Add the following extension after the ParseResult class definition:

```dart
/// Extension on ParseStatus to support pending transaction state management
extension ParseStatusExtension on ParseStatus {
  /// Returns true if this status indicates a soft-fail that requires user input
  bool get isSoftFail {
    return this == ParseStatus.needsType ||
           this == ParseStatus.needsCategory ||
           this == ParseStatus.needsAmountConfirmation ||
           this == ParseStatus.missingAmount ||
           this == ParseStatus.ambiguousAmount;
  }

  /// Maps this ParseStatus to the corresponding PendingStage
  /// Returns null for statuses that don't require pending state
  PendingStage? toPendingStage() {
    switch (this) {
      case ParseStatus.needsType:
        return PendingStage.typeSelection;
      case ParseStatus.needsCategory:
        return PendingStage.categorySelection;
      case ParseStatus.needsAmountConfirmation:
      case ParseStatus.ambiguousAmount:
        return PendingStage.amountConfirmation;
      case ParseStatus.missingAmount:
        return PendingStage.missingAmount;
      default:
        return null;
    }
  }

  /// Returns true if this status requires amount confirmation
  bool get requiresAmountConfirmation {
    return this == ParseStatus.needsAmountConfirmation ||
           this == ParseStatus.ambiguousAmount;
  }
}
```

Add import for PendingStage: `import 'pending_transaction_state.dart';`

This extension provides helper methods for identifying soft-fail statuses and mapping them to appropriate pending stages, supporting the state management workflow in ChatScreen.

### test/models/pending_transaction_state_test.dart(NEW)

References: 

- test/models/parse_result_test.dart
- test/models/transaction_model_test.dart
- test/helpers/test_helpers.dart

Create comprehensive unit tests for PendingTransactionState class following the established testing patterns from the codebase.

Implement the following test structure:

**Imports and Setup:**
- Import `flutter_test`, the new model classes, and `test_helpers.dart`
- Use `setUp()` method to initialize common test data using `TestHelpers.createTestTransaction()` and `TestHelpers.createNeedsCategoryParseResult()` etc.

**Test Groups:**

1. **Factory Constructor Validation Group:**
   - Test `forTypeSelection()` succeeds with `ParseStatus.needsType` and throws `ArgumentError` with other statuses
   - Test `forCategorySelection()` succeeds with `ParseStatus.needsCategory` and throws `ArgumentError` with other statuses
   - Test `forAmountConfirmation()` succeeds with `ParseStatus.needsAmountConfirmation` and `ParseStatus.ambiguousAmount`, throws `ArgumentError` with other statuses
   - Test `forMissingAmount()` succeeds with `ParseStatus.missingAmount` and throws `ArgumentError` with other statuses
   - Test that all factory constructors properly set `parseResult`, `originalText`, and `stage` fields

2. **Getter Behavior Group:**
   - Test `isTypeSelection`, `isCategorySelection`, `isAmountConfirmation`, `isMissingAmount` return correct boolean values for each stage
   - Test `transaction` getter returns `parseResult.transaction`
   - Test `candidateAmounts` and `candidateTexts` getters return `parseResult.candidateAmounts` and `parseResult.candidateTexts`
   - Test `ambiguityType` getter returns `parseResult.ambiguityType`

3. **Equality and HashCode Group:**
   - Test that two instances with identical fields are equal and have same hashCode
   - Test that instances with different `parseResult`, `originalText`, or `stage` are not equal
   - Test hashCode consistency across multiple calls
   - Test equality with various combinations of field differences

4. **CopyWith Method Group:**
   - Test `copyWith()` with single field changes preserves other fields
   - Test `copyWith()` with multiple field changes
   - Test `copyWith()` with no parameters returns equal instance
   - Test `copyWith()` maintains validation constraints (if validation is added to copyWith)

5. **Immutability Group:**
   - Verify that all fields are final and cannot be modified after construction
   - Test that `copyWith()` returns a new instance rather than modifying the original

6. **ToString Method Group:**
   - Test that `toString()` includes all relevant fields
   - Test `toString()` with different stages and data combinations

7. **Edge Cases Group:**
   - Test with empty `originalText`
   - Test with null optional fields in `ParseResult` (candidateAmounts, candidateTexts, ambiguityType)
   - Test with various transaction types and currencies
   - Test with long `originalText` and complex `ParseResult` data

8. **Integration with ParseResult Group:**
   - Test that all data from `ParseResult` is accessible through convenience getters
   - Test with `ParseResult` instances created by `TestHelpers`
   - Test with different `ParseResult` factory constructors (success, needsCategory, etc.)

Use the established testing patterns:
- Follow the same `group()` and `test()` organization as existing model tests
- Use `TestHelpers` methods for creating test data
- Use `expect()` assertions with appropriate matchers
- Test error cases with `expectThrows<ArgumentError>()`
- Include descriptive test names and clear assertions

Create helper methods similar to `ParseResultMatchers` if needed for complex state validation.

### test/models/parse_status_extension_test.dart(NEW)

References: 

- test/models/parse_result_test.dart
- lib/models/parse_result.dart(MODIFY)

Create unit tests for the ParseStatus extension methods following established testing patterns.

Implement the following test structure:

**Imports:**
- Import `flutter_test` and the model classes with the new extension

**Test Groups:**

1. **isSoftFail Getter Group:**
   - Test that `isSoftFail` returns `true` for: `needsType`, `needsCategory`, `needsAmountConfirmation`, `missingAmount`, `ambiguousAmount`
   - Test that `isSoftFail` returns `false` for: `success`, `failed`
   - Create a comprehensive truth table test covering all ParseStatus enum values

2. **toPendingStage Method Group:**
   - Test that `needsType.toPendingStage()` returns `PendingStage.typeSelection`
   - Test that `needsCategory.toPendingStage()` returns `PendingStage.categorySelection`
   - Test that `needsAmountConfirmation.toPendingStage()` returns `PendingStage.amountConfirmation`
   - Test that `ambiguousAmount.toPendingStage()` returns `PendingStage.amountConfirmation`
   - Test that `missingAmount.toPendingStage()` returns `PendingStage.missingAmount`
   - Test that `success.toPendingStage()` returns `null`
   - Test that `failed.toPendingStage()` returns `null`

3. **requiresAmountConfirmation Getter Group:**
   - Test that `requiresAmountConfirmation` returns `true` for: `needsAmountConfirmation`, `ambiguousAmount`
   - Test that `requiresAmountConfirmation` returns `false` for all other ParseStatus values

4. **Consistency with ParseResult Group:**
   - Test that `isSoftFail` matches the existing `requiresUserInput` getter behavior in ParseResult
   - Test that the extension methods work correctly with ParseResult instances created by TestHelpers
   - Verify that the mapping logic aligns with the factory constructor validation in PendingTransactionState

5. **Enum Completeness Group:**
   - Test that all ParseStatus enum values are handled by the extension methods
   - Verify that adding new ParseStatus values would be caught by the tests (defensive testing)

Use the same testing patterns as other enum and extension tests in the codebase:
- Use descriptive test names
- Group related tests together
- Test all enum values explicitly
- Use `expect()` assertions with clear expected values
- Include edge case testing where applicable