I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current state management uses four nullable fields that are always set and cleared together, indicating they should be consolidated into a single object. The fields are mutually exclusive - only one pending state is active at a time, which aligns perfectly with the `PendingTransactionState` design. The routing logic in `_onQuickReplySelected` uses a series of if-statements to check which field is non-null, which can be simplified to a switch statement on the pending stage. All field access patterns follow consistent getter/setter patterns that can be abstracted into convenience methods. The existing test suite is well-designed and loosely coupled to internal state structure, requiring minimal updates.

### Approach

I'll refactor ChatScreen's state management by replacing four nullable pending fields with a single `PendingTransactionState?` object. This consolidation leverages the existing `PendingTransactionState` model class that was created in the previous phase, which provides validation, type safety, and convenience getters. The approach maintains complete backward compatibility by preserving all existing UI workflows and business logic while simplifying the internal state representation. The refactor follows a systematic pattern: replace field declarations, update setter logic in handler methods, convert routing logic from if-chains to switch statements, and update field access throughout response handlers.

### Reasoning

I analyzed the current ChatScreen implementation and found it uses four nullable fields (`_pendingTypeSelection`, `_pendingCategorySelection`, `_pendingAmountConfirmation`, `_pendingOriginalText`) to track incomplete transactions requiring user input. I examined how these fields are set in handler methods like `_handleTypeSelection`, read in routing logic like `_onQuickReplySelected`, and cleared in response handlers. I studied the QuickReplyWidget integration and confirmed it only depends on callback functions, not internal state structure. I reviewed existing tests and found they focus on UI behavior rather than direct field access, making them resilient to internal refactoring. I verified that the `PendingTransactionState` class provides all necessary validation and data access patterns needed for the migration.

## Mermaid Diagram

sequenceDiagram
    participant User as User Input
    participant CS as ChatScreen
    participant PS as PendingTransactionState
    participant QR as QuickReplyWidget
    participant TPS as TransactionParsingService
    participant Provider as TransactionProvider

    Note over User, Provider: Consolidated State Management Flow

    User->>CS: Send message "buy coffee"
    CS->>TPS: parseTransaction("buy coffee")
    TPS-->>CS: ParseResult(status: needsType)
    
    CS->>PS: PendingTransactionState.forTypeSelection(text, parseResult)
    PS->>PS: Validate status == needsType
    PS-->>CS: Return validated pending state
    CS->>CS: _pendingState = pendingState
    
    CS->>Provider: Add system message with quick replies
    Provider->>QR: Render quick reply buttons ["Expense", "Income", "Cancel"]
    QR-->>User: Display quick reply options
    
    User->>QR: Tap "Expense"
    QR->>CS: _onQuickReplySelected("Expense")
    CS->>PS: Check _pendingState?.stage
    PS-->>CS: Return PendingStage.typeSelection
    
    CS->>CS: switch(stage) → _handleTypeSelectionResponse("Expense")
    CS->>CS: Access _pendingParse!.transaction
    CS->>TPS: Learn type association
    CS->>CS: Create updated transaction with type
    
    alt Category needed
        CS->>PS: PendingTransactionState.forCategorySelection(text, newParseResult)
        CS->>Provider: Add category selection message
        Provider->>QR: Render category quick replies
    else Transaction complete
        CS->>CS: _clearPending()
        CS->>Provider: Save completed transaction
    end
    
    Note over User, Provider: State properly consolidated and managed through single object

## Proposed File Changes

### lib/screens/chat_screen.dart(MODIFY)

References: 

- lib/models/pending_transaction_state.dart
- lib/services/parser/parse_logger.dart

Replace the four nullable pending state fields with a single consolidated state object and update all related state management logic.

**Step 1: Replace Field Declarations (Lines 32-37)**
Replace the existing four nullable fields:
```
ParseResult? _pendingTypeSelection;
ParseResult? _pendingCategorySelection;
String? _pendingOriginalText;
ParseResult? _pendingAmountConfirmation;
```
With a single field:
```
PendingTransactionState? _pendingState;
```

**Step 2: Add Convenience Helper Methods (After line 41)**
Add helper methods to simplify field access:
```
void _clearPending() => _pendingState = null;
String? get _pendingText => _pendingState?.originalText;
ParseResult? get _pendingParse => _pendingState?.parseResult;
```

**Step 3: Update Handler Entry Points**
In `_handleTypeSelection` (lines 307-308), replace field assignments with:
`_pendingState = PendingTransactionState.forTypeSelection(originalText, parseResult);`

In `_handleCategorySelectionWithQuickReplies` (lines 481-482), replace with:
`_pendingState = PendingTransactionState.forCategorySelection(originalText, parseResult);`

In `_handleAmountConfirmation` (lines 528-529), replace with:
`_pendingState = PendingTransactionState.forAmountConfirmation(originalText, parseResult);`

**Step 4: Update Routing Logic in _onQuickReplySelected (Lines 636-645)**
Replace the if-chain with a switch statement:
```
switch (_pendingState?.stage) {
  case PendingStage.typeSelection:
    await _handleTypeSelectionResponse(selectedOption);
    break;
  case PendingStage.categorySelection:
    await _handleCategorySelectionResponse(selectedOption);
    break;
  case PendingStage.amountConfirmation:
    await _handleAmountConfirmationResponse(selectedOption);
    break;
  case PendingStage.missingAmount:
    // Future implementation for missing amount handling
    break;
  default:
    ParseLogger.w('ui', 'No pending state for quick reply');
}
```

**Step 5: Update Response Handlers**
In `_handleTypeSelectionResponse` (lines 652, 677, 681, 688, 691-692):
- Replace null checks with `if (_pendingState == null) return;`
- Replace `_pendingTypeSelection!.transaction` with `_pendingParse!.transaction`
- Replace `_pendingOriginalText!` with `_pendingText!`
- Replace field clearing with `_clearPending()`

In `_handleCategorySelectionResponse` (lines 697, 713-715, 723, 737-738):
- Replace `_pendingCategorySelection` access with `_pendingParse`
- Replace `_pendingOriginalText` access with `_pendingText`
- Replace field clearing with `_clearPending()`

In `_handleAmountConfirmationResponse` (lines 780, 795-796, 820, 826, 830, 841-842):
- Replace `_pendingAmountConfirmation` access with `_pendingParse`
- Replace `_pendingOriginalText` access with `_pendingText`
- Replace field clearing with `_clearPending()`

In `_processCategorySelection` (lines 746, 753, 760, 765):
- Update all field access to use convenience getters

**Step 6: Update Debug Logging (Lines 633-634)**
Replace debug print statements with structured logging:
- Replace `print('DEBUG: _pendingTypeSelection: ${_pendingTypeSelection != null}');` with `ParseLogger.d('ui', 'Pending state: ${_pendingState?.stage}');`
- Update other debug prints to use ParseLogger with appropriate correlation IDs

**Step 7: Add Import Statement**
Add import for the PendingTransactionState model:
`import '../models/pending_transaction_state.dart';`

This refactor maintains all existing functionality while consolidating state management into a single, type-safe object with built-in validation.

### test/widgets/chat_screen_test.dart(MODIFY)

References: 

- lib/screens/chat_screen.dart(MODIFY)
- lib/models/pending_transaction_state.dart
- test/helpers/test_helpers.dart

Update the ChatScreen widget tests to work with the new consolidated pending state management while maintaining comprehensive test coverage.

**Step 1: Verify Existing Test Structure**
Ensure all existing soft-fail flow tests continue to work:
- Tests for `needsType` parse results and type selection quick replies
- Tests for `needsCategory` parse results and category selection quick replies  
- Tests for `needsAmountConfirmation` parse results and amount confirmation quick replies
- Tests for transaction completion and learning confirmation flows
- Tests for error states and edge cases

**Step 2: Add State Transition Verification Tests**
Add new test cases to verify the consolidated state management:
- Test that pending state is properly created when soft-fail parse results are processed
- Test that only one pending state is active at a time (mutually exclusive)
- Test that pending state is properly cleared after successful completion
- Test that pending state is properly cleared when user cancels
- Test state transitions between different pending stages (e.g., type → category)

**Step 3: Update Test Helpers**
Update any test helper methods that may depend on the old state structure:
- Ensure `createTestWidget` helper continues to work with Provider setup
- Update any mock configurations that simulate state changes
- Verify that test assertions still capture the expected UI behavior

**Step 4: Add Integration Test Cases**
Add comprehensive test scenarios for the new state management:
- Test complete soft-fail flow: parse → pending state → user input → completion
- Test cancellation flow: parse → pending state → user cancels → state cleared
- Test error handling: invalid state transitions, malformed data
- Test rapid user interactions and edge cases with the new state structure

**Step 5: Verify Quick Reply Integration**
Ensure all quick reply interaction tests continue to work:
- Test that quick reply buttons are rendered correctly for each pending stage
- Test that quick reply selection triggers the correct response handlers
- Test that quick reply data flows correctly through the new state structure
- Test accessibility features and UI component rendering

The tests should focus on verifying that the UI behavior and user experience remain identical while the internal state management is simplified.

### test/integration/soft_fail_flow_test.dart(MODIFY)

References: 

- lib/services/parser/transaction_parsing_service.dart
- lib/models/pending_transaction_state.dart
- lib/models/parse_result.dart
- test/mocks/mock_storage_service.dart

Update the soft-fail flow integration tests to verify end-to-end functionality with the new consolidated pending state management.

**Step 1: Verify Existing Integration Tests**
Ensure all existing amount confirmation integration tests continue to work:
- Complete amount confirmation flow tests (user-reported scenarios)
- User selection of different amounts from candidates
- Error scenarios during amount confirmation (invalid amounts, storage errors)
- Multiple embedded numbers scenarios
- Learned associations integration

**Step 2: Add Comprehensive State Transition Tests**
Add new integration test scenarios that exercise the complete pending state lifecycle:
- **Type Selection Flow**: Parse result with `needsType` → pending state creation → user selects type → state transitions to category selection → completion
- **Category Selection Flow**: Parse result with `needsCategory` → pending state creation → user selects category → transaction completion → state cleared
- **Amount Confirmation Flow**: Parse result with `needsAmountConfirmation` → pending state creation → user selects amount → completion or further category selection → state cleared
- **Missing Amount Flow**: Parse result with `missingAmount` → pending state creation → user provides amount → completion → state cleared

**Step 3: Add State Validation Tests**
Add tests that verify the internal state management works correctly:
- Test that `PendingTransactionState` is created with correct stage for each `ParseStatus`
- Test that state transitions follow expected patterns (type → category → completion)
- Test that state is properly cleared on successful completion
- Test that state is properly cleared on user cancellation
- Test that no state leaks occur between different pending scenarios

**Step 4: Add Error Handling Integration Tests**
Add comprehensive error scenario tests:
- Test invalid state transitions (e.g., trying to handle category selection when in type selection stage)
- Test corrupted pending state data handling
- Test concurrent state modifications (rapid user interactions)
- Test recovery from parsing service errors during pending state processing

**Step 5: Add Multi-Stage Flow Tests**
Add tests that exercise complete multi-stage soft-fail scenarios:
- Parse with `needsType` → user selects type → automatic transition to `needsCategory` → user selects category → completion
- Parse with `needsAmountConfirmation` → user selects amount → transition to `needsCategory` → user selects category → completion
- Test that learned associations work correctly across multi-stage flows
- Test that state is maintained correctly throughout complex flows

These integration tests should verify that the entire soft-fail system works seamlessly with the new consolidated state management while maintaining all existing functionality.

### test/integration/pending_state_transitions_test.dart(NEW)

References: 

- lib/screens/chat_screen.dart(MODIFY)
- lib/models/pending_transaction_state.dart
- lib/widgets/quick_reply_widget.dart
- test/helpers/test_helpers.dart
- test/mocks/mock_storage_service.dart

Create comprehensive integration tests specifically for pending state transitions and the new consolidated state management system.

**Test Structure:**
Create a new integration test file focused on validating the `PendingTransactionState` integration with ChatScreen and the complete soft-fail workflow.

**Test Groups:**

**1. State Creation and Validation Group**
Test that pending states are created correctly for each soft-fail scenario:
- Test `needsType` creates `PendingStage.typeSelection` with correct ParseResult and originalText
- Test `needsCategory` creates `PendingStage.categorySelection` with correct data
- Test `needsAmountConfirmation` creates `PendingStage.amountConfirmation` with candidate data
- Test `ambiguousAmount` creates `PendingStage.amountConfirmation` with ambiguity data
- Test `missingAmount` creates `PendingStage.missingAmount` with appropriate context
- Test that invalid ParseStatus/PendingStage combinations throw appropriate errors

**2. State Transition Flow Group**
Test complete state transition workflows:
- **Simple Type Flow**: needsType → user selects type → state cleared or transitions to category
- **Simple Category Flow**: needsCategory → user selects category → state cleared
- **Simple Amount Flow**: needsAmountConfirmation → user selects amount → state cleared or transitions to category
- **Complex Multi-Stage Flow**: needsType → typeSelection → needsCategory → categorySelection → completion
- **Amount-First Flow**: needsAmountConfirmation → amountConfirmation → needsCategory → categorySelection → completion

**3. State Cleanup and Isolation Group**
Test that state management is properly isolated and cleaned up:
- Test that pending state is cleared after successful transaction completion
- Test that pending state is cleared when user cancels at any stage
- Test that no state leaks occur between different transactions
- Test that rapid successive transactions don't interfere with each other
- Test that state is properly reset when parsing errors occur

**4. Quick Reply Integration Group**
Test the integration between pending state and quick reply functionality:
- Test that quick reply options are generated correctly for each pending stage
- Test that quick reply selection routes to correct handler based on pending stage
- Test that quick reply data (candidateAmounts, candidateTexts) flows correctly through state
- Test that quick reply cancellation properly clears pending state
- Test that invalid quick reply selections are handled gracefully

**5. Error Handling and Edge Cases Group**
Test error scenarios and edge cases:
- Test handling of corrupted pending state data
- Test concurrent state modifications (multiple quick reply selections)
- Test state recovery from parsing service failures
- Test handling of unexpected ParseStatus values
- Test memory cleanup and no memory leaks in state management

**6. Backward Compatibility Group**
Test that all existing functionality continues to work:
- Test that all existing soft-fail scenarios produce identical user experience
- Test that learning integration continues to work with new state management
- Test that transaction completion flows remain unchanged
- Test that error messages and user feedback remain consistent

**Test Setup:**
- Use real ChatScreen widget with mocked dependencies (TransactionParsingService, StorageService, etc.)
- Set up Provider context with TransactionProvider and other required providers
- Use MockEntityExtractor and other established mocks for deterministic behavior
- Include performance measurements to ensure no regression in state management efficiency

**Assertions:**
- Verify pending state creation, transitions, and cleanup using widget state inspection
- Verify UI behavior matches expected patterns for each state
- Verify that Provider state changes correctly reflect pending state transitions
- Verify that no exceptions or errors occur during state transitions
- Verify that memory usage remains stable across multiple state transitions

These tests will provide comprehensive coverage of the new consolidated state management system and ensure that the refactor maintains all existing functionality while improving internal architecture.

### test/debug/ui_amount_confirmation_test.dart(MODIFY)

References: 

- lib/screens/chat_screen.dart(MODIFY)
- lib/models/pending_transaction_state.dart
- lib/widgets/quick_reply_widget.dart

Update the UI amount confirmation debug tests to work with the new consolidated pending state management.

**Step 1: Update Test Setup**
Ensure test setup works with the new state structure:
- Verify that `createTestWidget` helper continues to work with Provider setup
- Update `createMockAmountConfirmationResult` helper if needed to work with new state management
- Ensure mock configurations properly simulate the new pending state creation

**Step 2: Update State Verification Tests**
Update tests that verify pending state behavior:
- Replace any direct field access verification with pending state verification
- Update tests that check for state cleanup to use the new consolidated state
- Ensure tests that verify callback routing still work with the new switch-based routing

**Step 3: Verify QuickReplyWidget Integration**
Ensure all QuickReplyWidget integration tests continue to work:
- Test QuickReplyWidget rendering for amount confirmation scenarios
- Test correct display and callback routing for candidate amounts and cancel options
- Test accessibility features and UI component rendering with new state management

**Step 4: Update Edge Case Tests**
Ensure edge case handling tests work with new state structure:
- Test handling of null/empty candidateTexts with new state management
- Test single/multiple candidate scenarios with consolidated state
- Test UI crash prevention with malformed pending state data

**Step 5: Add New Debug Tests**
Add new debug tests specific to the consolidated state management:
- Test pending state creation and validation for amount confirmation scenarios
- Test state transition debugging (logging, state inspection)
- Test performance of new state management under various load conditions
- Test memory usage and cleanup of pending state objects

The updated tests should maintain all existing debug functionality while providing additional insights into the new consolidated state management system.