I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current parsing services follow consistent patterns with async methods returning `Future<ParseResult>`, but they're implemented as monolithic classes. The existing test infrastructure uses custom mocks and helper utilities rather than third-party libraries. The ParseResult model has well-defined factory constructors with validation, and the Transaction model provides all necessary fields for parsing scenarios. All services depend on common dependencies like StorageService and CategoryFinderService, suggesting these should be accessible through the parsing context.

### Approach

I'll create the foundation for a strategy pattern by implementing an abstract `ParsingStrategy` class and a `ParsingContext` data transfer object. This approach follows the chain of responsibility pattern where strategies can decline to handle input by returning null. The design prioritizes extensibility through a flexible context object with an extras map, while maintaining complete isolation from existing code to ensure zero behavioral impact. The implementation includes comprehensive unit tests that validate the interface contract without depending on production parsing logic.

### Reasoning

I analyzed the current parsing architecture by examining the MlKitParserService and FallbackParserService implementations to understand their method signatures, dependencies, and data flow patterns. I studied the existing test infrastructure to understand mocking patterns, test utilities, and object creation approaches. I examined the ParseResult and Transaction models to understand their structure, factory methods, and validation requirements. I identified all the services and data that parsing strategies will need access to, including StorageService, CategoryFinderService, LocalizationService, and various intermediate parsing data.

## Mermaid Diagram

sequenceDiagram
    participant Test as Unit Tests
    participant Strategy as ParsingStrategy
    participant Context as ParsingContext
    participant Result as ParseResult

    Note over Test, Result: Strategy Pattern Foundation Setup

    Test->>Strategy: Create DummyStrategy instance
    Test->>Context: Create ParsingContext with text
    Test->>Context: Add data to extras map
    
    Test->>Strategy: Call execute(context)
    Strategy->>Context: Access text and extras
    Strategy->>Result: Create ParseResult
    Strategy-->>Test: Return ParseResult or null
    
    Test->>Context: Verify extras modifications
    Test->>Context: Set interimResult
    Test->>Context: Verify interimResult is stored
    
    Note over Test, Result: Chain of Responsibility Pattern
    Test->>Strategy: Test null return (decline to handle)
    Strategy-->>Test: Return null (no exception)

## Proposed File Changes

### lib/services/parser/parsing_strategy.dart(NEW)

References: 

- lib/models/parse_result.dart

Create an abstract base class `ParsingStrategy` that defines the interface for all parsing strategies in the strategy pattern.

The class should include:
- An abstract method `Future<ParseResult?> execute(ParsingContext context)` that strategies must implement
- An abstract getter `String get name` for debugging and logging purposes
- Proper documentation explaining the null return convention (returning null means "I decline to handle this input")

Import statements needed:
- `dart:async` for Future support
- `../../models/parse_result.dart` for the ParseResult class
- `parsing_context.dart` for the ParsingContext class

The interface should support the chain of responsibility pattern where multiple strategies can be tried in sequence, with each strategy having the option to decline handling by returning null. This design allows for flexible strategy composition without forcing every strategy to handle every input type.

Ensure the class is completely abstract with no concrete implementation, as concrete strategies will be implemented in subsequent phases.

### lib/services/parser/parsing_context.dart(NEW)

References: 

- lib/models/parse_result.dart

Create a `ParsingContext` class that serves as a data transfer object for passing information between parsing strategies.

The class should include:
- A required `String text` field containing the user input to be parsed
- An optional `Locale? locale` field for localization support (used by `FallbackParserService`)
- A `Map<String, dynamic> extras` field for extensible data passing without API changes
- A mutable `ParseResult? interimResult` field for storing partial parsing outcomes
- A constructor with named parameters, defaulting extras to an empty map if not provided

Import statements needed:
- `dart:ui` for the Locale class
- `../../models/parse_result.dart` for the ParseResult class

The context should be mutable to allow strategies to modify it during processing. The extras map provides a flexible extension point for future strategy needs without requiring API changes. This design accommodates the various dependencies and intermediate data identified in the current parsing services, including access to StorageService, CategoryFinderService, LearnedAssociationService, and other parsing utilities.

The class should be lightweight and focused solely on data transfer, with no business logic or validation beyond basic constructor setup.

### test/services/parser/parsing_strategy_interface_test.dart(NEW)

References: 

- test/services/parser/mlkit_parser_service_test.dart
- test/services/parser/fallback_parser_service_test.dart
- test/helpers/test_helpers.dart
- lib/models/parse_result.dart
- lib/models/transaction_model.dart

Create comprehensive unit tests for the `ParsingStrategy` interface and `ParsingContext` class following the existing test patterns from `mlkit_parser_service_test.dart` and `fallback_parser_service_test.dart`.

Implement the following test groups:

1. **Interface Instantiation Safety**: Verify that the abstract `ParsingStrategy` class cannot be instantiated directly (this is enforced at compile time)

2. **Dummy Implementation Behavior**: Create a concrete `DummyStrategy extends ParsingStrategy` for testing purposes with configurable behavior. Test that:
   - The `execute()` method receives the same context reference that was passed in
   - The returned `ParseResult` bubbles back correctly from the strategy
   - The `name` getter returns the expected value
   - The strategy can return different types of `ParseResult` objects

3. **ParsingContext Mutability**: Verify that the context object can be modified:
   - Adding entries to the `extras` map and confirming they can be read back
   - Setting the `interimResult` field and confirming the change is observable
   - Modifying the context and verifying changes persist

4. **Null Result Convention**: Test that a strategy returning `null` is accepted without throwing exceptions, supporting the chain of responsibility pattern

Use the existing test infrastructure:
- Import `TestHelpers` from `../../helpers/test_helpers.dart` for creating test `Transaction` and `ParseResult` objects
- Follow the test structure patterns from existing parser tests
- Use `flutter_test` framework (no third-party mocking libraries)
- Create test data using the established factory methods

The tests should validate only the interface contract and context behavior without depending on any production parsing logic, ensuring they remain isolated and focused on the strategy pattern foundation.