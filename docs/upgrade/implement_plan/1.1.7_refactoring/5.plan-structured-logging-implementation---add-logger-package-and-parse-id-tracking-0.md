I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

The current parsing services use unstructured logging with `print()` and `debugPrint()` statements scattered across multiple files. I found extensive logging in TransactionParsingService (16 statements), MlKitStrategy (35 statements), and several other parsing services. The existing ParsingContext class flows through the strategy pattern but lacks a correlation ID field. The codebase already uses the `uuid` package for transaction IDs and has comprehensive test infrastructure with custom mocks. No logger package is currently present, and there are no conflicts with adding one.

### Approach

I'll implement structured logging by adding the `logger` package and creating a `ParseLogger` utility that generates unique correlation IDs for each parse operation. The approach extends the existing `ParsingContext` with an optional `parseId` field to propagate IDs through the strategy chain. I'll replace all `print()` and `debugPrint()` statements with structured logging calls while maintaining the same message content and debug information. The implementation follows the existing dependency injection patterns and maintains complete backward compatibility.

### Reasoning

I analyzed the current logging landscape by searching for all `print()` and `debugPrint()` statements across the parsing services directory. I examined the `ParsingContext` class structure and strategy pattern flow to understand how correlation IDs should propagate. I reviewed the existing test infrastructure to understand mocking patterns and test organization. I checked `pubspec.yaml` for current dependencies and confirmed that the `logger` package can be added without conflicts. I studied the `uuid` package usage to understand existing ID generation patterns.

## Mermaid Diagram

sequenceDiagram
    participant Client as Client Code
    participant TPS as TransactionParsingService
    participant PL as ParseLogger
    participant Context as ParsingContext
    participant LAS as LearnedAssociationStrategy
    participant MKS as MlKitStrategy
    participant FRS as FallbackRegexStrategy
    participant Logger as Logger Output

    Note over Client, Logger: Structured Logging with Correlation IDs

    Client->>TPS: parseTransaction("buy coffee 5$")
    TPS->>PL: start("buy coffee 5$")
    PL->>PL: Generate unique ID (e.g., "a1b2c3d4")
    PL->>Logger: [parse:a1b2c3d4] START: "buy coffee 5$"
    PL-->>TPS: Return correlation ID "a1b2c3d4"
    
    TPS->>Context: Create ParsingContext(text, locale, parseId: "a1b2c3d4")
    
    TPS->>PL: i("a1b2c3d4", "Trying strategy: LearnedAssociationStrategy")
    PL->>Logger: [parse:a1b2c3d4] Trying strategy: LearnedAssociationStrategy
    TPS->>LAS: execute(context)
    LAS->>LAS: Extract parseId = "a1b2c3d4" from context
    LAS->>PL: d("a1b2c3d4", "Checking learned associations...")
    PL->>Logger: [parse:a1b2c3d4] Checking learned associations...
    LAS-->>TPS: Return null (no association found)
    TPS->>PL: i("a1b2c3d4", "Strategy LearnedAssociationStrategy declined")
    
    TPS->>PL: i("a1b2c3d4", "Trying strategy: MlKitStrategy")
    TPS->>MKS: execute(context)
    MKS->>MKS: Extract parseId = "a1b2c3d4" from context
    MKS->>PL: d("a1b2c3d4", "ML Kit found 2 entities...")
    MKS->>PL: d("a1b2c3d4", "Consolidated candidates: [5.0]")
    PL->>Logger: [parse:a1b2c3d4] ML Kit found 2 entities...
    PL->>Logger: [parse:a1b2c3d4] Consolidated candidates: [5.0]
    MKS-->>TPS: Return ParseResult.success()
    TPS->>PL: i("a1b2c3d4", "Strategy MlKitStrategy handled the input")
    
    Note over Client, Logger: All log messages correlated with same ID "a1b2c3d4"

## Proposed File Changes

### pubspec.yaml(MODIFY)

Add the `logger` package to the dependencies section after line 26.

Add the following line:
```
logger: ^2.6.1
```

This package provides structured logging capabilities with configurable output formats and is fully compatible with the current Flutter SDK constraints (`>=3.0.0 <4.0.0`). The logger package has no conflicts with existing dependencies and adds minimal overhead to the application.

### lib/services/parser/parse_logger.dart(NEW)

Create a utility class for structured logging with unique correlation ID generation.

Implement the following structure:
- Import `package:logger/logger.dart` and `package:uuid/uuid.dart`
- Create a static `Logger _logger` instance configured with `PrettyPrinter(methodCount: 0, errorMethodCount: 3, lineLength: 120, colors: true, printEmojis: false)`
- Create a static `Uuid _uuid` instance for ID generation
- Implement `static String start(String text)` method that generates an 8-character correlation ID using `_uuid.v4().substring(0, 8)` and logs the parse start with `_logger.i('[parse:$id] START: "$text"')`
- Implement logging methods:
  - `static void d(String id, String message)` for debug logs
  - `static void i(String id, String message)` for info logs  
  - `static void w(String id, String message, [Object? error])` for warnings
  - `static void e(String id, String message, [Object? error, StackTrace? stackTrace])` for errors
- All methods prefix messages with `[parse:$id]` for easy filtering and correlation
- Implement `static void setLogOutput(LogOutput output)` for test injection
- Add comprehensive documentation explaining the correlation ID concept and usage patterns

The utility provides a clean interface for structured logging while maintaining the same debug information currently provided by print statements.

### lib/services/parser/parsing_context.dart(MODIFY)

Extend the ParsingContext class to support parse correlation IDs.

Add a new optional field `final String? parseId` to the class after the existing fields.

Update the constructor to accept an optional `parseId` parameter:
```
ParsingContext({
  required this.text,
  this.locale,
  this.parseId,
  Map<String, dynamic>? extras,
}) : extras = extras ?? {};
```

Update the class documentation to explain that `parseId` is used for correlating log messages across the parsing pipeline and can be null for backward compatibility.

This change maintains complete backward compatibility since the new field is optional and defaults to null.

### lib/services/parser/transaction_parsing_service.dart(MODIFY)

References: 

- lib/services/parser/parse_logger.dart(NEW)
- lib/services/parser/parsing_context.dart(MODIFY)

Replace print/debugPrint statements with structured logging and add parse ID generation.

Add import for the new ParseLogger: `import 'parse_logger.dart';`

In the `parseTransaction` method around line 197:
- Replace the existing print statement with parse ID generation: `final parseId = ParseLogger.start(text);`
- Update the ParsingContext creation to include the parseId: `final context = ParsingContext(text: text, locale: null, parseId: parseId);`
- Replace the strategy logging print statements (lines 214, 217, 220) with:
  - `ParseLogger.i(parseId, 'Trying strategy: ${strategy.name}');`
  - `ParseLogger.i(parseId, 'Strategy ${strategy.name} handled the input');`
  - `ParseLogger.i(parseId, 'Strategy ${strategy.name} declined to handle');`

Replace all debugPrint statements with appropriate ParseLogger calls:
- Initialization messages (lines 105, 112, 119, 144, 151, 158) → `ParseLogger.i(parseId ?? 'init', message)`
- Error messages (lines 122, 123, 161, 162) → `ParseLogger.w(parseId ?? 'init', message)`
- Debug messages in completeTransaction (lines 262, 266, 270, 274) → `ParseLogger.d(parseId ?? 'complete', message)`

Maintain the same message content but route through the structured logger for consistent formatting and correlation.

### lib/services/parser/strategies/mlkit_strategy.dart(MODIFY)

References: 

- lib/services/parser/parse_logger.dart(NEW)
- lib/services/parser/parsing_context.dart(MODIFY)

Replace extensive print/debugPrint statements with structured logging using correlation IDs.

Add import for ParseLogger: `import '../parse_logger.dart';`

In the `execute` method, extract the parseId from context: `final parseId = context.parseId ?? 'unknown';`

Replace all print statements with ParseLogger calls:
- Error print (line 58) → `ParseLogger.e(parseId, 'Error in MlKitStrategy: $e', e);`
- MLKIT_DEBUG prints (lines 68, 75, 77, 81, 113, 117, 122, 126) → `ParseLogger.d(parseId, message)` (remove MLKIT_DEBUG prefix since parseId provides correlation)
- Ambiguity detection prints (lines 297, 298, 349, 350) → `ParseLogger.d(parseId, message)`

Replace all debugPrint statements with `ParseLogger.d(parseId, message)` calls:
- Entity processing debugs (lines 91, 97, 99, 105, 109)
- Candidate consolidation debugs (lines 254, 255, 256, 262, 272, 279, 286)
- Ambiguity detection debugs (lines 302, 311, 317, 321, 335, 341, 345, 396)
- Missing amount debugs (lines 131, 132)

Maintain the same detailed debug information but route through structured logging for better organization and filtering. The parseId correlation allows tracking the complete ML Kit parsing flow for each request.

### lib/services/parser/fallback_parser_service.dart(MODIFY)

References: 

- lib/services/parser/parse_logger.dart(NEW)

Replace the single print statement with structured logging.

Add import for ParseLogger: `import 'parse_logger.dart';`

Replace the print statement on line 35 in the `_initializeLearnedAssociationService` method:
- Change `print('Failed to initialize learned association service in FallbackParserService: $e');` to `ParseLogger.w('init', 'Failed to initialize learned association service in FallbackParserService: $e', e);`

Since this is an initialization error that occurs outside the main parsing flow, use 'init' as the correlation ID. This maintains the same error reporting while providing structured logging format.

### lib/services/parser/strategies/learned_association_strategy.dart(MODIFY)

References: 

- lib/services/parser/parse_logger.dart(NEW)

Replace error print statement with structured logging.

Add import for ParseLogger: `import '../parse_logger.dart';`

In the `execute` method, extract parseId from context: `final parseId = context.parseId ?? 'unknown';`

Replace the error print statement (line 58) with: `ParseLogger.e(parseId, 'Error in LearnedAssociationStrategy: $e', e);`

This provides consistent error reporting with correlation ID tracking for the learned association strategy.

### lib/services/parser/strategies/fallback_regex_strategy.dart(MODIFY)

References: 

- lib/services/parser/parse_logger.dart(NEW)

Replace error print statement with structured logging.

Add import for ParseLogger: `import '../parse_logger.dart';`

In the `execute` method, extract parseId from context: `final parseId = context.parseId ?? 'unknown';`

Replace the error print statement (line 40) with: `ParseLogger.e(parseId, 'Error in FallbackRegexStrategy: $e', e);`

This ensures consistent error reporting across all strategies with proper correlation ID tracking.

### lib/services/parser/learned_association_service.dart(MODIFY)

References: 

- lib/services/parser/parse_logger.dart(NEW)

Replace print statements with structured logging for error handling and migration reporting.

Add import for ParseLogger: `import 'parse_logger.dart';`

Replace all print statements with appropriate ParseLogger calls:
- Storage corruption errors (lines 120, 165, 213) → `ParseLogger.w('storage', message, e)`
- Learning errors (line 146) → `ParseLogger.e('learn', message, e)`
- Retrieval errors (lines 194, 224) → `ParseLogger.e('retrieve', message, e)`
- Migration info (line 260) → `ParseLogger.i('migration', message)`
- Migration errors (line 263) → `ParseLogger.e('migration', message, e)`

Use descriptive correlation IDs since these operations occur outside the main parsing flow. This provides structured logging for service-level operations while maintaining the same error reporting functionality.

### lib/services/parser/category_finder_service.dart(MODIFY)

References: 

- lib/services/parser/parse_logger.dart(NEW)

Replace print statement with structured logging.

Add import for ParseLogger: `import 'parse_logger.dart';`

Replace the print statement on line 24 with: `ParseLogger.w('init', 'Failed to initialize learned association service in CategoryFinderService: $e', e);`

This provides consistent initialization error reporting with structured logging format.

### lib/services/parser/real_entity_extractor.dart(MODIFY)

References: 

- lib/services/parser/parse_logger.dart(NEW)

Replace print statement with structured logging.

Add import for ParseLogger: `import 'parse_logger.dart';`

Replace the print statement on line 144 with: `ParseLogger.e('mlkit', 'Error during ML Kit entity annotation: $e', e);`

This provides consistent error reporting for ML Kit entity extraction failures with structured logging format.

### lib/services/parser/learned_category_storage.dart(MODIFY)

References: 

- lib/services/parser/parse_logger.dart(NEW)

Replace print statements with structured logging.

Add import for ParseLogger: `import 'parse_logger.dart';`

Replace the print statements:
- Line 213: `ParseLogger.e('migration', 'Error getting learned categories for migration: $e', e);`
- Line 230: `ParseLogger.e('migration', 'Error clearing legacy data after migration: $e', e);`

This provides consistent migration error reporting with structured logging format.

### test/services/parser/parse_logger_test.dart(NEW)

References: 

- test/helpers/test_helpers.dart
- test/services/parser/learned_association_service_test.dart

Create comprehensive unit tests for the ParseLogger utility class following established testing patterns.

Implement test groups covering:

**ID Generation Tests**: Test that `start()` method returns non-null 8-character correlation IDs and that consecutive calls produce unique IDs. Verify the ID format matches expected pattern.

**Logging Method Tests**: Test that `d()`, `i()`, `w()`, and `e()` methods write to the logger with correct message formatting. Verify that all messages are prefixed with `[parse:<id>]` format.

**LogOutput Injection Tests**: Test that `setLogOutput()` allows injection of custom LogOutput for testing. Use `MemoryLogOutput` to capture log records and verify message content, levels, and formatting.

**Error Handling Tests**: Test that `w()` and `e()` methods properly handle optional error objects and stack traces. Verify that error information is included in log output.

**Message Formatting Tests**: Test that correlation IDs are properly formatted in log messages and that message content is preserved correctly.

Use the established testing patterns:
- Import `flutter_test` and the `ParseLogger` class
- Use `group()` and `test()` blocks for organization
- Follow the same assertion patterns as other utility tests
- Test with various ID and message combinations
- Verify log output capture and filtering capabilities

The tests should focus on the logging utility functionality without testing business logic.

### test/services/parser/parse_id_propagation_test.dart(NEW)

References: 

- test/services/parser/mlkit_parser_service_test.dart
- test/helpers/test_helpers.dart
- test/mocks/mock_storage_service.dart

Create integration tests to verify parse ID propagation through the parsing pipeline.

Implement test scenarios covering:

**Parse ID Generation and Propagation**: Set up a fully mocked `TransactionParsingService` with `MemoryLogOutput` injected into `ParseLogger`. Parse sample text and capture all log records. Verify that the first log record is a 'START' message with a generated correlation ID and that subsequent strategy logs use the same ID.

**Strategy Chain Correlation**: Test that all strategies (LearnedAssociationStrategy, MlKitStrategy, FallbackRegexStrategy) receive the same parseId through the ParsingContext and use it in their log messages. Search captured log output for messages containing the same correlation ID across different strategies.

**Context Flow Verification**: Test that the parseId flows correctly through the ParsingContext from TransactionParsingService to each strategy. Verify that strategies can access `context.parseId` and use it for logging.

**Error Correlation**: Test that error scenarios maintain correlation ID consistency. Trigger various error conditions and verify that error logs contain the correct correlation ID.

**Backward Compatibility**: Test that existing code continues to work when parseId is null or not provided. Verify that strategies handle missing parseId gracefully.

Use established testing patterns:
- Import `flutter_test`, parsing services, and all required mocks
- Set up mocks using `MockStorageService`, `MockEntityExtractor`, `MockCategoryFinderService`, etc.
- Follow `setUp()` and `tearDown()` patterns with singleton reset
- Use `TestHelpers` for creating test data
- Focus on logging side-effects and correlation ID propagation, not business logic
- Assert on captured log records for correlation ID consistency

The tests should verify the end-to-end correlation ID flow without testing the actual parsing business logic.