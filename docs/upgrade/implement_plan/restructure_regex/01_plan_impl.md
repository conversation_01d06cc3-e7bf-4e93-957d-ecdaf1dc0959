I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've thoroughly analyzed the codebase and understand the current implementation. The FallbackParserService currently has hardcoded English regex patterns in the `_detectTransactionType` method (lines 119-147) that need to be replaced with a localization-based system. The project already has the `intl` package and a comprehensive test suite. The existing CurrencyUtils class provides robust currency detection that we can leverage. The test structure shows extensive coverage with sample transaction data that we can use to validate our refactoring.

### Approach

We'll implement a resource file architecture that separates language-specific keywords from business logic. The approach involves creating a LocalizationService that loads JSON files containing keywords and patterns, then refactoring FallbackParserService to use these patterns dynamically. We'll maintain backward compatibility by ensuring the public API remains unchanged while adding comprehensive tests to validate both English parsing accuracy and Spanish localization capability.

### Reasoning

I examined the PRD document to understand requirements, analyzed the current FallbackParserService implementation to identify hardcoded patterns, reviewed the comprehensive test suite to understand expected behavior, checked the project structure and dependencies, and studied the CurrencyUtils class to understand existing currency detection logic. This gave me a complete picture of what needs to be changed and how to maintain compatibility.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant FPS as FallbackParserService
    participant LS as LocalizationService
    participant Cache as Pattern Cache
    participant Assets as JSON Assets
    participant CU as CurrencyUtils

    User->>FPS: parseTransaction(text)
    FPS->>LS: getPatternsForLocale(currentLocale)
    
    alt Cache Hit
        LS->>Cache: Check cached patterns
        Cache-->>LS: Return cached LocalizationData
    else Cache Miss
        LS->>Assets: Load locale JSON file
        Assets-->>LS: Return JSON data
        LS->>LS: Parse JSON to LocalizationData
        LS->>Cache: Store in cache
    end
    
    LS-->>FPS: Return LocalizationData
    FPS->>FPS: Build regex patterns from keywords
    FPS->>FPS: Parse transaction type from text
    FPS->>CU: Extract currency information
    CU-->>FPS: Return currency details
    FPS->>FPS: Create Transaction object
    FPS-->>User: Return ParseResult

    Note over FPS,Assets: Error Handling: If locale fails,<br/>fallback to English patterns

## Proposed File Changes

### pubspec.yaml(MODIFY)

Add the `assets/l10n/` directory to the flutter assets section to include localization JSON files in the app bundle. This enables the LocalizationService to load language-specific patterns at runtime.

### assets(NEW)

Create the assets directory to house localization files.

### assets/l10n(NEW)

Create the l10n directory inside assets to contain language-specific JSON files.

### assets/l10n/en.json(NEW)

References: 

- lib/services/parser/fallback_parser_service.dart(MODIFY)

Create the English localization file containing all the keywords currently hardcoded in `lib/services/parser/fallback_parser_service.dart`. Include expense_keywords (spent, paid, bought, purchased, etc.), income_keywords (received, earned, income, salary, etc.), loan_keywords (borrowed, lent, loan, debt), decimal_separator (.), thousands_separator (,), and locale (en-US). Extract these keywords from the existing regex patterns in the _detectTransactionType method.

### assets/l10n/es.json(NEW)

Create the Spanish localization file as specified in the PRD to demonstrate internationalization capability. Include Spanish translations: expense_keywords (gasté, pagado, comprado, costo, cena, almuerzo), income_keywords (recibido, ganado, salario, vendido, regalo), loan_keywords (prestado, préstamo, deuda), decimal_separator (,), thousands_separator (.), and locale (es-ES).

### lib/models/localization_data.dart(NEW)

Create a data model class to represent the structure of localization JSON files. Include fields for locale, decimal_separator, thousands_separator, expense_keywords, income_keywords, and loan_keywords. Implement fromJson factory constructor for JSON deserialization and validation. Add proper error handling for malformed JSON data.

### lib/services/localization_service.dart(NEW)

References: 

- lib/models/localization_data.dart(NEW)

Create the LocalizationService that manages loading and caching of localization files. Implement singleton pattern for app-wide access. Include methods: getPatternsForLocale(Locale locale) that returns LocalizationData, preloadLocale for performance optimization, and clearCache for memory management. Use rootBundle.loadString to load JSON files from assets. Implement graceful fallback to English when requested locale is unavailable. Add caching mechanism using Map<String, LocalizationData> to avoid repeated file loading. Include proper error handling and logging for debugging.

### lib/services/parser/fallback_parser_service.dart(MODIFY)

References: 

- lib/services/localization_service.dart(NEW)
- lib/utils/currency_utils.dart
- lib/services/parser/category_finder_service.dart

Refactor the FallbackParserService to use LocalizationService instead of hardcoded patterns. Add LocalizationService as a dependency via constructor injection. Remove all hardcoded regex patterns from _detectTransactionType method (lines 119-147). Make parseTransaction method async to accommodate pattern loading. Implement dynamic regex building using keywords from LocalizationData. Update _extractAmount method to use configurable decimal and thousands separators from localization data. Maintain all existing functionality including currency detection via `lib/utils/currency_utils.dart`, category finding via `lib/services/parser/category_finder_service.dart`, and learned associations. Preserve the exact same public API signature to ensure backward compatibility. Add proper error handling with fallback to English patterns when localization fails.

### test/models/localization_data_test.dart(NEW)

References: 

- lib/models/localization_data.dart(NEW)

Create comprehensive unit tests for LocalizationData model. Test JSON deserialization with valid data, invalid data, missing fields, and malformed JSON. Verify that all required fields are properly parsed and validated. Test error handling for edge cases like empty arrays and null values.

### test/services/localization_service_test.dart(NEW)

References: 

- lib/services/localization_service.dart(NEW)

Create comprehensive unit tests for LocalizationService. Test successful loading of English and Spanish locales, caching behavior to ensure patterns are loaded only once, fallback to English when requested locale is unavailable, error handling for missing files and malformed JSON, and memory management with cache clearing. Use TestWidgetsFlutterBinding.ensureInitialized() for proper asset loading in tests. Mock asset bundle for controlled testing scenarios.

### test/services/parser/fallback_parser_service_test.dart(MODIFY)

References: 

- lib/services/parser/fallback_parser_service.dart(MODIFY)
- lib/services/localization_service.dart(NEW)
- test/test_data/sample_transactions.dart

Update existing tests to work with the refactored FallbackParserService. Inject LocalizationService mock in setUp method to control localization data during testing. Add new test groups for localization functionality: 'Localization Integration' to test English parsing maintains same accuracy as before, 'Spanish Localization' to test Spanish keyword recognition using mock Spanish data, 'Localization Fallback' to test graceful fallback when locale data is unavailable, and 'Performance with Localization' to ensure parsing speed doesn't degrade significantly. Preserve all existing test cases from `test/test_data/sample_transactions.dart` to ensure no regression in parsing accuracy. Use the comprehensive test structure already established in the file.

### test/mocks/mock_localization_service.dart(NEW)

References: 

- lib/services/localization_service.dart(NEW)
- lib/models/localization_data.dart(NEW)

Create a mock LocalizationService for testing purposes. Implement the same interface as LocalizationService but return predefined LocalizationData for different locales. Include methods to simulate loading delays, errors, and different localization scenarios. This enables controlled testing without depending on actual asset files.

### test/integration/localization_integration_test.dart(NEW)

References: 

- lib/services/parser/fallback_parser_service.dart(MODIFY)
- lib/services/localization_service.dart(NEW)

Create integration tests that validate the complete localization pipeline. Test end-to-end parsing with actual JSON files, verify that changing device locale affects parsing behavior, test performance with multiple locale switches, and validate that all existing functionality (currency detection, category finding, learned associations) works correctly with localized patterns. Use real asset files and actual LocalizationService to test the complete integration.

### test/test_data/spanish_sample_transactions.dart(NEW)

References: 

- test/test_data/sample_transactions.dart

Create Spanish sample transaction data following the same structure as `test/test_data/sample_transactions.dart`. Include Spanish transaction texts with expected parsing results to validate Spanish localization. Cover expense, income, and loan transactions with Spanish keywords like 'gasté €25 en café', 'recibido €1000 salario', 'prestado €200 a amigo'. This data will be used to test Spanish parsing capability.