I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've analyzed the bug where user input 'com trua tai Lux68 2m' saves as expense 68 instead of expense 2,000,000. The root cause is that ML Kit detects "68" from "Lux68" as a money entity and stops there, never falling back to regex parsing that would correctly identify "2m" as 2 million. The AmountUtils class already supports "2m" → 2,000,000 conversion, but ML Kit's false positive prevents the fallback mechanism from working. The issue occurs because M<PERSON> Kit finds ANY money entity and considers parsing successful, even when it's wrong.

### Approach

**Multi-layered Fix Strategy**:

1. **Enhanced ML Kit Money Entity Validation**: Improve `parseMoneyEntity()` to handle abbreviations and validate that detected amounts aren't embedded in vendor names
2. **Smart Amount Selection Logic**: When ML Kit finds money entities, also run AmountUtils extraction and choose the larger/more reasonable amount
3. **Vendor Name Pattern Detection**: Add logic to ignore numeric values that appear to be part of vendor/location names
4. **Comprehensive Testing**: Add tests that specifically cover the "embedded number vs abbreviated amount" scenario

This approach maintains backward compatibility while fixing the core issue of ML Kit's false positives blocking better regex-based parsing.

### Reasoning

I explored the codebase structure to understand the parsing pipeline, examined the ML Kit parser service to see how entity extraction works, analyzed the AmountUtils class to confirm it supports "2m" parsing, reviewed existing tests to understand coverage gaps, and identified that the issue occurs when ML Kit finds a false positive money entity and never falls back to regex parsing that would correctly handle abbreviations.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant MLKitParser
    participant MLKit
    participant AmountUtils
    participant SelectionLogic
    participant Transaction

    Note over User,Transaction: Current Bug Flow
    User->>MLKitParser: "com trua tai Lux68 2m"
    MLKitParser->>MLKit: annotateText()
    MLKit-->>MLKitParser: [Money("68" from "Lux68")]
    MLKitParser->>MLKitParser: parseMoneyEntity("68")
    MLKitParser-->>User: Transaction(amount: 68)

    Note over User,Transaction: Fixed Flow
    User->>MLKitParser: "com trua tai Lux68 2m"
    MLKitParser->>MLKit: annotateText()
    MLKit-->>MLKitParser: [Money("68" from "Lux68")]
    MLKitParser->>AmountUtils: extractAmountFromText("com trua tai Lux68 2m")
    AmountUtils-->>MLKitParser: {amount: 2000000, currency: null}
    MLKitParser->>SelectionLogic: selectBestAmount([68], 2000000)
    SelectionLogic->>SelectionLogic: isEmbeddedInVendorName("68", "Lux68")
    SelectionLogic->>SelectionLogic: hasAbbreviation("2m") = true
    SelectionLogic->>SelectionLogic: isLargerAmount(2000000 > 68*20) = true
    SelectionLogic-->>MLKitParser: 2000000
    MLKitParser-->>User: Transaction(amount: 2000000)

## Proposed File Changes

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/utils/amount_utils.dart

**Enhance parseMoneyEntity() to handle abbreviations and validate context**:

Update the `_parseMoneyEntity()` method around lines 236-276 to:
- Extend the numeric regex pattern to include abbreviations: `r'(\d+(?:,\d{3})*(?:\.\d+)?[kKmMbB]?|\d+\.\d+[kKmMbB]?)'`
- Use `AmountUtils.parseAbbreviatedNumber()` when abbreviation suffixes are detected
- Add validation to skip amounts that appear to be embedded in vendor names using pattern `r'[A-Za-z]+\d+[A-Za-z]*'`
- Only accept embedded numbers if they have clear currency context (symbols before/after)

**Implement smart amount selection in _parseWithMLKit()**:

Around lines 122-228, enhance the ML Kit parsing logic to:
- Collect all money entities from ML Kit into a list of candidates
- Run `AmountUtils.extractAmountFromText()` on the full text as an additional candidate
- Implement `_selectBestAmount()` helper that chooses between candidates using these rules:
  - Prefer amounts with abbreviations (k/m/b) over plain numbers
  - Prefer larger amounts when one is >20x the other (likely vendor name vs real amount)
  - Prefer amounts not embedded in alphabetic sequences
  - Fall back to first ML Kit result if no clear winner

**Add vendor name detection helper**:

Create `_isEmbeddedInVendorName()` method that:
- Checks if the detected amount appears within an alphabetic sequence like "Lux68"
- Returns true if the number has letters immediately before AND after
- Returns false if there are currency symbols or clear separators

This ensures that "68" in "Lux68" is deprioritized while "$68" or "68 USD" would still be valid.

### test/services/parser/mlkit_parser_service_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- test/mocks/mock_entity_extractor.dart(MODIFY)

**Add test group for embedded number vs abbreviation scenarios**:

Add a new test group 'Embedded Numbers vs Abbreviations' with test cases that reproduce the exact bug:
- Test case: 'com trua tai Lux68 2m' should parse as 2,000,000 not 68
- Test case: 'dinner at Cafe123 500k' should parse as 500,000 not 123
- Test case: 'shopping Mall456 1.5M' should parse as 1,500,000 not 456

**Add test cases for vendor name detection**:

- Test that 'Hotel789' embedded numbers are ignored when larger amounts exist
- Test that '$789' or '789 USD' are still valid even if embedded
- Test that multiple embedded numbers choose the largest reasonable amount

**Add mock entity extractor tests**:

Create tests using `MockEntityExtractor` that return specific ML Kit results:
- Mock returning 'Lux68' as Money entity with value 68
- Verify that the parser still correctly identifies '2m' as the real amount
- Test the selection logic between multiple money entity candidates

**Add regression tests**:

Ensure existing functionality still works:
- Simple amounts like '$100 food' continue to work
- Currency detection remains accurate
- Learned associations are not affected

### test/mocks/mock_entity_extractor.dart(MODIFY)

References: 

- lib/services/parser/entity_extractor_base.dart

**Add support for creating specific ML Kit entity scenarios**:

Enhance the `MockEntityExtractor` class to support creating test scenarios where ML Kit returns specific money entities:

- Add `addMoneyEntity(String text, int start, int end, String entityText)` method to simulate ML Kit detecting specific text ranges as money entities
- Add `simulateLux68Scenario()` helper that sets up the exact bug scenario: returns "68" as a money entity from position of "Lux68"
- Add `simulateMultipleMoneyEntities()` to test selection logic between multiple candidates

**Add entity positioning helpers**:

- Add methods to calculate start/end positions for entity text within full input
- Ensure mock entities have correct positioning for text removal logic
- Support overlapping entities to test edge cases

This allows tests to reproduce the exact ML Kit behavior that causes the bug, ensuring our fixes work correctly.

### test/integration/parsing_pipeline_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/models/transaction_model.dart

**Add end-to-end test for the Lux68 2m bug**:

Add a new test group 'Vendor Name vs Amount Bug' with integration tests:
- Test the complete flow from user input 'com trua tai Lux68 2m' to final transaction
- Verify that the amount is parsed as 2,000,000 VND (assuming Vietnamese locale)
- Test that the description preserves the original text
- Verify that transaction type is correctly detected as expense

**Add tests for similar vendor name patterns**:

- Test various patterns: 'Restaurant123 500k', 'Hotel456 1.5M', 'Shop789 2m'
- Test with different currencies and locales
- Verify that legitimate embedded amounts (like '$123 fee') still work

**Add performance regression tests**:

- Ensure the enhanced parsing logic doesn't significantly impact performance
- Test with longer texts containing multiple potential amounts
- Verify that fallback mechanisms work when ML Kit is unavailable

These tests ensure the fix works in real-world scenarios and doesn't break existing functionality.

### docs/upgrade/implement_plan/bugs/20250727_2_mlkit_vendor_name_bug.md(NEW)

References: 

- docs/upgrade/implement_plan/bugs/20250727_1_currency_fallback_bug.md

**Create documentation for the ML Kit vendor name parsing bug**:

Document the issue where ML Kit incorrectly identifies numbers embedded in vendor names (like "68" in "Lux68") as money entities, preventing correct parsing of abbreviated amounts (like "2m" for 2 million).

**Include sections for**:

- **Problem Description**: Detailed explanation of the bug with examples
- **Root Cause Analysis**: How ML Kit entity extraction works and why it fails
- **Solution Overview**: Multi-layered approach with smart amount selection
- **Technical Changes**: Summary of code modifications made
- **Testing Strategy**: How the fix is validated
- **Migration Notes**: Any considerations for existing users
- **Performance Impact**: Analysis of the enhanced parsing logic

**Add examples**:

- Before: 'com trua tai Lux68 2m' → 68 VND
- After: 'com trua tai Lux68 2m' → 2,000,000 VND
- Edge cases handled and preserved functionality

This documentation helps future developers understand the issue and the solution approach.

### test/services/parser/vendor_name_parsing_test.dart(NEW)

References: 

- test/mocks/mock_entity_extractor.dart(MODIFY)
- lib/services/parser/mlkit_parser_service.dart(MODIFY)

**Create dedicated test file for vendor name parsing scenarios**:

Create comprehensive tests specifically for the vendor name vs amount disambiguation:

**Test Categories**:

- **Vendor Name Patterns**: Test various formats like 'Restaurant123', 'Hotel456', 'Shop789'
- **Abbreviation Patterns**: Test 'k', 'm', 'b' suffixes with vendor names
- **Currency Context**: Test when embedded numbers have currency symbols
- **Multiple Candidates**: Test selection logic when multiple amounts are present
- **Edge Cases**: Test boundary conditions and error scenarios

**Specific Test Cases**:

- 'Lux68 2m' → 2,000,000 (the original bug)
- 'Cafe123 500k' → 500,000
- 'Hotel456 $789' → 789 (currency context makes embedded number valid)
- 'Shop999 1.5M investment' → 1,500,000
- 'Restaurant 100k' → 100,000 (no embedded number)

**Mock ML Kit Scenarios**:

- Use `MockEntityExtractor` to simulate specific ML Kit responses
- Test the selection algorithm with controlled inputs
- Verify fallback behavior when ML Kit fails

This focused test file ensures comprehensive coverage of the vendor name parsing logic.