I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've analyzed the vendor name parsing bug where <PERSON><PERSON> Kit incorrectly identifies numbers embedded in vendor names (like "70" in "lux70") as money entities, preventing correct parsing of abbreviated amounts (like "100k"). The codebase already has significant infrastructure to handle this issue, but there are gaps in the vendor name detection logic and amount selection algorithm that need to be addressed.

### Approach

**Multi-layered Fix Strategy**:

1. **Enhanced Vendor Name Detection**: Fix the `_isEmbeddedInVendorName` method to use entity start/end positions instead of unreliable `indexOf` searches
2. **Improved Amount Selection Logic**: Ensure `AmountUtils` always runs alongside ML Kit and enhance the selection algorithm to prioritize abbreviations
3. **Robust Fallback Mechanism**: Guarantee that abbreviated amounts are detected even when ML Kit finds false positive money entities
4. **Comprehensive Testing**: Add tests for the exact failing scenarios and edge cases

This approach maintains backward compatibility while fixing the core issue of ML Kit's false positives blocking better regex-based parsing.

### Reasoning

I explored the codebase structure to understand the parsing pipeline, examined the ML Kit parser service to see how entity extraction works, analyzed the AmountUtils class to confirm it supports abbreviation parsing, reviewed existing tests to understand coverage gaps, and identified that the issue occurs when ML Kit finds false positive money entities and the current vendor name detection logic fails to filter them out properly.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant MLKitParser
    participant MLKit
    participant VendorDetection
    participant AmountUtils
    participant SelectionLogic
    participant Transaction

    Note over User,Transaction: Current Bug Flow
    User->>MLKitParser: "com trua tai lux70 100k"
    MLKitParser->>MLKit: annotateText()
    MLKit-->>MLKitParser: [Money("70" from "lux70")]
    MLKitParser->>VendorDetection: _isEmbeddedInVendorName("70", fullText)
    VendorDetection-->>MLKitParser: false (indexOf fails)
    MLKitParser-->>User: Transaction(amount: 70)

    Note over User,Transaction: Fixed Flow
    User->>MLKitParser: "com trua tai lux70 100k"
    MLKitParser->>MLKit: annotateText()
    MLKit-->>MLKitParser: [Money("70" at pos 15-17)]
    MLKitParser->>VendorDetection: _isEmbeddedInVendorName(fullText, 15, 17)
    VendorDetection->>VendorDetection: Check chars at pos 14 & 17
    VendorDetection-->>MLKitParser: true (letters before & after)
    MLKitParser->>AmountUtils: extractAmountFromText(fullText)
    AmountUtils-->>MLKitParser: {amount: 100000, currency: null}
    MLKitParser->>SelectionLogic: selectBestAmount([70], 100000)
    SelectionLogic->>SelectionLogic: hasAbbreviation("100k") = true
    SelectionLogic->>SelectionLogic: isEmbedded(70) = true
    SelectionLogic-->>MLKitParser: 100000 (prefer abbreviation)
    MLKitParser-->>User: Transaction(amount: 100000)

## Proposed File Changes

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/utils/amount_utils.dart

**Fix vendor name detection using entity positions**:

Update the `_isEmbeddedInVendorName` method signature to accept `start` and `end` parameters instead of relying on `indexOf` searches. The new signature should be `_isEmbeddedInVendorName(String fullText, int start, int end)`. This ensures accurate position-based detection of embedded numbers.

Modify the logic to check characters at specific positions rather than searching for substrings. Check if there are letters immediately before `start` position and after `end` position to determine if the number is embedded in a vendor name.

**Enhance the ML Kit parsing flow**:

In the `_parseWithMLKit` method around lines 134-202, ensure that `_extractNonVendorAmount` is always called regardless of whether ML Kit found money entities. Currently it's only called when ML Kit candidates are empty, but it should run in parallel to provide alternative candidates.

Update the call to `_parseMoneyEntity` around line 144 to pass entity start/end positions to the enhanced vendor name detection.

**Improve amount selection algorithm**:

Enhance the `_selectBestAmount` method around lines 350-395 to:
- Always prefer amounts with abbreviations (k/m/b) over plain numbers, regardless of size difference
- Improve the 20x threshold logic to handle edge cases like "mall50 1k" where the difference is exactly 20x
- Add better currency propagation when selecting AmountUtils results over ML Kit results
- Add logging to help debug selection decisions

**Fix currency handling in selection**:

In the amount selection logic around lines 209-231, ensure that when AmountUtils result is selected, its currency is properly preserved and not overwritten by ML Kit currency results.

### test/services/parser/vendor_name_parsing_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- test/mocks/mock_entity_extractor.dart(MODIFY)

**Add tests for the exact failing scenarios**:

Add a new test group 'Exact User Reported Issues' with specific test cases that reproduce the user's reported problems:
- Test case: 'com trua tai lux70 100k' should parse as 100,000 not 70
- Test case: 'com trua tai lux70 100' should parse as 100 not 70
- Test case: 'com trua tai Lux70 100k' (uppercase) should parse as 100,000 not 70
- Test case: 'dinner at lux70 100k vnd' should parse as 100,000 VND not 70

**Add edge case tests for vendor name detection**:

Add tests for boundary conditions:
- Numbers at the start of vendor names: '70Lux 100k'
- Numbers at the end of vendor names: 'Lux70 100k'
- Multiple embedded numbers: 'Shop123 Mall456 100k'
- Similar-sized amounts: 'Hotel80 100 fee' (close to 20x threshold)

**Add tests for improved selection logic**:

Add tests that verify the enhanced amount selection:
- Abbreviation preference: 'Store999 1k' should choose 1k over 999
- Currency preservation: 'Cafe50 100k vnd' should preserve VND currency
- Multiple abbreviations: 'Budget 500k spent 2m' behavior verification

Use `MockEntityExtractorFactory.createVendorNameScenario` to simulate ML Kit returning the embedded numbers as money entities, ensuring tests match real-world ML Kit behavior.

### test/mocks/mock_entity_extractor.dart(MODIFY)

References: 

- lib/services/parser/entity_extractor_base.dart

**Enhance mock factory methods for position-based testing**:

Update the `createVendorNameScenario` method to be more precise about entity positioning. Instead of using `calculateEntityPosition` which relies on `indexOf`, allow explicit start/end position specification to match real ML Kit behavior more accurately.

Add a new factory method `createExactPositionScenario` that takes explicit start/end positions for money entities, allowing tests to simulate exact ML Kit output without relying on string searching.

**Add helper methods for the reported scenarios**:

Add specific factory methods:
- `createLux70Scenario()` that simulates ML Kit finding "70" at the exact position in "com trua tai lux70 100k"
- `createMultipleEmbeddedScenario()` for testing multiple vendor names with embedded numbers
- `createAbbreviationMissedScenario()` for cases where ML Kit misses abbreviations but finds embedded numbers

**Improve entity positioning accuracy**:

Enhance the position calculation logic to handle edge cases like:
- Unicode characters that might affect byte positions
- Multiple occurrences of the same number in different contexts
- Whitespace and punctuation around entities

This ensures mock behavior closely matches real ML Kit entity extraction, making tests more reliable for catching real-world issues.

### test/integration/parsing_pipeline_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/models/transaction_model.dart

**Add end-to-end tests for the vendor name bug**:

Add a new test group 'Vendor Name vs Amount Integration' with comprehensive integration tests:
- Test the complete flow from user input 'com trua tai lux70 100k' to final transaction with amount 100,000
- Test currency detection and preservation in vendor name scenarios
- Test transaction type detection (expense/income) with vendor names
- Test description preservation while filtering out embedded numbers

**Add performance regression tests**:

Add tests to ensure the enhanced parsing logic doesn't significantly impact performance:
- Test parsing speed with multiple vendor names and amounts
- Test memory usage with complex vendor name patterns
- Test fallback mechanism performance when ML Kit is unavailable

**Add real-world scenario tests**:

Add tests based on common real-world patterns:
- Restaurant names with numbers: 'Pho24 50k lunch', 'Cafe7Eleven 25k coffee'
- Hotel/accommodation: 'Hotel123 2m booking', 'Motel6 150k night'
- Shopping venues: 'Mall456 500k shopping', 'Store99 1.5m purchase'

These tests ensure the fix works in realistic usage scenarios and doesn't break existing functionality.

### test/services/parser/mlkit_parser_service_test.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- test/mocks/mock_entity_extractor.dart(MODIFY)

**Add unit tests for the enhanced vendor name detection**:

Add a new test group 'Enhanced Vendor Name Detection' with tests for the improved `_isEmbeddedInVendorName` method:
- Test position-based detection vs old indexOf-based detection
- Test edge cases like numbers at string boundaries
- Test Unicode and special character handling
- Test performance with long strings containing multiple numbers

**Add tests for improved amount selection logic**:

Add tests for the enhanced `_selectBestAmount` method:
- Test abbreviation preference over larger plain numbers
- Test currency preservation when selecting AmountUtils results
- Test threshold edge cases (exactly 20x, slightly under/over)
- Test selection with multiple ML Kit candidates vs single AmountUtils result

**Add regression tests for existing functionality**:

Ensure the changes don't break existing behavior:
- Test simple amount parsing without vendor names
- Test currency detection accuracy
- Test transaction type detection
- Test learned association functionality

Use dependency injection with `MockEntityExtractor` to test specific ML Kit scenarios without relying on the actual ML Kit service.

### test/debug_vendor_name_parsing.dart(NEW)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/utils/amount_utils.dart

**Create a debug test file for manual verification**:

Create a standalone debug script that can be run to test the exact scenarios reported by the user:
- Test 'com trua tai lux70 100k' with real ML Kit (if available) and with mocks
- Test 'com trua tai lux70 100' with both setups
- Print detailed debugging information showing:
  - What entities ML Kit detects
  - How the vendor name detection logic evaluates each entity
  - What AmountUtils extracts from the full text
  - How the selection algorithm chooses between candidates
  - The final transaction details

**Include performance benchmarking**:

Add simple performance measurements to ensure the enhanced logic doesn't significantly slow down parsing:
- Time the parsing of various vendor name scenarios
- Compare performance before and after the changes
- Test with longer texts containing multiple vendor names

**Add manual test cases**:

Include a comprehensive set of manual test cases that can be easily run to verify the fix:
- Various vendor name patterns with different number positions
- Different abbreviation formats (k, K, m, M, b, B)
- Mixed currency scenarios
- Edge cases and boundary conditions

This file serves as both a debugging tool during development and a manual verification tool for the fix.