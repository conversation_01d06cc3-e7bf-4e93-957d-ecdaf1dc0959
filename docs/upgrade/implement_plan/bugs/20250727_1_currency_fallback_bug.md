I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've analyzed the codebase and identified the exact issues:

**Currency Bug**: The `_buildTransactionFromAssociation` method in `mlkit_parser_service.dart` hardcodes currency detection and defaults to 'USD' instead of using the stored default currency from `StorageService`. This only affects the learned association fast-path.

**Number Enhancement**: MLKit doesn't support "k/M" abbreviations natively. All three amount extraction methods (`FallbackParserService`, `MlKitParserService`, and legacy `TransactionParserService`) use hardcoded regex patterns that don't handle these abbreviations.

**Code Structure**: The parsing pipeline flows: MLKit parser → fallback parser → legacy parser, with learned associations as a fast-path that bypasses normal currency handling.

### Approach

**Two-Phase Approach**:

1. **Currency Bug Fix**: Modify `_buildTransactionFromAssociation` to use `StorageService.getDefaultCurrency()` instead of hardcoded currency detection, ensuring consistency with other parsing paths.

2. **Number Enhancement**: Create a shared `AmountUtils` class to handle "k/M/B" abbreviations, then update all three amount extraction methods to use this utility. This centralizes the logic and eliminates code duplication.

The solution maintains backward compatibility, doesn't break existing functionality, and follows the established patterns in the codebase.

### Reasoning

I explored the codebase structure to understand the parsing pipeline, examined the currency utilities and storage service, analyzed the MLKit parser services to identify the exact bug location, reviewed the transaction model to understand currency handling, checked existing tests to understand expected behavior, and confirmed that MLKit doesn't support number abbreviations natively through web search.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant ChatScreen
    participant MLKitParser
    participant LearnedAssociation
    participant StorageService
    participant AmountUtils
    participant Transaction

    Note over User,Transaction: Currency Bug Fix Flow
    User->>ChatScreen: "1000 clothes" (first time)
    ChatScreen->>MLKitParser: parseTransaction()
    MLKitParser->>LearnedAssociation: getAssociation() → null
    MLKitParser->>AmountUtils: extractAmountFromText("1000 clothes")
    AmountUtils-->>MLKitParser: {amount: 1000, currency: null}
    MLKitParser->>StorageService: getDefaultCurrency() → "VND"
    MLKitParser-->>ChatScreen: needsCategory(transaction with VND)
    User->>ChatScreen: Confirms "shopping" category
    ChatScreen->>LearnedAssociation: learn("1000 clothes", categoryId: "shopping")
    
    Note over User,Transaction: Second Transaction (Bug Fixed)
    User->>ChatScreen: "2000 clothes"
    ChatScreen->>MLKitParser: parseTransaction()
    MLKitParser->>LearnedAssociation: getAssociation() → found
    MLKitParser->>AmountUtils: extractAmountFromText("2000 clothes")
    AmountUtils-->>MLKitParser: {amount: 2000, currency: null}
    MLKitParser->>StorageService: getDefaultCurrency() → "VND"
    MLKitParser-->>ChatScreen: success(transaction with VND, shopping)

    Note over User,Transaction: Number Abbreviation Enhancement
    User->>ChatScreen: "100k food"
    ChatScreen->>MLKitParser: parseTransaction()
    MLKitParser->>AmountUtils: extractAmountFromText("100k food")
    AmountUtils->>AmountUtils: parseAbbreviatedNumber("100k") → 100000
    AmountUtils-->>MLKitParser: {amount: 100000, currency: null}
    MLKitParser->>StorageService: getDefaultCurrency() → "VND"
    MLKitParser-->>ChatScreen: success(transaction: 100000 VND, food)

## Proposed File Changes

### lib/utils/amount_utils.dart(NEW)

Create a new utility class `AmountUtils` with the following functionality:

- `parseAbbreviatedNumber(String token)` method that handles number abbreviations:
  - Recognizes 'k', 'K' for thousands (multiply by 1,000)
  - Recognizes 'm', 'M' for millions (multiply by 1,000,000)
  - Recognizes 'b', 'B' for billions (multiply by 1,000,000,000)
  - Supports decimal values like '1.5k', '2.3M'
  - Returns null for invalid input
  - Handles edge cases like '0k', negative numbers

- `extractAmountFromText(String text, {String? thousandsSeparator, String? decimalSeparator})` method that:
  - Uses regex to find amount patterns with optional abbreviations
  - Handles currency symbols before/after amounts
  - Normalizes separators based on locale
  - Returns a map with 'amount' (double) and 'currency' (String?) keys
  - Integrates with existing currency detection logic

Include comprehensive error handling and input validation. The utility should be stateless and thread-safe.

### test/utils/amount_utils_test.dart(NEW)

References: 

- test/utils/currency_utils_test.dart

Create comprehensive unit tests for `AmountUtils` covering:

**parseAbbreviatedNumber tests**:
- Basic abbreviations: '100k' → 100000, '2M' → 2000000, '1.5B' → 1500000000
- Case insensitivity: '100K', '2m', '1.5b'
- Decimal handling: '1.25k' → 1250, '2.5M' → 2500000
- Edge cases: '0k' → 0, invalid inputs return null
- Boundary values: very large numbers, precision limits

**extractAmountFromText tests**:
- Integration with abbreviations: '100k VND', '$2.5M', '€1.2k'
- Currency detection with abbreviations
- Locale-specific separators with abbreviations
- Mixed formats: '1,500k', '2.5M dollars'
- Error cases: malformed input, multiple amounts

**Regression tests**:
- Ensure existing amount patterns still work
- Verify currency detection remains intact
- Test with real transaction text examples

Use the same testing patterns as `currency_utils_test.dart` for consistency.

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/utils/amount_utils.dart(NEW)
- lib/services/storage_service.dart

**Fix Currency Bug in Learned Associations**:

In the `_buildTransactionFromAssociation` method (lines ~456-483):
- Remove the hardcoded currency detection logic that defaults to 'USD'
- Replace with: `final defaultCurrency = await _storageService.getDefaultCurrency();`
- Update currency extraction to: `String currencyCode = _extractCurrencyFromText(text) ?? defaultCurrency;`
- This ensures learned associations use the same currency logic as other parsing paths

**Integrate Amount Abbreviation Support**:

Update `_extractPositiveAmount` method (lines ~307-332):
- Import the new `AmountUtils` class
- Replace the existing regex-based amount extraction with `AmountUtils.extractAmountFromText`
- Maintain the existing currency detection logic
- Ensure the method signature and return format remain unchanged for backward compatibility

Update the simple amount extraction in `_buildTransactionFromAssociation`:
- Replace the basic regex with `AmountUtils.extractAmountFromText` to support abbreviations
- Maintain the existing fallback behavior

These changes ensure that both the main parsing path and learned association path support number abbreviations and use consistent currency handling.

### lib/services/parser/fallback_parser_service.dart(MODIFY)

References: 

- lib/utils/amount_utils.dart(NEW)

**Integrate Amount Abbreviation Support**:

Update `_extractPositiveAmount` method (lines ~204-240):
- Import the new `AmountUtils` class
- Replace the existing regex-based amount extraction with `AmountUtils.extractAmountFromText`
- Pass the localization data separators to the utility method
- Maintain the existing currency detection and localization logic
- Ensure the method signature and return format remain unchanged

The integration should:
- Preserve the existing localization support for decimal and thousands separators
- Maintain the currency symbol and code detection logic
- Keep the same error handling and fallback behavior
- Support the new abbreviation formats while maintaining backward compatibility

This ensures that when MLKit is not available, the fallback parser still supports number abbreviations like '100k' and '2.5M'.

### lib/services/transaction_parser_service.dart(MODIFY)

References: 

- lib/utils/amount_utils.dart(NEW)

**Integrate Amount Abbreviation Support**:

Update `_extractPositiveAmount` method (lines ~96-111):
- Import the new `AmountUtils` class
- Replace the existing hardcoded regex pattern with `AmountUtils.extractAmountFromText`
- Update the method to return the amount as double (maintaining existing signature)
- Keep the existing USD-focused currency handling for backward compatibility

Note: This service appears to be legacy code that's being superseded by the MLKit and fallback parsers. The changes here ensure consistency across all parsing methods, but the main fixes are in the more actively used parsers.

The integration maintains the existing simple behavior while adding abbreviation support for any code paths that still use this legacy parser.

### test/services/parser/mlkit_parser_service_test.dart(MODIFY)

References: 

- lib/services/storage_service.dart
- lib/utils/amount_utils.dart(NEW)

**Add Currency Bug Regression Tests**:

Add new test cases to verify the currency bug fix:
- Test that learned associations use the default currency when no explicit currency is provided
- Test scenario: set default currency to 'VND', learn association with '1000 clothes', then parse '2000 clothes' and verify it uses VND not USD
- Test that explicit currency symbols in text still override the default currency

**Add Number Abbreviation Tests**:

Add comprehensive test cases for the new abbreviation support:
- Basic abbreviations: '100k food', '2.5M salary', '1.2B investment'
- Mixed with currency: '$100k shopping', '€2.5M bonus', '₫1.5k transport'
- Case variations: '100K', '2m', '1.5B'
- Integration with learned associations: learn '100k starbucks' then parse '200k starbucks'
- Error cases: invalid abbreviations, malformed input

Ensure all existing tests continue to pass to verify backward compatibility.

### test/services/parser/fallback_parser_service_test.dart(MODIFY)

References: 

- test/services/parser/mlkit_parser_service_test.dart(MODIFY)
- test/services/parser/learning_bug_investigation_test.dart(MODIFY)

Create comprehensive unit tests for `FallbackParserService` focusing on:

**Number Abbreviation Support**:
- Test abbreviation parsing with different localization settings
- Verify '100k', '2.5M', '1.2B' work with various decimal/thousands separators
- Test currency detection with abbreviations
- Verify localization data integration

**Currency Handling**:
- Test that default currency is used when no explicit currency is provided
- Verify currency symbol and code detection works with abbreviations
- Test context-aware currency detection

**Integration Tests**:
- Test complete parsing pipeline with abbreviations
- Verify transaction creation with abbreviated amounts
- Test error handling and fallback behavior

Use the same testing patterns as existing parser tests, following the structure in `mlkit_parser_service_test.dart` and `learning_bug_investigation_test.dart`.

### test/integration/parsing_pipeline_test.dart(MODIFY)

References: 

- lib/services/storage_service.dart
- lib/utils/amount_utils.dart(NEW)

**Add End-to-End Tests for Bug Fixes**:

Add integration tests that verify the complete user workflow:

**Currency Bug Integration Test**:
- Simulate user setting default currency to VND
- Parse '1000 clothes' and confirm category (triggering learning)
- Parse '2000 clothes' and verify it uses VND currency, not USD
- Test the complete flow from chat input to transaction storage

**Number Abbreviation Integration Test**:
- Test parsing '100k food', '2.5M salary' through the complete pipeline
- Verify amounts are correctly expanded (100k → 100000)
- Test with different currency settings
- Verify learned associations work with abbreviations

**Combined Scenario Test**:
- Set default currency to VND
- Parse '100k starbucks' (learn association)
- Parse '200k starbucks' (use learned association with VND currency)
- Verify both abbreviation expansion and currency consistency

These tests ensure the fixes work correctly in the real application flow, not just in isolated unit tests.

### test/services/parser/learning_bug_investigation_test.dart(MODIFY)

References: 

- lib/services/storage_service.dart
- lib/utils/amount_utils.dart(NEW)

**Add Currency Bug Specific Test**:

Add a new test group 'Currency Bug Investigation' with:
- Test case that reproduces the exact bug scenario described by the user
- Set default currency to VND using `StorageService.saveDefaultCurrency('VND')`
- Parse '1000 clothes' and confirm category selection (this should trigger learning)
- Parse '2000 clothes' and verify the transaction uses VND currency, not USD
- Verify this works through the learned association fast-path

**Add Number Abbreviation Tests**:

Add test cases in existing groups to verify:
- Learned associations work with abbreviated numbers
- '100k starbucks' learned, then '200k starbucks' parsed correctly
- Abbreviations work in the complete learning workflow
- Integration with the existing vendor name extraction logic

These tests specifically target the bugs reported by the user and ensure the fixes work in the learning context.