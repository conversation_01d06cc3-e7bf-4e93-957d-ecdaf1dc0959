# Architectural Refactoring for Maintainability (v1.1.7)

## 1. Overview & Problem Statement

The current transaction parsing system is powerful, intelligent, and robust. However, its effectiveness comes with a high degree of architectural complexity. The primary parsing service has become a large orchestrator responsible for managing multiple strategies (learning, ML Kit, raw number finding, fallbacks), ambiguity resolution, and state management.

This document outlines a plan for a series of targeted architectural refactorings. The goal is not to change the user-facing functionality but to significantly improve the long-term maintainability, testability, and extensibility of the codebase, reducing the cognitive load on developers and making the system easier to debug and enhance.

## 2. Goals

-   **Improve Developer Experience**: Reduce the complexity of core services to make the system easier to understand and modify.
-   **Enhance Testability**: Isolate business logic into smaller, independently testable units.
-   **Increase Extensibility**: Make it simpler to add new features (e.g., a new parsing strategy) without refactoring existing code.
-   **Streamline Debugging**: Provide better tools and patterns for tracing the flow of a single transaction parse from start to finish.

---

## 3. Proposed Architectural Refinements

### 3.1. Refinement 1: Consolidate Parsing Logic with a Strategy Pattern

**Current Situation:**
The `MlKitParserService` is a large orchestrator that holds the logic for the entire pipeline: checking learned associations, calling ML Kit, calling the `RawNumberFinder`, consolidating results, detecting ambiguity, and finally calling the `FallbackParserService`. This makes the service difficult to modify without understanding every single step.

**Recommendation:**
Refactor the parsing pipeline into a list of "strategies" that are executed in a predefined order.

1.  **Create an Abstract `ParsingStrategy` Class:**
    ```dart
    abstract class ParsingStrategy {
      Future<ParseResult?> execute(String text, ParsingContext context);
    }
    ```

2.  **Implement Each Step as a Strategy:**
    -   `LearnedAssociationStrategy implements ParsingStrategy`
    -   `MlKitStrategy implements ParsingStrategy` (This would internally use ML Kit + `RawNumberFinder`)
    -   `FallbackRegexStrategy implements ParsingStrategy`

3.  **Simplify the Main Parser Service:**
    The `MlKitParserService` (which could be renamed to `TransactionParsingService`) would no longer contain the implementation details. Its job would be to simply manage and execute the strategies.

    ```dart
    // Inside the main parser service
    class TransactionParsingService {
      final List<ParsingStrategy> _strategies = [
        LearnedAssociationStrategy(),
        MlKitStrategy(),
        FallbackRegexStrategy(),
      ];

      Future<ParseResult> parseTransaction(String text) {
        final context = ParsingContext(originalText: text);
        for (final strategy in _strategies) {
          final result = await strategy.execute(text, context);
          if (result != null && result.isSuccess) {
            // Or handle soft-fails and update context
            return result;
          }
        }
        return ParseResult.failed();
      }
    }
    ```

**Benefit:**
This change makes the system significantly easier to maintain and extend. To understand the ML Kit logic, a developer only needs to look at `MlKitStrategy.dart`. To add a new parsing method (e.g., a server-side LLM), one would simply create a new strategy class and add it to the list without touching existing code. Testing becomes much simpler, as each strategy can be unit-tested in complete isolation.

### 3.2. Refinement 2: Centralize Configuration

**Current Situation:**
The parsing logic contains several hardcoded "magic numbers" and thresholds, such as the 20x difference used to select the best amount candidate.

**Recommendation:**
Externalize these values into a dedicated, injectable configuration object.

```dart
class ParsingConfig {
  final double amountAmbiguityThreshold; // e.g., 20.0
  // Add other configurable values here
  
  const ParsingConfig({this.amountAmbiguityThreshold = 20.0});
}
```
This `ParsingConfig` object would be created once and passed to the services that need it, likely via a Provider.

**Benefit:**
This allows for easy tuning of the parser's behavior by modifying a single configuration file. It makes the configurable parts of the system explicit and would allow for A/B testing of different parsing heuristics in the future.

### 3.3. Refinement 3: Implement a Unified, Structured Logging System

**Current Situation:**
Debugging is likely done with `print()` statements scattered throughout the services, making it hard to trace a single transaction's journey through the entire pipeline.

**Recommendation:**
Use a proper logging package (like `logger`) and create a unified logging format that tracks a single parse request using a unique ID.

1.  Generate a unique ID for each `parseTransaction` request.
2.  Pass this ID through all internal service and strategy calls.
3.  Log the input and output of each stage with this ID.

**Example Log Output:**
```
[INFO] [ParseID: abc-123] Starting parse for: "Dinner at Lux68 50"
[DEBUG] [ParseID: abc-123] LearnedAssociationStrategy: No match found.
[DEBUG] [ParseID: abc-123] MlKitStrategy: Found entities: [Money(50)]
[DEBUG] [ParseID: abc-123] RawNumberFinder: Found numbers: [68, 50]
[DEBUG] [ParseID: abc-123] Consolidation: Candidates are [68, 50].
[INFO] [ParseID: abc-123] Ambiguity detected. Returning needsAmountConfirmation.
```

**Benefit:**
This is a massive improvement for maintainability. It makes debugging complex parsing failures trivial, as a developer can simply filter the logs by a `ParseID` to see the exact flow and pinpoint where the logic went wrong.

### 3.4. Refinement 4: Encapsulate the "Soft Fail" State

**Current Situation:**
The `ChatScreen` manages the state of a "soft fail" with several nullable fields (`_pendingTypeSelection`, `_pendingAmountConfirmation`, `_pendingOriginalText`, etc.). This can become complex to manage and prone to error.

**Recommendation:**
Create a dedicated class to represent the state of an in-progress, ambiguous transaction.

```dart
class PendingTransactionState {
  final String originalText;
  final Transaction partialTransaction;
  final ParseStatus currentStatus; // e.g., needsType, needsAmountConfirmation

  PendingTransactionState({
    required this.originalText,
    required this.partialTransaction,
    required this.currentStatus,
  });
}
```
The `ChatScreen` would then only need to manage one state object: `PendingTransactionState? _pendingState;`.

**Benefit:**
This simplifies the `ChatScreen` by consolidating multiple state flags into a single, well-defined object. It reduces the risk of inconsistent UI states and makes the soft-fail flow more extensible. Adding a new step to the flow is as simple as adding a new `ParseStatus` and handling it, without adding more nullable fields to the `ChatScreen`.

---

## 4. Conclusion

These four refinements focus on creating clearer boundaries, making the system's behavior more explicit, and improving the developer's ability to debug and extend the code. By investing in this architectural cleanup, we ensure that the powerful parsing system remains a maintainable and scalable asset for the project's future.