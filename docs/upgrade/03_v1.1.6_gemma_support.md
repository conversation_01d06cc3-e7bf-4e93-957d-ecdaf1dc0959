# Money Lover Chat - AI-Assisted Disambiguation PRD

**Version:** 2.1 (AI-Assisted Disambiguation)  
**Date:** July 28, 2025  
**Author:** Gemini  
**Status:** Proposed  

---

## 1. Introduction & Vision

Our current transaction parser is highly effective, combining on-device ML Kit with a robust fallback system and user-specific learning. However, it still encounters situations where it must "soft fail" and ask the user for clarification (e.g., transaction type or category).

This document outlines the plan to integrate a server-side LLM (Gemma 1B) as an **"intelligent consultant."** This AI will be invoked *only* when our existing system is uncertain, with the goal of resolving ambiguity automatically and further reducing the need for user intervention. This enhances the user experience by making the app feel smarter and more seamless, without sacrificing the speed and privacy of the on-device-first approach.

## 2. Goals & Success Metrics

### Primary Objectives
- **🎯 Primary Goal:** Reduce the frequency of user-facing "soft fail" prompts (`needsType` and `needsCategory`) by at least **70%**
- **🚀 Secondary Goal:** Increase user confidence in the app's automated parsing

### Success Metrics
- A measurable decrease in the number of times `ParseStatus.needsCategory` and `ParseStatus.needsType` are returned to the `ChatScreen`

### Key Constraint
> ⚠️ **Important:** The existing parsing logic (Learned Associations, ML Kit, Regex) must remain the primary, default path. The Gemma service is a fallback enhancement, not a replacement.

## 3. API Contract

The client and server will communicate through a single, versatile Cloud Function.

### Endpoint Configuration
- **Endpoint:** HTTPS Callable Cloud Function named `getAiSuggestion`
- **Design Philosophy:** Task-based design - the function performs different tasks based on a parameter, making it reusable

### Request Format
**Client → Server:**
```json
{
  "text": "Dinner with friends at The Local Bistro $45",
  "task": "categorize" // or "get_transaction_type"
}
```

### Response Formats
**Success Response (Server → Client):**
```json
{
  "result": "food" // The result will be a valid category ID or transaction type name
}
```

**Error Response (Server → Client):**
```json
{
  "error": "Suggestion failed.",
  "details": "The API returned an invalid format." // Optional for debugging
}
```

---

## 4. Phased Implementation Plan

## Phase 1: Server-Side - Building the "AI Consultant"

**Team:** 🔧 Server/Backend Agent  
**Goal:** Create a secure, reliable, and task-oriented Cloud Function that can provide intelligent suggestions.

### Features Overview

| Feature | Description | Priority |
|---------|-------------|----------|
| **Firebase Project Setup** | Configure Firebase with Cloud Functions and Firestore | 🔴 Critical |
| **Secure, Task-Driven Cloud Function** | Create the `getAiSuggestion` Cloud Function | 🔴 Critical |
| **Prompt Engineering** | Craft highly specific prompts for constrained output | 🔴 Critical |
| **Data Logging for Future Training** | Log requests and results to Firestore | 🟡 Medium |

### Detailed Feature Specifications

u**User Story:** As a developer, I need a backend environment to securely run code.

**Acceptance Criteria:**
- A Firebase project is created and linked
- Cloud Functions and Firestore are enabled

#### Secure, Task-Driven Cloud Function
**User Story:** As a developer, I need a single endpoint that can handle different types of AI suggestions based on a "task" parameter.

**Acceptance Criteria:**
- The function `getAiSuggestion` is deployed
- It accepts `text` and `task` parameters
- The Gemma API key is stored securely using environment variables
- It uses different, optimized prompts based on the `task` (`categorize` vs. `get_transaction_type`)
- It returns a valid category ID or transaction type name, matching the API Contract
- It includes robust error handling for invalid tasks or failed Gemma API calls

#### Prompt Engineering
**User Story:** As a developer, I need to ensure the AI's response is predictable and easily parsable.

**Acceptance Criteria:**
- **For `categorize` task:** Prompt instructs the model to return *only* a valid category ID from the provided list
- **For `get_transaction_type` task:** Prompt instructs the model to return *only* one of `expense`, `income`, or `loan`
- The prompts are stored as separate text files or constants for easy management

#### Data Logging for Future Training
**User Story:** As a product owner, I want to collect high-quality data to eventually train a better on-device model.

**Acceptance Criteria:**
- On a successful Gemma response, a new document is written to a "gemma_suggestions" collection in Firestore
- The document contains `inputText`, `task`, `gemmaResult`, and `timestamp`

---

## Phase 2: Client-Side - Intelligent Integration

**Team:** 📱 Client/Flutter Agent  
**Goal:** Integrate the "AI Consultant" into the existing parsing flow at the precise moment of uncertainty.

### Features Overview

| Feature | Description | Priority |
|---------|-------------|----------|
| **Client-Side Service** | Create a service in Flutter to call the Cloud Function | 🔴 Critical |
| **Parser Augmentation** | Modify `MlKitParserService` to call AI before failing | 🔴 Critical |
| **Seamless Fallback** | Ensure app remains functional if AI service fails | 🔴 Critical |

### Detailed Feature Specifications

#### Client-Side Service
**User Story:** As a developer, I need a simple, reusable way to call our backend AI service.

**Acceptance Criteria:**
- A new `RemoteSuggestionService` is created
- It has one method: `Future<String?> getSuggestion({required String text, required String task})`
- It correctly calls the `getAiSuggestion` Cloud Function and handles the request/response based on the API Contract
- It includes a timeout of 10 seconds

#### Parser Augmentation
**User Story:** As a user, when the app is unsure about my transaction, I want it to try to figure it out intelligently before asking me for help.

**Acceptance Criteria:**
- `MlKitParserService` now depends on `RemoteSuggestionService`
- **Inside `_parseWithMLKit`:**
  1. When `_detectTransactionType()` returns `null`, the parser now calls `remoteSuggestionService.getSuggestion(task: 'get_transaction_type')`. If successful, it uses the result and continues
  2. When `categoryFinder.findCategory()` returns `null`, the parser now calls `remoteSuggestionService.getSuggestion(task: 'categorize')`. If successful, it uses the result
  3. **Only if the remote service fails or returns null** does the parser return `ParseStatus.needsType` or `ParseStatus.needsCategory`

#### Seamless Fallback
**User Story:** As a user, I want the app to work reliably, even if the "smart" features are temporarily unavailable.

**Acceptance Criteria:**
- The `RemoteSuggestionService` call is wrapped in a `try-catch` block inside the parser
- On any failure (network error, timeout, server error), the catch block logs the error and allows the parser to proceed to the existing "soft fail" logic (prompting the user)
- The user experience in case of failure is identical to the current app's behavior

---

## Phase 3: Closing the Loop (Future Optimization)

**Goal:** Leverage the AI's suggestions to improve the on-device learning system.

### Features Overview

| Feature | Team | Priority |
|---------|------|----------|
| **Implicit Learning from AI** | 📱 Client | 🟡 Medium |

### Detailed Feature Specifications

#### Implicit Learning from AI
**Team:** Client  
**User Story:** As a user, I expect the app to remember the smart suggestions it makes, so I don't have to correct them later.

**Acceptance Criteria:**
- When Gemma successfully provides a category/type and a transaction is saved, a "pending" learned association is created
- If the user does not edit this transaction within 24 hours, the pending association is committed to the `LearnedAssociationService`
- This prevents a potentially incorrect AI guess from immediately polluting the user's high-quality manual learning data

---

## Implementation Flow Diagram

```
User Input → Parse Pipeline
     ↓
1. Check LearnedAssociations (existing)
     ↓ (if not found)
2. ML Kit Analysis (existing)
     ↓ (if uncertain)
3. 🤖 AI Consultant (NEW!)
     ↓ (if AI fails)
4. Keyword/Regex Fallback (existing)
     ↓ (if still uncertain)
5. Prompt User (existing)
```

---

*This document outlines a conservative, additive approach to integrating AI assistance that enhances the existing robust parsing system without compromising its reliability or performance.*
