# PRD: Multiple Number Ambiguity Detection Fix
**Version:** 1.1
**Status:** Proposed
**Author:** Gemini
**Date:** 2025-07-27

## 1. Overview

This document outlines the plan to fix a critical bug in the `MlKitParserService` that prevents the "Multiple Number Soft Fail" feature from working correctly. The current implementation contains a logical flaw that causes the parser to prematurely select an amount, bypassing the intended ambiguity detection logic. This is caused by an over-reliance on the initial results from the real Google ML Kit service, which can mask underlying ambiguity.

## 2. Problem Statement

The core issue is a conflict between the "specialist" (real ML Kit service) and the "generalist" (the parser's internal tools like `AmountUtils`).

1.  **The Specialist's Blind Spot**: The real ML Kit service is a "black box" that may return an overly "clean" result. For an input like `"lux69 100"`, it might use its own context models to decide that `69` is part of a vendor name and therefore only return `[Money("100")]` as a candidate.
2.  **The Parser's Flawed Trust**: The current parsing logic trusts this "clean" result. It receives the single candidate `100` and, seeing no other candidates *from that source*, it proceeds without ever detecting an ambiguity.
3.  **The Logical Bypass**: The parser's own ability to find the number `69` is never used to challenge the specialist's incomplete result. The ambiguity detection logic is never triggered because, from the parser's point of view, it never received multiple numbers to be ambiguous about.

This results in the system making an incorrect assumption and failing to trigger the "soft fail" prompt as intended.

## 3. Proposed Solution: True Candidate Consolidation

The solution is to enforce a strict "Trust but Verify" policy. The parser must never blindly accept the ML Kit result as the sole source of truth. It must always use its own tools to find additional candidates.

The new, robust flow will be:

1.  **Step 1: Independent Parallel Extraction (The Core Fix)**
    *   **Path A (ML Kit):** Send the raw text to the ML Kit service and get its list of `Money` entities.
    *   **Path B (Internal Tools):** **Independently and in parallel**, run the parser's own internal number-finding tools (e.g., `AmountUtils`, regex) on the **exact same raw text** to generate a second, raw list of all possible numeric values.

2.  **Step 2: True Consolidation**
    *   Create a single, comprehensive list of all unique numeric candidates found from **both Path A and Path B**. This is the crucial step. Even if ML Kit returns only `[100]`, if the internal tools find `[69, 100]`, the consolidated list must be `[69, 100]`.

3.  **Step 3: Centralized Decision Logic**
    *   The existing ambiguity detection logic will be applied to this truly complete and consolidated list.

4.  **Step 4: Apply Soft Fail as Intended**
    *   If the centralized logic finds more than one plausible candidate in the consolidated list, it will trigger the `ParseResult.needsAmountConfirmation` status.
    *   If there is only one clear candidate, it will proceed to create the transaction.

This revised flow guarantees that the parser's own robust, generalist tools are always used to verify and enrich the results from the specialist ML Kit service. It ensures that hidden ambiguities are always uncovered, allowing the soft fail mechanism to function as designed.