# PRD: The Unified Learning System
**Version:** 1.0
**Status:** Proposed
**Author:** Gemini
**Date:** 2025-07-26

## 1. Overview

This document outlines the requirements for creating a **Unified Learning System** within the Money Lover Chat application. Building upon the "soft fail" conversational enhancements, this feature will enable the app to learn from user corrections, whether they happen during initial transaction entry or during a later manual edit.

The goal is to create a system that gets progressively smarter and more accurate with use, significantly reducing the need for repetitive manual corrections and building user trust in the app's intelligence.

## 2. Problem Statement

The current system is helpful but has no long-term memory of user preferences.
1.  **It doesn't learn from corrections**: When a user resolves a "soft fail" (e.g., selects a transaction type or category), the system completes the single transaction but doesn't remember that choice for the future.
2.  **It doesn't learn from edits**: If a user manually edits a transaction's category or type from the transaction list, that valuable feedback is completely ignored by the parsing system.

This leads to a static user experience where the user may have to correct the same type of transaction (e.g., always changing "Netflix" from "Utilities" to "Entertainment") repeatedly, undermining the feeling of an intelligent assistant.

## 3. Goals

-   **Implement True Learning**: Create a persistent memory of user preferences for transaction types and categories based on text patterns.
-   **Reduce User Friction**: Drastically decrease the number of "soft fail" prompts for transactions the user has entered or corrected before.
-   **Increase Parsing Speed & Accuracy**: For known text patterns, bypass the complex parsing logic entirely and use the learned association for near-instant, 100% accurate results.
-   **Build User Confidence**: Create a system that visibly adapts to the user's habits, making them feel understood.

## 4. Proposed Solution: The Unified Learning System

We will introduce a new, centralized service responsible for storing and retrieving learned associations between text patterns and transaction attributes.

### 4.1. The `LearnedAssociationService`

This new service will manage a key-value store where the key is a normalized text pattern (e.g., "the local bistro") and the value is an object containing the learned attributes.

**Proposed Data Structure (in SharedPreferences):**
```json
{
  "the local bistro": {
    "type": "expense",
    "categoryId": "food",
    "lastUpdated": "2025-07-26T12:00:00Z",
    "confidence": 5
  },
  "acme inc salary": {
    "type": "income",
    "categoryId": "salary",
    "lastUpdated": "2025-07-25T09:00:00Z",
    "confidence": 12
  }
}
```

### 4.2. Learning Triggers: Where the App Learns

The system will learn from two primary user actions, treating each as a high-value signal of the user's intent.

1.  **During the "Soft Fail" Flow**:
    -   **Trigger**: A user selects a type or category via a quick-reply button or the category picker dialog.
    -   **Action**: The `ChatScreen` will call `LearnedAssociationService.learn(text, type: selectedType, categoryId: selectedCategory)`.

2.  **During Manual Transaction Editing**:
    -   **Trigger**: A user edits an existing transaction and changes its `type` or `categoryId`.
    -   **Action**: The `TransactionProvider`'s `updateTransaction` method will detect the change and call the `LearnedAssociationService` to **overwrite** the previous association with the new, corrected data. This is the most critical signal for correcting bad habits.

### 4.3. The Updated Parsing Flow

The `MlKitParserService` will be updated to consult the `LearnedAssociationService` *before* any other processing.

```mermaid
sequenceDiagram
    participant User
    participant ChatScreen
    participant MlKitParserService
    participant LearnedAssociationService

    User->>ChatScreen: Enters text: "Dinner at The Local Bistro 50"
    ChatScreen->>MlKitParserService: parseTransaction(text)
    
    MlKitParserService->>LearnedAssociationService: getAssociation("dinner at the local bistro")
    
    alt Learned Association Found
        LearnedAssociationService-->>MlKitParserService: Return {type: "expense", categoryId: "food"}
        Note over MlKitParserService: Bypasses ML Kit & Regex. Creates complete transaction.
        MlKitParserService-->>ChatScreen: Return ParseResult(status=success)
        ChatScreen->>User: "✅ Transaction Saved"
    else No Association Found
        LearnedAssociationService-->>MlKitParserService: Return null
        Note over MlKitParserService: Proceeds with existing ML Kit / Fallback flow.
        MlKitParserService-->>ChatScreen: Return ParseResult(status=needsCategory)
        Note over ChatScreen: Triggers soft-fail flow, which then triggers learning.
    end
```

## 5. Technical Implementation Strategy

1.  **`lib/services/parser/learned_association_service.dart` (New)**:
    -   Create the new service to manage the JSON data structure in `SharedPreferences`.
    -   Implement `learn(String text, {TransactionType? type, String? categoryId})`. This method will fetch the existing record for the text, update its fields, and save it back.
    -   Implement `getAssociation(String text)`.
    -   Include logic for text normalization (lowercase, trim) to ensure consistent keys.

2.  **`lib/services/parser/mlkit_parser_service.dart` (Refactor)**:
    -   Inject the `LearnedAssociationService`.
    -   In `parseTransaction`, the very first step will be to call `getAssociation`. If a result is returned, the service will construct the `Transaction` object and return a success result immediately.

3.  **`lib/screens/chat_screen.dart` (Refactor)**:
    -   In the handlers for quick-reply selection and `CategoryPickerDialog` selection, add a call to `LearnedAssociationService.learn(...)` to save the user's choice.

4.  **`lib/models/transaction_model.dart` (Refactor `TransactionProvider`)**:
    -   In the `updateTransaction` method within `TransactionProvider`, compare the original transaction with the updated one.
    -   If `type` or `categoryId` has changed, call `LearnedAssociationService.learn(...)` with the transaction's description and the new, corrected values.

## 6. Success Criteria

-   When a user categorizes a transaction for "Vendor X" as "Shopping", the next transaction for "Vendor X" is automatically categorized as "Shopping" without a soft-fail prompt.
-   When a user manually edits a transaction for "My Gym" from "Other" to "Health", the next transaction for "My Gym" is correctly categorized as "Health".
-   The rate of soft-fail prompts for a user's recurring transaction descriptions demonstrably decreases over the first 5-10 entries.
-   The system feels like it is actively adapting to the user's personal financial vocabulary.
