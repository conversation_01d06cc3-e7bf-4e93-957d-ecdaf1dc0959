I have created the following plan after thorough exploration and analysis of the codebase. Follow the below plan verbatim. Trust the files and references. Do not re-verify what's written in the plan. Explore only when absolutely necessary. First implement all the proposed file changes and then I'll review all the changes together at the end.

### Observations

I've analyzed the Money Lover Chat Flutter app's startup performance and identified the primary bottleneck. The main issue is that the app waits for ML Kit model download to complete before showing any UI. This happens in `main()` where `await MlKitParserService.getInstance()` blocks until the ML Kit model is downloaded, which can take seconds or minutes on first launch. The app has robust fallback mechanisms already in place - if ML Kit fails, it uses regex-based parsing. Provider constructors (ThemeProvider, TransactionProvider) don't block the UI as their async operations aren't awaited in main(). The solution is to move ML Kit initialization to background after the first frame renders.

### Approach

The solution focuses on **verification first, then minimal safe changes**:

1. **Add Performance Measurement**: Instrument startup with timestamps to quantify each bottleneck
2. **Move ML Kit to Background**: Remove the blocking `await MlKitParserService.getInstance()` from main() and initialize it after first frame
3. **Add Model Download Caching**: Store a flag in SharedPreferences after successful ML Kit model download to skip checks on subsequent launches
4. **Preserve All Existing Logic**: Keep all fallback mechanisms, error handling, and user experience intact

This approach is conservative and safe because the app already has comprehensive fallback mechanisms when ML Kit is unavailable.

### Reasoning

I analyzed the codebase systematically to understand the startup flow. I examined the main entry point and traced through all synchronous operations that block the UI. I found that the main bottleneck is in the ML Kit service initialization, specifically the model download process. I also verified that robust fallback mechanisms exist, and that provider constructors don't actually block the UI. I confirmed that screens can handle loading states and missing data gracefully.

## Mermaid Diagram

sequenceDiagram
    participant User
    participant Main as main()
    participant Storage as StorageService
    participant UI as App UI
    participant MLKit as MlKitParserService
    participant Chat as ChatScreen
    
    User->>Main: Launch App
    Main->>Main: StartupTimer.mark('app-start')
    Main->>Storage: init() (fast)
    Storage-->>Main: Ready
    Main->>Main: StartupTimer.mark('storage-ready')
    Main->>UI: runApp() - Show UI immediately
    UI->>User: Display app interface
    Main->>Main: StartupTimer.mark('ui-shown')
    
    Note over Main,MLKit: Background initialization starts
    Main->>MLKit: initializeInBackground() (async, no await)
    MLKit->>MLKit: Check cached model flag
    alt Model already downloaded
        MLKit->>MLKit: Skip download, quick init
    else Model needs download
        MLKit->>MLKit: Download model (slow, in background)
    end
    MLKit-->>Main: Ready (updates provider)
    Main->>Main: StartupTimer.mark('mlkit-ready')
    
    User->>Chat: Send first message
    Chat->>MLKit: parseTransaction()
    alt MLKit ready
        MLKit-->>Chat: Advanced parsing result
    else MLKit not ready
        MLKit-->>Chat: Fallback regex parsing
    end
    Chat->>User: Show parsed transaction

## Proposed File Changes

### lib/main.dart(MODIFY)

References: 

- lib/services/storage_service.dart
- lib/services/parser/mlkit_parser_service.dart(MODIFY)

**Add startup performance measurement and move ML Kit initialization to background:**

1. **Add performance logging imports**: Import `dart:developer` for Timeline or use DateTime for simple logging
2. **Add startup timer helper**: Create a simple class to track initialization times with `DateTime.now().microsecondsSinceEpoch`
3. **Remove blocking ML Kit initialization**: Remove the `await MlKitParserService.getInstance(storageService)` call from lines 16-22
4. **Modify MultiProvider setup**: Change the ML Kit provider to handle nullable service initially using `Provider<MlKitParserService?>.value(value: null)`
5. **Add background initialization**: After `runApp()`, use `WidgetsBinding.instance.addPostFrameCallback()` to start ML Kit initialization once the first frame is rendered
6. **Add performance markers**: Wrap each major operation (StorageService.init, runApp, background ML Kit init) with timer markers
7. **Create background initialization method**: Add a method that initializes ML Kit and updates the provider once ready

This ensures the UI appears immediately after storage initialization while ML Kit downloads in the background.

### lib/services/parser/real_entity_extractor.dart(MODIFY)

References: 

- lib/services/storage_service.dart

**Add model download caching to prevent redundant downloads:**

1. **Add StorageService dependency**: Modify the class to accept a StorageService instance for caching
2. **Add cache key constant**: Define a constant for the SharedPreferences key like `'ml_kit_en_model_downloaded'`
3. **Check cache before download**: Before calling `manager.isModelDownloaded()`, check if we have a cached flag indicating the model was previously downloaded
4. **Skip download check if cached**: If the cache flag is true, skip the expensive `isModelDownloaded()` and `downloadModel()` calls
5. **Set cache flag after successful download**: After successful model download, store the flag in SharedPreferences
6. **Add error handling for cache**: If download fails, ensure the cache flag is not set or is reset
7. **Add performance logging**: Measure and log the time taken for model download operations
8. **Maintain backward compatibility**: Ensure the caching is optional and doesn't break existing functionality

This optimization significantly reduces startup time on subsequent app launches after the initial model download.

### lib/services/parser/mlkit_parser_service.dart(MODIFY)

References: 

- lib/services/storage_service.dart
- lib/services/parser/real_entity_extractor.dart(MODIFY)
- lib/services/parser/fallback_parser_service.dart

**Add background initialization support and improve null safety:**

1. **Add static background initialization method**: Create `static Future<void> initializeInBackground(StorageService storageService)` that can be called after UI is shown
2. **Enhance getInstance for background use**: Modify `getInstance()` to support being called from background without blocking
3. **Add readiness indicator**: Add a `bool get isReady` getter that returns true when ML Kit is fully initialized and available
4. **Improve null safety in parsing**: Ensure `parseTransaction()` gracefully handles the case where ML Kit is not yet initialized
5. **Add initialization status tracking**: Add methods to check initialization progress and notify when ready
6. **Enhance error handling**: Ensure the service works correctly when ML Kit initialization is delayed or fails
7. **Add performance logging**: Log initialization times and success/failure status
8. **Maintain singleton pattern**: Ensure thread safety when initializing from background

The service should seamlessly handle being used before full ML Kit initialization, falling back to regex parsing until ML Kit becomes available.

### lib/screens/chat_screen.dart(MODIFY)

References: 

- lib/services/parser/mlkit_parser_service.dart(MODIFY)
- lib/services/storage_service.dart
- lib/models/transaction_model.dart

**Update to handle progressive ML Kit initialization:**

1. **Remove duplicate service initialization**: Remove the `_initializeParserService()` method that creates duplicate StorageService and MlKitParserService instances
2. **Use provider services**: Get ML Kit service from the Provider instead of creating new instances
3. **Handle nullable ML Kit service**: Add null checks for when MlKitParserService is not yet available from the provider
4. **Add service readiness listener**: Listen for when ML Kit service becomes available and update UI accordingly
5. **Show parsing mode status**: Display appropriate messages to users about parsing capabilities ("Basic mode" vs "Full ML Kit available")
6. **Update error handling**: Handle the case where ML Kit becomes available after the screen is already loaded
7. **Maintain existing fallback behavior**: Ensure the screen continues to work with regex parsing when ML Kit is unavailable
8. **Add loading state for ML Kit**: Show a subtle indicator when ML Kit is initializing in the background

This eliminates duplicate service initialization and provides better user feedback about the parsing capabilities available.

### lib/utils/startup_timer.dart(NEW)

**Create a simple startup performance measurement utility:**

1. **Create singleton timer class**: Implement a simple class to track startup performance metrics
2. **Add timing methods**: Provide methods like `mark(String label)` and `measure(String startLabel, String endLabel)`
3. **Store timestamps**: Use `DateTime.now().microsecondsSinceEpoch` for high-precision timing
4. **Add logging methods**: Provide methods to print timing results to console
5. **Add conditional compilation**: Use `kDebugMode` to ensure timing code is only active in debug builds
6. **Add summary method**: Provide a method to print a summary of all startup timings
7. **Keep it lightweight**: Ensure the timer itself doesn't add significant overhead
8. **Add reset functionality**: Allow clearing timing data for multiple test runs

This utility will help measure the actual impact of the startup optimizations and detect any regressions in the future.

### test/performance/startup_performance_test.dart(NEW)

References: 

- lib/main.dart(MODIFY)
- lib/utils/startup_timer.dart(NEW)

**Create integration test to measure and verify startup performance:**

1. **Create startup timing test**: Implement a test that measures app startup time from launch to first frame
2. **Test cold start scenario**: Measure startup time when ML Kit model needs to be downloaded (first launch)
3. **Test warm start scenario**: Measure startup time when ML Kit model is already cached (subsequent launches)
4. **Add performance assertions**: Set reasonable thresholds for startup times (e.g., first frame within 2 seconds)
5. **Test background initialization**: Verify that ML Kit initializes correctly in the background
6. **Test fallback behavior**: Ensure the app works correctly when ML Kit initialization fails
7. **Add device variation testing**: Test on different device configurations if possible
8. **Create performance regression detection**: Fail the test if startup time exceeds acceptable limits

This test ensures that startup performance improvements are maintained and prevents regressions in future development.

### docs/startup_optimization.md(NEW)

**Create documentation for startup performance optimization:**

1. **Document the problem**: Explain the original startup performance issue with ML Kit model download
2. **Explain the solution**: Describe the background initialization approach and its benefits
3. **Document performance improvements**: Include before/after timing measurements
4. **Provide troubleshooting guide**: Help developers debug startup-related issues
5. **Add best practices**: Guidelines for maintaining good startup performance when adding new features
6. **Document the fallback mechanisms**: Explain how the app handles ML Kit unavailability
7. **Include testing procedures**: How to test startup performance and verify optimizations
8. **Add monitoring recommendations**: Suggest ways to monitor startup performance in production

This documentation helps future developers understand the optimization and maintain good startup performance.