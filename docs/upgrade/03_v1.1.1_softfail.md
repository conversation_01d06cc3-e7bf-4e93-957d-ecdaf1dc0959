# PRD: Conversational Assistant Enhancements
**Version:** 1.0
**Status:** Proposed
**Author:** Gemini
**Date:** 2025-07-26

## 1. Overview

This document outlines the requirements for enhancing the Money Lover Chat feature, transforming it from a simple parser into a more intelligent and resilient conversational assistant. The core of this proposal is to eliminate interaction dead-ends by gracefully handling ambiguous user inputs and to build user trust by making the system's learning capabilities explicit.

## 2. Problem Statement

Currently, when the transaction parser fails to determine the transaction type (e.g., for an input like `"Movie night 20"`), it responds with a generic failure message: `"Could not determine the transaction"`. This creates a frustrating user experience, as it forces the user to rephrase their input without guidance and breaks the conversational flow.

Additionally, while the system learns from a user's manual category selections, this process is invisible to the user. They are not aware that their actions are making the assistant smarter, which is a missed opportunity to build confidence and engagement.

## 3. Goals

-   **Eliminate Parsing Dead-Ends**: Ensure the chatbot can always proceed with a transaction entry, even with ambiguous input, by asking clarifying questions.
-   **Improve User Experience**: Make the chat interaction feel more natural, helpful, and intelligent.
-   **Increase User Trust**: Make the app's learning process transparent to the user, reinforcing the value of their input.
-   **Reduce User Friction**: Minimize the cognitive load and number of steps required to log a transaction successfully.

## 4. Proposed Features

### 4.1. Feature 1: Transaction Type Disambiguation with Quick Replies

When the parser successfully extracts an amount but cannot determine the transaction type (expense, income, etc.), it will no longer fail. Instead, it will prompt the user for clarification using interactive quick-reply buttons.

**User Flow:**

```mermaid
sequenceDiagram
    participant User
    participant ChatScreen
    participant ParserService

    User->>ChatScreen: Enters text: "Movie night 20"
    ChatScreen->>ParserService: parseTransaction("Movie night 20")
    Note over ParserService: Extracts amount (20), but type is unclear.
    ParserService-->>ChatScreen: Return ParseResult(needsTypeDisambiguation)

    ChatScreen->>User: Display message: "I see $20 for 'Movie night'. What type of transaction is this?"
    ChatScreen->>User: Display Quick Reply buttons: [ Expense ], [ Income ], [ Cancel ]

    User->>ChatScreen: Taps [ Expense ]
    ChatScreen->>ParserService: Process as Expense
    Note over ChatScreen: Proceeds to category detection/selection flow.
```

**Requirements:**
-   The `ParserService` must introduce a new result state, e.g., `needsTypeDisambiguation`.
-   The `ChatScreen` must be able to render a system message with a set of interactive, single-tap quick-reply buttons.
-   Tapping a quick-reply button should continue the transaction logging flow with the selected type.
-   The `[ Cancel ]` button should gracefully terminate the current transaction entry attempt.

### 4.2. Feature 2: Explicit Learning Confirmation

After a user manually selects a category for a transaction using the `CategoryPickerDialog`, the assistant will provide an explicit confirmation that reinforces the learning mechanism.

**User Flow:**

```mermaid
sequenceDiagram
    participant User
    participant ChatScreen
    participant CategoryFinderService

    Note over User, ChatScreen: User enters "Dinner at The Local Bistro 45"
    Note over ChatScreen, CategoryFinderService: Parser cannot find a category, so ChatScreen shows CategoryPickerDialog.

    User->>ChatScreen: Selects 'Food & Drink' from dialog
    ChatScreen->>CategoryFinderService: learnCategory("The Local Bistro", "food_drink_id")
    ChatScreen->>ChatScreen: Save the complete transaction.

    ChatScreen->>User: Display confirmation message: "✅ Got it. I've saved 'Dinner at The Local Bistro' as a $45 expense under 'Food & Drink'."
    ChatScreen->>User: Display learning message: "I'll remember to categorize 'The Local Bistro' as Food & Drink for you next time."
```

**Requirements:**
-   After a category is chosen from the `CategoryPickerDialog` and the transaction is saved, two new system messages should be displayed in the chat.
-   The first message confirms the transaction details as saved.
-   The second message explicitly states that the app has learned this association for future use.

## 5. Technical Implementation Strategy

1.  **`models/parse_result.dart`**:
    -   Introduce a new enum `ParseStatus { success, needsCategory, needsType, failed }`.
    -   Update `ParseResult` to use this enum instead of boolean flags.

2.  **Parser Services (`mlkit_parser_service.dart`, `fallback_parser_service.dart`)**:
    -   Modify the parsers to return a `ParseResult` with `status: ParseStatus.needsType` when the transaction type is ambiguous but an amount is present.

3.  **`lib/widgets/quick_reply_widget.dart` (New)**:
    -   Create a new stateless widget that takes a list of strings and a callback function.
    -   Renders a row of styled buttons (`OutlinedButton` or `FilledButton.tonal`).

4.  **`lib/screens/chat_screen.dart`**:
    -   Update the `_sendMessage` method to handle the new `ParseStatus.needsType` state by displaying the quick reply widget.
    -   Update the logic following a successful category selection from `CategoryPickerDialog` to post the two new confirmation and learning messages.

## 6. Success Criteria

-   The "Could not determine the transaction" error is no longer shown to the user for ambiguous transaction types.
-   User testing confirms that the quick-reply disambiguation flow is intuitive and helpful.
-   User testing confirms that the explicit learning confirmation message improves user confidence in the system's intelligence.
-   A 100% of ambiguous-type transactions can be successfully logged through the new disambiguation flow.
