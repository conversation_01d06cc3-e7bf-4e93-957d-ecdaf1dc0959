# PRD: Regex Fallback System Restructuring
**Version:** 1.0
**Status:** Proposed
**Author:** Gemini
**Date:** 2025-07-26

## 1. Overview

This document outlines the requirements for a critical architectural refactoring of the `FallbackParserService`. The goal is to replace the current hardcoded, brittle regex logic with a scalable, maintainable, and localization-ready system. This ensures that the fallback parser, which serves as a crucial safety net for when ML Kit is unavailable, is not a liability for the app's internationalization goals.

## 2. Problem Statement

The current `FallbackParserService` was implemented as a direct, regex-based equivalent of the original parser. This approach has significant architectural flaws:

1.  **Hardcoded English Keywords**: The regex patterns contain English-specific keywords like "spent," "paid," and "salary," making the fallback system completely non-functional for users in any other language.
2.  **US-Centric Formatting**: The patterns assume US-centric currency formats (e.g., `$1,234.56`) and will fail to correctly parse common international formats (e.g., `1.234,56 €`).
3.  **Poor Maintainability & Scalability**: The regex strings are embedded directly in the Dart code. Expanding this to support more languages or formats would lead to unreadable, unmanageable code and a high risk of introducing bugs.

This implementation directly contradicts the primary goal of supporting a global user base and creates a jarring experience for non-English users when the app enters fallback mode.

## 3. Goals

-   **Decouple Localization from Logic**: Separate language-specific keywords and patterns from the application's business logic.
-   **Improve Maintainability**: Make it easy to view, edit, and manage parsing patterns without modifying the core Dart code.
-   **Create a Scalable Architecture**: Establish a clear and simple process for adding new languages to the fallback system in the future.
-   **Enhance Robustness**: While not aiming to replicate ML Kit, the fallback should be more robust in handling different number and currency formats for the languages it supports.

## 4. Proposed Solution: The "Resource File" Architecture

We will refactor the `FallbackParserService` to dynamically build its regex patterns at runtime by loading them from external JSON resource files.

### 4.1. High-Level Architecture

```mermaid
graph TD
    subgraph "App Initialization"
        A[Device Locale Detected] --> B{LocalizationService};
        B --> C[Load correct l10n JSON file];
    end

    subgraph "Fallback Parsing Flow"
        D[User Input] --> E{FallbackParserService};
        E --> F[Request patterns from LocalizationService];
        F --> G[Build Regex from patterns];
        G --> H[Parse Input];
        H --> I[Return Transaction];
    end

    C --> F;
```

### 4.2. Localization Asset Structure

A new directory will be created to house the localization files: `assets/l10n/`.

Inside this directory, we will have a JSON file for each supported language. For the initial implementation, we will start with English.

**`assets/l10n/en.json`:**
```json
{
  "locale": "en-US",
  "decimal_separator": ".",
  "thousands_separator": ",",
  "expense_keywords": [
    "spent", "paid", "bought", "purchased", "expense", "charge", "cost", "dinner", "lunch", "breakfast"
  ],
  "income_keywords": [
    "received", "earned", "income", "salary", "got", "sold", "bonus", "gift"
  ],
  "loan_keywords": [
    "borrowed", "lent", "loan", "debt"
  ]
}
```

**Future `assets/l10n/es.json` (Example):**
```json
{
  "locale": "es-ES",
  "decimal_separator": ",",
  "thousands_separator": ".",
  "expense_keywords": [
    "gasté", "pagado", "comprado", "costo", "cena", "almuerzo"
  ],
  "income_keywords": [
    "recibido", "ganado", "salario", "vendido", "regalo"
  ],
  "loan_keywords": [
    "prestado", "préstamo", "deuda"
  ]
}
```

### 4.3. Refactored Service Logic

The `FallbackParserService` will be modified to:
1.  No longer contain any hardcoded keywords or regex patterns.
2.  Depend on a new `LocalizationService` (or similar manager).
3.  On `parseTransaction`, it will request the localization patterns for the device's current locale.
4.  It will then dynamically construct its regex using the keywords and separators provided in the loaded JSON data.

This approach ensures that the parsing logic remains generic, while all the language-specific details are handled in easily editable resource files.

## 5. Technical Implementation Strategy

1.  **`pubspec.yaml`**:
    -   Declare the `assets/l10n/` directory so the JSON files are included in the app bundle.

2.  **`assets/l10n/en.json` (New)**:
    -   Create the initial JSON file for the English language as detailed above.

3.  **`lib/services/localization_service.dart` (New)**:
    -   Create a new service responsible for managing the loading and caching of localization files.
    -   It will expose a method like `Future<Map<String, dynamic>> getPatternsForLocale(Locale locale)`.
    -   This service will handle the logic of loading the correct JSON file from the assets bundle.

4.  **`lib/services/parser/fallback_parser_service.dart` (Refactor)**:
    -   Remove all hardcoded regex strings.
    -   Inject the `LocalizationService`.
    -   Modify the `parseTransaction` method to be `async`.
    -   Before parsing, it will `await` the patterns from the `LocalizationService`.
    -   It will then build its keyword and number-parsing regex patterns dynamically using the retrieved data.

## 6. Success Criteria

-   All hardcoded keywords and regex patterns are successfully removed from `fallback_parser_service.dart`.
-   The `FallbackParserService` correctly loads its patterns from `assets/l10n/en.json` at runtime.
-   The fallback system continues to parse English transactions with the same or higher degree of accuracy as before.
-   The architecture is validated by demonstrating that adding a new `es.json` file and setting the device locale to Spanish allows the fallback parser to correctly recognize Spanish keywords without any changes to the Dart code.
