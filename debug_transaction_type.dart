#!/usr/bin/env dart

// Test the transaction type detection logic directly
void main() {
  print('Testing transaction type detection for "2000 dinner"...\n');
  
  final text = '2000 dinner';
  final result = detectTransactionType(text);
  
  print('Input: "$text"');
  print('Result: $result');
  
  // Let's trace through the logic step by step
  final normalizedText = text.toLowerCase().trim();
  print('\nStep-by-step analysis:');
  print('Normalized text: "$normalizedText"');
  
  // Test each regex pattern
  final patterns = [
    {'name': 'Starting with -', 'pattern': r'^\s*-', 'type': 'expense'},
    {'name': 'Expense keywords', 'pattern': r'(spent|paid|bought|purchased|expense|pay|payment|cost|spent on|paid for|charge|bought for|dinner|lunch|breakfast|meal|food|coffee|restaurant|groceries|shopping|gas|fuel)', 'type': 'expense'},
    {'name': 'Income keywords', 'pattern': r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift|bonus|dividend|interest|return|gain|profit|reward)', 'type': 'income'},
    {'name': 'Loan keywords', 'pattern': r'(borrowed|lent|loan|debt|credit|lend|borrowed from|lent to)', 'type': 'loan'},
    {'name': 'Contains "for"', 'pattern': r'\bfor\b', 'type': 'expense (conditional)'},
    {'name': 'Currency symbols', 'pattern': r'[$€£¥]', 'type': 'expense'},
  ];
  
  for (final pattern in patterns) {
    final regex = RegExp(pattern['pattern']!);
    final matches = regex.hasMatch(normalizedText);
    print('${pattern['name']}: ${matches ? "✅ MATCH" : "❌ NO MATCH"} (${pattern['type']})');
    
    if (matches && pattern['name'] == 'Expense keywords') {
      final match = regex.firstMatch(normalizedText);
      print('  Matched word: "${match?.group(0)}"');
    }
  }
}

enum TransactionType { expense, income, loan }

TransactionType? detectTransactionType(String text) {
  final normalizedText = text.toLowerCase().trim();
  
  if (RegExp(r'^\s*-').hasMatch(normalizedText)) {
    return TransactionType.expense;
  }
  
  if (RegExp(r'(spent|paid|bought|purchased|expense|pay|payment|cost|spent on|paid for|charge|bought for|dinner|lunch|breakfast|meal|food|coffee|restaurant|groceries|shopping|gas|fuel)')
      .hasMatch(normalizedText)) {
    return TransactionType.expense;
  }
  
  if (RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift|bonus|dividend|interest|return|gain|profit|reward)')
      .hasMatch(normalizedText)) {
    return TransactionType.income;
  }
  
  if (RegExp(r'(borrowed|lent|loan|debt|credit|lend|borrowed from|lent to)')
      .hasMatch(normalizedText)) {
    return TransactionType.loan;
  }
  
  if (RegExp(r'\bfor\b').hasMatch(normalizedText) && 
      !RegExp(r'(received|earned|income|salary|payment received|got paid|got money|earned from|money from|receive|selling|sold|gift).*?\bfor\b').hasMatch(normalizedText)) {
    return TransactionType.expense;
  }
  
  if (RegExp(r'[$€£¥]').hasMatch(normalizedText)) {
    return TransactionType.expense;
  }
  
  return null;
}
