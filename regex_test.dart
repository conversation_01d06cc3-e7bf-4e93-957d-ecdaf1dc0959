void main() {
  print('Testing transaction type detection for "2000 dinner"...');
  
  final text = '2000 dinner';
  final normalizedText = text.toLowerCase().trim();
  print('Normalized text: "$normalizedText"');
  
  // Test the expense regex specifically
  final expensePattern = RegExp(r'(spent|paid|bought|purchased|expense|pay|payment|cost|spent on|paid for|charge|bought for|dinner|lunch|breakfast|meal|food|coffee|restaurant|groceries|shopping|gas|fuel)');
  final matches = expensePattern.hasMatch(normalizedText);
  
  print('Expense pattern matches: $matches');
  
  if (matches) {
    final match = expensePattern.firstMatch(normalizedText);
    print('Matched word: "${match?.group(0)}"');
  }
  
  // Test if "dinner" specifically works
  final dinnerTest = RegExp(r'dinner').hasMatch(normalizedText);
  print('Dinner specifically matches: $dinnerTest');
}
