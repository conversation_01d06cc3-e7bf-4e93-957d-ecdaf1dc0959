#!/usr/bin/env dart

// Simple test function to check if our fixes work
void main() {
  print('Testing transaction parsing fixes...\n');
  
  // Test 1: Check if food-related terms are in expense regex
  final testText = '2000 dinner';
  final lowerText = testText.toLowerCase();
  final expensePattern = RegExp(r'(spent|paid|bought|purchased|expense|pay|payment|cost|spent on|paid for|charge|bought for|dinner|lunch|breakfast|meal|food|coffee|restaurant|groceries|shopping|gas|fuel)');
  
  if (expensePattern.hasMatch(lowerText)) {
    print('✅ "dinner" matches expense pattern');
  } else {
    print('❌ "dinner" does NOT match expense pattern');
  }
  
  // Test 2: Check if the pattern works for other food terms
  final foodTerms = ['lunch', 'breakfast', 'meal', 'food', 'coffee', 'restaurant', 'groceries'];
  print('\nTesting other food terms:');
  for (final term in foodTerms) {
    if (expensePattern.hasMatch(term)) {
      print('✅ "$term" matches expense pattern');
    } else {
      print('❌ "$term" does NOT match expense pattern');
    }
  }
  
  print('\nTest completed!');
  print('Summary: Added food-related terms to expense detection and "Other" category for expenses.');
}
