#!/bin/bash

# Debug Multiple Numbers Detection Script
# This script monitors ADB logs for multiple number detection related messages
# Usage: ./scripts/debug_multiple_numbers.sh [options]

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Default values
SAVE_LOGS=false
LOG_FILE=""
FILTER_SCENARIO=""
VERBOSE=false

# Function to display usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Monitor ADB logs for multiple number detection debugging"
    echo ""
    echo "OPTIONS:"
    echo "  -s, --save FILE     Save logs to specified file"
    echo "  -f, --filter TEXT   Filter logs for specific test scenario"
    echo "  -v, --verbose       Show verbose output"
    echo "  -c, --clear         Clear ADB logs before starting"
    echo "  -h, --help          Show this help message"
    echo ""
    echo "EXAMPLES:"
    echo "  $0                           # Basic monitoring"
    echo "  $0 -c -s debug.log          # Clear logs and save to file"
    echo "  $0 -f \"lux69 100\"           # Filter for specific test scenario"
    echo "  $0 -v -s verbose.log        # Verbose mode with log saving"
    echo ""
    echo "KEY LOG PATTERNS TO WATCH FOR:"
    echo "  - 'ML Kit found entities'"
    echo "  - 'Raw finder found'"
    echo "  - 'Consolidated candidates'"
    echo "  - 'Amount confirmation triggered'"
    echo "  - 'Quick replies rendered'"
    echo "  - 'User selected amount'"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--save)
            SAVE_LOGS=true
            LOG_FILE="$2"
            shift 2
            ;;
        -f|--filter)
            FILTER_SCENARIO="$2"
            shift 2
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -c|--clear)
            echo -e "${YELLOW}Clearing ADB logs...${NC}"
            adb logcat -c
            echo -e "${GREEN}ADB logs cleared.${NC}"
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            echo -e "${RED}Unknown option: $1${NC}"
            show_usage
            exit 1
            ;;
    esac
done

# Check if ADB is available
if ! command -v adb &> /dev/null; then
    echo -e "${RED}Error: ADB is not installed or not in PATH${NC}"
    exit 1
fi

# Check if device is connected
DEVICE_COUNT=$(adb devices | grep -c "device$")
if [ "$DEVICE_COUNT" -eq 0 ]; then
    echo -e "${RED}Error: No Android device connected${NC}"
    echo "Please connect a device or start an emulator"
    exit 1
fi

echo -e "${GREEN}Connected devices:${NC}"
adb devices
echo ""

# Setup log file if requested
if [ "$SAVE_LOGS" = true ]; then
    if [ -z "$LOG_FILE" ]; then
        LOG_FILE="debug_multiple_numbers_$(date +%Y%m%d_%H%M%S).log"
    fi
    echo -e "${BLUE}Saving logs to: $LOG_FILE${NC}"
    echo "# Multiple Number Detection Debug Log - $(date)" > "$LOG_FILE"
    echo "# Generated by debug_multiple_numbers.sh" >> "$LOG_FILE"
    echo "" >> "$LOG_FILE"
fi

# Function to colorize and process log lines
process_log_line() {
    local line="$1"
    local timestamp=$(date '+%H:%M:%S.%3N')
    
    # Save to file if requested
    if [ "$SAVE_LOGS" = true ]; then
        echo "[$timestamp] $line" >> "$LOG_FILE"
    fi
    
    # Apply color coding based on content
    if [[ "$line" == *"ERROR:"* ]]; then
        echo -e "${RED}[$timestamp] $line${NC}"
    elif [[ "$line" == *"ML Kit found entities"* ]]; then
        echo -e "${BLUE}[$timestamp] $line${NC}"
    elif [[ "$line" == *"Raw finder found"* ]]; then
        echo -e "${CYAN}[$timestamp] $line${NC}"
    elif [[ "$line" == *"Consolidated candidates"* ]]; then
        echo -e "${PURPLE}[$timestamp] $line${NC}"
    elif [[ "$line" == *"Amount confirmation triggered"* ]]; then
        echo -e "${YELLOW}[$timestamp] $line${NC}"
    elif [[ "$line" == *"Quick replies rendered"* ]]; then
        echo -e "${GREEN}[$timestamp] $line${NC}"
    elif [[ "$line" == *"User selected amount"* ]]; then
        echo -e "${GREEN}[$timestamp] $line${NC}"
    elif [[ "$line" == *"Transaction completed"* ]]; then
        echo -e "${GREEN}[$timestamp] $line${NC}"
    elif [[ "$line" == *"DEBUG:"* ]]; then
        if [ "$VERBOSE" = true ]; then
            echo -e "${NC}[$timestamp] $line${NC}"
        fi
    else
        echo -e "${NC}[$timestamp] $line${NC}"
    fi
}

# Main monitoring function
start_monitoring() {
    echo -e "${GREEN}Starting ADB log monitoring for multiple number detection...${NC}"
    echo -e "${YELLOW}Press Ctrl+C to stop monitoring${NC}"
    echo ""
    
    # Build the grep pattern for filtering
    local grep_pattern="DEBUG.*(_parseWithMLKit|_handleAmountConfirmation|_consolidateCandidates|_detectAmountAmbiguityFromCandidates|completeTransaction|Quick reply|Amount confirmation|ML Kit found|Raw finder|Consolidated|candidateTexts|candidateAmounts)|flutter.*DEBUG|I/flutter.*DEBUG"
    
    if [ -n "$FILTER_SCENARIO" ]; then
        echo -e "${BLUE}Filtering for scenario: '$FILTER_SCENARIO'${NC}"
        grep_pattern="$grep_pattern|$FILTER_SCENARIO"
    fi
    
    # Start monitoring with appropriate filters
    adb logcat | grep -E "$grep_pattern" | while IFS= read -r line; do
        process_log_line "$line"
    done
}

# Trap Ctrl+C to cleanup
cleanup() {
    echo ""
    echo -e "${YELLOW}Monitoring stopped.${NC}"
    if [ "$SAVE_LOGS" = true ]; then
        echo -e "${BLUE}Logs saved to: $LOG_FILE${NC}"
        echo -e "${BLUE}Log file size: $(wc -l < "$LOG_FILE") lines${NC}"
    fi
    exit 0
}

trap cleanup INT

# Display monitoring info
echo -e "${CYAN}=== Multiple Number Detection Debug Monitor ===${NC}"
echo -e "${CYAN}Monitoring patterns:${NC}"
echo "  • ML Kit entity extraction"
echo "  • Raw number finder results"
echo "  • Candidate consolidation"
echo "  • Ambiguity detection"
echo "  • Amount confirmation UI"
echo "  • User selection processing"
echo ""

if [ -n "$FILTER_SCENARIO" ]; then
    echo -e "${BLUE}Scenario filter: '$FILTER_SCENARIO'${NC}"
fi

if [ "$VERBOSE" = true ]; then
    echo -e "${YELLOW}Verbose mode: ON${NC}"
else
    echo -e "${YELLOW}Verbose mode: OFF (use -v to enable DEBUG messages)${NC}"
fi

echo ""

# Start the monitoring
start_monitoring
