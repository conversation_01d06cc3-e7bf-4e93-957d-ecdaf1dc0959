#!/bin/bash

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status messages
print_status() {
    echo -e "${YELLOW}[*] $1${NC}"
}

print_success() {
    echo -e "${GREEN}[✓] $1${NC}"
}

print_error() {
    echo -e "${RED}[✗] $1${NC}"
}

# Check if Flutter is installed
print_status "Checking Flutter installation..."
if ! command -v flutter &> /dev/null; then
    print_error "Flutter is not installed. Please install Flutter first."
    exit 1
fi
print_success "Flutter is installed"

# Check Flutter doctor
print_status "Running Flutter doctor..."
flutter doctor

# Check if any emulator is running
print_status "Checking for running emulator..."
if ! adb devices | grep -q "emulator"; then
    print_error "No emulator is running. Please start an emulator first."
    print_status "Available emulators:"
    flutter emulators
    print_status "You can start an emulator using: flutter emulators --launch <emulator_id>"
    exit 1
fi
print_success "Emulator is running"

# Clean the project
print_status "Cleaning the project..."
flutter clean
print_success "Project cleaned"

# Get dependencies
print_status "Getting dependencies..."
flutter pub get
print_success "Dependencies updated"

# Build and install the app
print_status "Building and installing the app..."
flutter run --release

print_success "Build and installation completed!" 