import 'lib/services/parser/learned_category_storage.dart';
import 'test/mocks/mock_storage_service.dart';

void main() async {
  final mockStorage = MockStorageService();
  final learnedStorage = LearnedCategoryStorage(mockStorage);
  
  // Test the specific failing case
  const text = 'Café! @#\$ Coffee & More...';
  const categoryId = 'food';
  
  await learnedStorage.saveLearnedCategory(text, categoryId);
  
  // Check what was actually stored
  final allData = await learnedStorage.getAllLearnedCategories();
  print('Stored data: $allData');
  
  // Try to lookup
  final result = await learnedStorage.getLearnedCategory('cafe coffee more');
  print('Lookup result: $result');
}
