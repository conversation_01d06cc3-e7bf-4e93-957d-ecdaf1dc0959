# DreamFlow (Money Lover Chat)

A Flutter application for managing finances with transaction tracking, categorization, statistics, and a chat interface for transaction entry.

**Project Name**: DreamFlow
**Display Name**: Money Lover Chat
**Version**: 1.0.0

## Core Features

- **Transaction Management**: Create, edit, and delete transactions with support for expenses, income, and loans
- **Smart Parsing**: Hybrid ML Kit + regex parsing system with learning capabilities
- **Chat Interface**: Natural language transaction entry with "soft fail" user experience
- **Categorization**: Automatic and manual transaction categorization with learning
- **Statistics**: Interactive charts and financial insights using fl_chart
- **Localization**: Multi-language support (English, Spanish) with localized parsing
- **Multimedia**: Support for audio, image, video, and file attachments
- **Theme Customization**: Dark/light theme support with persistent preferences

## Getting Started

### Requirements

- Flutter 3.0.0 or higher
- Dart SDK 3.0.0 or higher

### Setup

1. Clone the repository:
```bash
git clone <repository-url>
```

2. Install dependencies:
```bash
flutter pub get
```

3. Run the app:
```bash
flutter run
```

## Project Structure

- `lib/` - Main application code
  - `models/` - Data models (Transaction, ParseResult, LocalizationData, etc.)
  - `services/` - Business logic and data services
    - `parser/` - Hybrid ML parsing system with learning capabilities
  - `screens/` - UI screens (Chat, Statistics, Categories, Settings)
  - `widgets/` - Reusable UI components
  - `utils/` - Utility functions for currency, amounts, and parsing
  - `navigation/` - Navigation logic
  - `*.dart` - Multimedia support files (audio, image, video, file handling)

## Documentation

Detailed documentation is available in the `docs/` directory:

- [Documentation Index](docs/index.md) - Complete documentation overview
- [Current Implementation Status](docs/current_implementation_status.md) - Current feature status and known issues
- [Codebase Structure](docs/codebase.md) - Detailed explanation of project organization
- [Architecture](docs/architecture.md) - Application architecture and design patterns
- [Setup Guide](docs/setup_guide.md) - Detailed environment setup
- [Feature Guides](docs/feature_guides/) - Detailed guides for chat transactions and statistics
- [Architectural Decisions](docs/decisions.md) - Record of key architectural decisions

## Testing

```bash
flutter test
```

## Contributing

For contribution guidelines, please see [CONTRIBUTING.md](CONTRIBUTING.md). 